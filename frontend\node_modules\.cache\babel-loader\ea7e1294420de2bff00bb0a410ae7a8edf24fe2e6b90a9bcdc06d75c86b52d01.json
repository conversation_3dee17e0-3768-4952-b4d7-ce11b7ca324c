{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents\\\\Programing\\\\TRO\\\\ModelTestsWorkbench\\\\frontend\\\\src\\\\pages\\\\boundingbox\\\\RankPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useMemo } from 'react';\nimport { Container, Typography, List, ListItem, ListItemText, ListItemIcon, Paper, CircularProgress, Alert, Box } from '@mui/material';\nimport { getBbRankData } from '../../services/api_bounding_box';\n\n// Helper to format rank number\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RankIcon = ({\n  rank\n}) => /*#__PURE__*/_jsxDEV(Box, {\n  sx: {\n    width: 30,\n    height: 30,\n    borderRadius: '50%',\n    backgroundColor: 'primary.main',\n    color: 'white',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    fontWeight: 'bold'\n  },\n  children: rank\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 18,\n  columnNumber: 3\n}, this);\n_c = RankIcon;\nconst RankPage = () => {\n  _s();\n  const [rankedModels, setRankedModels] = useState([]);\n  const [isLoading, setIsLoading] = useState(true); // Start with loading true\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    const calculateAndSetRankings = async () => {\n      setIsLoading(true);\n      setError(null);\n      try {\n        const {\n          models: allModels,\n          experiments: allExperiments\n        } = await getBbRankData();\n        if (!allModels || !allExperiments) {\n          setRankedModels([]);\n          setError(\"Could not retrieve all necessary data for ranking.\");\n          return;\n        }\n        const modelScores = {}; // { modelId: { totalScore: X, count: Y, name: Z, id: ID } }\n\n        allModels.forEach(model => {\n          modelScores[model.id] = {\n            totalScore: 0,\n            count: 0,\n            name: model.name,\n            id: model.id\n          };\n        });\n        allExperiments.forEach(experiment => {\n          if (experiment.results && Array.isArray(experiment.results)) {\n            experiment.results.forEach(result => {\n              if (result.model_id && result.score !== null && result.score !== undefined && result.status === 'success') {\n                if (modelScores[result.model_id]) {\n                  modelScores[result.model_id].totalScore += result.score;\n                  modelScores[result.model_id].count += 1;\n                }\n              }\n            });\n          }\n        });\n        const calculatedRankings = Object.values(modelScores).filter(model => model.count > 0) // Only include models with at least one rating\n        .map(model => ({\n          ...model,\n          averageScore: model.totalScore / model.count\n        })).sort((a, b) => b.averageScore - a.averageScore); // Sort descending by average score\n\n        setRankedModels(calculatedRankings);\n      } catch (err) {\n        setError('Failed to load or process ranking data.');\n        console.error(\"Error in ranking calculation:\", err);\n        setRankedModels([]);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n    calculateAndSetRankings();\n  }, []);\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"md\",\n      sx: {\n        mt: 4,\n        mb: 4,\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          mt: 1\n        },\n        children: \"Loading model ranking...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"md\",\n    sx: {\n      mt: 4,\n      mb: 4\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      component: \"h1\",\n      children: \"Model Ranking (Bounding Box)\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 17\n    }, this), !isLoading && !error && rankedModels.length === 0 && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      children: \"No models have been rated yet or no data available for ranking.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 9\n    }, this), rankedModels.length > 0 && /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 2,\n      children: /*#__PURE__*/_jsxDEV(List, {\n        children: rankedModels.map((model, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n          divider: true,\n          children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n            children: /*#__PURE__*/_jsxDEV(RankIcon, {\n              rank: index + 1\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n            primary: model.name,\n            secondary: `Average Score: ${model.averageScore ? model.averageScore.toFixed(2) : 'N/A'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 17\n          }, this)]\n        }, model.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 97,\n    columnNumber: 5\n  }, this);\n};\n_s(RankPage, \"HTaNDHdU+u/Dky4BarcHvQM4o0I=\");\n_c2 = RankPage;\nexport default RankPage;\nvar _c, _c2;\n$RefreshReg$(_c, \"RankIcon\");\n$RefreshReg$(_c2, \"RankPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useMemo", "Container", "Typography", "List", "ListItem", "ListItemText", "ListItemIcon", "Paper", "CircularProgress", "<PERSON><PERSON>", "Box", "getBbRankData", "jsxDEV", "_jsxDEV", "RankIcon", "rank", "sx", "width", "height", "borderRadius", "backgroundColor", "color", "display", "alignItems", "justifyContent", "fontWeight", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "RankPage", "_s", "rankedModels", "setRankedModels", "isLoading", "setIsLoading", "error", "setError", "calculateAndSetRankings", "models", "allModels", "experiments", "allExperiments", "modelScores", "for<PERSON>ach", "model", "id", "totalScore", "count", "name", "experiment", "results", "Array", "isArray", "result", "model_id", "score", "undefined", "status", "calculatedRankings", "Object", "values", "filter", "map", "averageScore", "sort", "a", "b", "err", "console", "max<PERSON><PERSON><PERSON>", "mt", "mb", "textAlign", "variant", "gutterBottom", "component", "severity", "length", "elevation", "index", "divider", "primary", "secondary", "toFixed", "_c2", "$RefreshReg$"], "sources": ["D:/Documents/Programing/TRO/ModelTestsWorkbench/frontend/src/pages/boundingbox/RankPage.js"], "sourcesContent": ["import React, { useState, useEffect, useMemo } from 'react';\r\nimport {\r\n  Container,\r\n  Typography,\r\n  List,\r\n  ListItem,\r\n  ListItemText,\r\n  ListItemIcon,\r\n  Paper,\r\n  CircularProgress,\r\n  Alert,\r\n  Box\r\n} from '@mui/material';\r\nimport { getBbRankData } from '../../services/api_bounding_box';\r\n\r\n// Helper to format rank number\r\nconst RankIcon = ({ rank }) => (\r\n  <Box sx={{\r\n    width: 30, height: 30, borderRadius: '50%', backgroundColor: 'primary.main',\r\n    color: 'white', display: 'flex', alignItems: 'center', justifyContent: 'center',\r\n    fontWeight: 'bold'\r\n  }}>\r\n    {rank}\r\n  </Box>\r\n);\r\n\r\nconst RankPage = () => {\r\n  const [rankedModels, setRankedModels] = useState([]);\r\n  const [isLoading, setIsLoading] = useState(true); // Start with loading true\r\n  const [error, setError] = useState(null);\r\n\r\n  useEffect(() => {\r\n    const calculateAndSetRankings = async () => {\r\n      setIsLoading(true);\r\n      setError(null);\r\n      try {\r\n        const { models: allModels, experiments: allExperiments } = await getBbRankData();\r\n\r\n        if (!allModels || !allExperiments) {\r\n          setRankedModels([]);\r\n          setError(\"Could not retrieve all necessary data for ranking.\");\r\n          return;\r\n        }\r\n\r\n        const modelScores = {}; // { modelId: { totalScore: X, count: Y, name: Z, id: ID } }\r\n\r\n        allModels.forEach(model => {\r\n          modelScores[model.id] = { totalScore: 0, count: 0, name: model.name, id: model.id };\r\n        });\r\n\r\n        allExperiments.forEach(experiment => {\r\n          if (experiment.results && Array.isArray(experiment.results)) {\r\n            experiment.results.forEach(result => {\r\n              if (result.model_id && result.score !== null && result.score !== undefined && result.status === 'success') {\r\n                if (modelScores[result.model_id]) {\r\n                  modelScores[result.model_id].totalScore += result.score;\r\n                  modelScores[result.model_id].count += 1;\r\n                }\r\n              }\r\n            });\r\n          }\r\n        });\r\n\r\n        const calculatedRankings = Object.values(modelScores)\r\n          .filter(model => model.count > 0) // Only include models with at least one rating\r\n          .map(model => ({\r\n            ...model,\r\n            averageScore: model.totalScore / model.count,\r\n          }))\r\n          .sort((a, b) => b.averageScore - a.averageScore); // Sort descending by average score\r\n\r\n        setRankedModels(calculatedRankings);\r\n\r\n      } catch (err) {\r\n        setError('Failed to load or process ranking data.');\r\n        console.error(\"Error in ranking calculation:\", err);\r\n        setRankedModels([]);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    calculateAndSetRankings();\r\n  }, []);\r\n\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <Container maxWidth=\"md\" sx={{ mt: 4, mb: 4, textAlign: 'center' }}>\r\n        <CircularProgress />\r\n        <Typography sx={{ mt: 1 }}>Loading model ranking...</Typography>\r\n      </Container>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Container maxWidth=\"md\" sx={{ mt: 4, mb: 4 }}>\r\n      <Typography variant=\"h4\" gutterBottom component=\"h1\">\r\n        Model Ranking (Bounding Box)\r\n      </Typography>\r\n\r\n      {error && <Alert severity=\"error\" sx={{ mb: 2 }}>{error}</Alert>}\r\n\r\n      {!isLoading && !error && rankedModels.length === 0 && (\r\n        <Alert severity=\"info\">No models have been rated yet or no data available for ranking.</Alert>\r\n      )}\r\n\r\n      {rankedModels.length > 0 && (\r\n        <Paper elevation={2}>\r\n          <List>\r\n            {rankedModels.map((model, index) => (\r\n              <ListItem key={model.id} divider>\r\n                <ListItemIcon>\r\n                  <RankIcon rank={index + 1} />\r\n                </ListItemIcon>\r\n                <ListItemText\r\n                  primary={model.name}\r\n                  secondary={`Average Score: ${model.averageScore ? model.averageScore.toFixed(2) : 'N/A'}`}\r\n                />\r\n              </ListItem>\r\n            ))}\r\n          </List>\r\n        </Paper>\r\n      )}\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default RankPage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,OAAO,QAAQ,OAAO;AAC3D,SACEC,SAAS,EACTC,UAAU,EACVC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,KAAK,EACLC,gBAAgB,EAChBC,KAAK,EACLC,GAAG,QACE,eAAe;AACtB,SAASC,aAAa,QAAQ,iCAAiC;;AAE/D;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,QAAQ,GAAGA,CAAC;EAAEC;AAAK,CAAC,kBACxBF,OAAA,CAACH,GAAG;EAACM,EAAE,EAAE;IACPC,KAAK,EAAE,EAAE;IAAEC,MAAM,EAAE,EAAE;IAAEC,YAAY,EAAE,KAAK;IAAEC,eAAe,EAAE,cAAc;IAC3EC,KAAK,EAAE,OAAO;IAAEC,OAAO,EAAE,MAAM;IAAEC,UAAU,EAAE,QAAQ;IAAEC,cAAc,EAAE,QAAQ;IAC/EC,UAAU,EAAE;EACd,CAAE;EAAAC,QAAA,EACCX;AAAI;EAAAY,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACF,CACN;AAACC,EAAA,GARIjB,QAAQ;AAUd,MAAMkB,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACsC,SAAS,EAAEC,YAAY,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAClD,MAAM,CAACwC,KAAK,EAAEC,QAAQ,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;EAExCC,SAAS,CAAC,MAAM;IACd,MAAMyC,uBAAuB,GAAG,MAAAA,CAAA,KAAY;MAC1CH,YAAY,CAAC,IAAI,CAAC;MAClBE,QAAQ,CAAC,IAAI,CAAC;MACd,IAAI;QACF,MAAM;UAAEE,MAAM,EAAEC,SAAS;UAAEC,WAAW,EAAEC;QAAe,CAAC,GAAG,MAAMjC,aAAa,CAAC,CAAC;QAEhF,IAAI,CAAC+B,SAAS,IAAI,CAACE,cAAc,EAAE;UACjCT,eAAe,CAAC,EAAE,CAAC;UACnBI,QAAQ,CAAC,oDAAoD,CAAC;UAC9D;QACF;QAEA,MAAMM,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC;;QAExBH,SAAS,CAACI,OAAO,CAACC,KAAK,IAAI;UACzBF,WAAW,CAACE,KAAK,CAACC,EAAE,CAAC,GAAG;YAAEC,UAAU,EAAE,CAAC;YAAEC,KAAK,EAAE,CAAC;YAAEC,IAAI,EAAEJ,KAAK,CAACI,IAAI;YAAEH,EAAE,EAAED,KAAK,CAACC;UAAG,CAAC;QACrF,CAAC,CAAC;QAEFJ,cAAc,CAACE,OAAO,CAACM,UAAU,IAAI;UACnC,IAAIA,UAAU,CAACC,OAAO,IAAIC,KAAK,CAACC,OAAO,CAACH,UAAU,CAACC,OAAO,CAAC,EAAE;YAC3DD,UAAU,CAACC,OAAO,CAACP,OAAO,CAACU,MAAM,IAAI;cACnC,IAAIA,MAAM,CAACC,QAAQ,IAAID,MAAM,CAACE,KAAK,KAAK,IAAI,IAAIF,MAAM,CAACE,KAAK,KAAKC,SAAS,IAAIH,MAAM,CAACI,MAAM,KAAK,SAAS,EAAE;gBACzG,IAAIf,WAAW,CAACW,MAAM,CAACC,QAAQ,CAAC,EAAE;kBAChCZ,WAAW,CAACW,MAAM,CAACC,QAAQ,CAAC,CAACR,UAAU,IAAIO,MAAM,CAACE,KAAK;kBACvDb,WAAW,CAACW,MAAM,CAACC,QAAQ,CAAC,CAACP,KAAK,IAAI,CAAC;gBACzC;cACF;YACF,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;QAEF,MAAMW,kBAAkB,GAAGC,MAAM,CAACC,MAAM,CAAClB,WAAW,CAAC,CAClDmB,MAAM,CAACjB,KAAK,IAAIA,KAAK,CAACG,KAAK,GAAG,CAAC,CAAC,CAAC;QAAA,CACjCe,GAAG,CAAClB,KAAK,KAAK;UACb,GAAGA,KAAK;UACRmB,YAAY,EAAEnB,KAAK,CAACE,UAAU,GAAGF,KAAK,CAACG;QACzC,CAAC,CAAC,CAAC,CACFiB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACH,YAAY,GAAGE,CAAC,CAACF,YAAY,CAAC,CAAC,CAAC;;QAEpD/B,eAAe,CAAC0B,kBAAkB,CAAC;MAErC,CAAC,CAAC,OAAOS,GAAG,EAAE;QACZ/B,QAAQ,CAAC,yCAAyC,CAAC;QACnDgC,OAAO,CAACjC,KAAK,CAAC,+BAA+B,EAAEgC,GAAG,CAAC;QACnDnC,eAAe,CAAC,EAAE,CAAC;MACrB,CAAC,SAAS;QACRE,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC;IAEDG,uBAAuB,CAAC,CAAC;EAC3B,CAAC,EAAE,EAAE,CAAC;EAGN,IAAIJ,SAAS,EAAE;IACb,oBACEvB,OAAA,CAACZ,SAAS;MAACuE,QAAQ,EAAC,IAAI;MAACxD,EAAE,EAAE;QAAEyD,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAjD,QAAA,gBACjEb,OAAA,CAACL,gBAAgB;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpBjB,OAAA,CAACX,UAAU;QAACc,EAAE,EAAE;UAAEyD,EAAE,EAAE;QAAE,CAAE;QAAA/C,QAAA,EAAC;MAAwB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvD,CAAC;EAEhB;EAEA,oBACEjB,OAAA,CAACZ,SAAS;IAACuE,QAAQ,EAAC,IAAI;IAACxD,EAAE,EAAE;MAAEyD,EAAE,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAhD,QAAA,gBAC5Cb,OAAA,CAACX,UAAU;MAAC0E,OAAO,EAAC,IAAI;MAACC,YAAY;MAACC,SAAS,EAAC,IAAI;MAAApD,QAAA,EAAC;IAErD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,EAEZQ,KAAK,iBAAIzB,OAAA,CAACJ,KAAK;MAACsE,QAAQ,EAAC,OAAO;MAAC/D,EAAE,EAAE;QAAE0D,EAAE,EAAE;MAAE,CAAE;MAAAhD,QAAA,EAAEY;IAAK;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,EAE/D,CAACM,SAAS,IAAI,CAACE,KAAK,IAAIJ,YAAY,CAAC8C,MAAM,KAAK,CAAC,iBAChDnE,OAAA,CAACJ,KAAK;MAACsE,QAAQ,EAAC,MAAM;MAAArD,QAAA,EAAC;IAA+D;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAC9F,EAEAI,YAAY,CAAC8C,MAAM,GAAG,CAAC,iBACtBnE,OAAA,CAACN,KAAK;MAAC0E,SAAS,EAAE,CAAE;MAAAvD,QAAA,eAClBb,OAAA,CAACV,IAAI;QAAAuB,QAAA,EACFQ,YAAY,CAAC+B,GAAG,CAAC,CAAClB,KAAK,EAAEmC,KAAK,kBAC7BrE,OAAA,CAACT,QAAQ;UAAgB+E,OAAO;UAAAzD,QAAA,gBAC9Bb,OAAA,CAACP,YAAY;YAAAoB,QAAA,eACXb,OAAA,CAACC,QAAQ;cAACC,IAAI,EAAEmE,KAAK,GAAG;YAAE;cAAAvD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACfjB,OAAA,CAACR,YAAY;YACX+E,OAAO,EAAErC,KAAK,CAACI,IAAK;YACpBkC,SAAS,EAAE,kBAAkBtC,KAAK,CAACmB,YAAY,GAAGnB,KAAK,CAACmB,YAAY,CAACoB,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK;UAAG;YAAA3D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F,CAAC;QAAA,GAPWiB,KAAK,CAACC,EAAE;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQb,CACX;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAEhB,CAAC;AAACG,EAAA,CApGID,QAAQ;AAAAuD,GAAA,GAARvD,QAAQ;AAsGd,eAAeA,QAAQ;AAAC,IAAAD,EAAA,EAAAwD,GAAA;AAAAC,YAAA,CAAAzD,EAAA;AAAAyD,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}