{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents\\\\Programing\\\\TRO\\\\ModelTestsWorkbench\\\\frontend\\\\src\\\\pages\\\\patent-viz\\\\PatentDashboardPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Container, Typography, Button, Grid, CircularProgress, Alert, Box } from '@mui/material';\nimport { getPatentDashboardStatistics } from '../../services/api_patent_viz';\nimport StatisticsSection from '../../components/patent-viz/StatisticsSection'; // Will be created next\n\n// Helper function (can be moved to a utils file)\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst formatTimestamp = isoString => {\n  if (!isoString) return 'N/A';\n  try {\n    return new Date(isoString).toLocaleString();\n  } catch (e) {\n    return 'Invalid Date';\n  }\n};\n\n// TDD: TEST: PatentDashboardPage fetches data on mount\n// TDD: TEST: PatentDashboardPage displays loading state while fetching\n// TDD: TEST: PatentDashboardPage displays error message on fetch failure\n// TDD: TEST: PatentDashboardPage renders StatisticsSection for overall and TRO-specific data\n// TDD: TEST: PatentDashboardPage Refresh button calls API with refresh=true\nconst PatentDashboardPage = () => {\n  _s();\n  const [dashboardData, setDashboardData] = useState(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const fetchDashboardData = useCallback(async (refresh = false) => {\n    setIsLoading(true);\n    setError(null);\n    try {\n      // TDD: TEST: API_CLIENT.get is called with correct URL and refresh param (covered by getPatentDashboardStatistics)\n      const response = await getPatentDashboardStatistics(refresh);\n      setDashboardData(response.data);\n    } catch (apiError) {\n      setError(apiError.message || 'Failed to fetch dashboard data.');\n      console.error(\"Error fetching dashboard data:\", apiError);\n    } finally {\n      setIsLoading(false);\n    }\n  }, []);\n  useEffect(() => {\n    fetchDashboardData(false);\n  }, [fetchDashboardData]);\n  const handleRefreshClick = () => {\n    fetchDashboardData(true);\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"lg\",\n    sx: {\n      mt: 4,\n      mb: 4\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      component: \"h1\",\n      children: \"Patent Statistics Dashboard\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"space-between\",\n      alignItems: \"center\",\n      mb: 2,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        children: dashboardData && dashboardData.last_updated_timestamp ? `Last Updated: ${formatTimestamp(dashboardData.last_updated_timestamp)} (Cache: ${dashboardData.cache_status || 'N/A'})` : 'Loading update time...'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: handleRefreshClick,\n        disabled: isLoading,\n        children: isLoading ? 'Refreshing...' : 'Refresh Data'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this), isLoading && !dashboardData &&\n    /*#__PURE__*/\n    // Show main loader only if no data yet\n    _jsxDEV(Box, {\n      display: \"flex\",\n      justifyContent: \"center\",\n      alignItems: \"center\",\n      minHeight: \"400px\",\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 9\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mt: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 9\n    }, this), dashboardData && !isLoading && !error &&\n    /*#__PURE__*/\n    // Render sections if data is available, not loading, and no error\n    _jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          component: \"h2\",\n          children: \"Overall Database Statistics\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(StatisticsSection, {\n          statsData: dashboardData.overall_statistics\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          gutterBottom: true,\n          component: \"h2\",\n          children: \"TRO-Specific Statistics (TRO = True)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(StatisticsSection, {\n          statsData: dashboardData.tro_specific_statistics\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 5\n  }, this);\n};\n_s(PatentDashboardPage, \"XjW3xygmWZLzrd6qB32vs/k5XYw=\");\n_c = PatentDashboardPage;\nexport default PatentDashboardPage;\nvar _c;\n$RefreshReg$(_c, \"PatentDashboardPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Container", "Typography", "<PERSON><PERSON>", "Grid", "CircularProgress", "<PERSON><PERSON>", "Box", "getPatentDashboardStatistics", "StatisticsSection", "jsxDEV", "_jsxDEV", "formatTimestamp", "isoString", "Date", "toLocaleString", "e", "PatentDashboardPage", "_s", "dashboardData", "setDashboardData", "isLoading", "setIsLoading", "error", "setError", "fetchDashboardData", "refresh", "response", "data", "apiError", "message", "console", "handleRefreshClick", "max<PERSON><PERSON><PERSON>", "sx", "mt", "mb", "children", "variant", "gutterBottom", "component", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "justifyContent", "alignItems", "last_updated_timestamp", "cache_status", "onClick", "disabled", "minHeight", "severity", "container", "spacing", "item", "xs", "statsData", "overall_statistics", "tro_specific_statistics", "_c", "$RefreshReg$"], "sources": ["D:/Documents/Programing/TRO/ModelTestsWorkbench/frontend/src/pages/patent-viz/PatentDashboardPage.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\r\nimport {\r\n  Container,\r\n  Typography,\r\n  Button,\r\n  Grid,\r\n  CircularProgress,\r\n  Alert,\r\n  Box,\r\n} from '@mui/material';\r\nimport { getPatentDashboardStatistics } from '../../services/api_patent_viz';\r\nimport StatisticsSection from '../../components/patent-viz/StatisticsSection'; // Will be created next\r\n\r\n// Helper function (can be moved to a utils file)\r\nconst formatTimestamp = (isoString) => {\r\n  if (!isoString) return 'N/A';\r\n  try {\r\n    return new Date(isoString).toLocaleString();\r\n  } catch (e) {\r\n    return 'Invalid Date';\r\n  }\r\n};\r\n\r\n// TDD: TEST: PatentDashboardPage fetches data on mount\r\n// TDD: TEST: PatentDashboardPage displays loading state while fetching\r\n// TDD: TEST: PatentDashboardPage displays error message on fetch failure\r\n// TDD: TEST: PatentDashboardPage renders StatisticsSection for overall and TRO-specific data\r\n// TDD: TEST: PatentDashboardPage Refresh button calls API with refresh=true\r\nconst PatentDashboardPage = () => {\r\n  const [dashboardData, setDashboardData] = useState(null);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n\r\n  const fetchDashboardData = useCallback(async (refresh = false) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      // TDD: TEST: API_CLIENT.get is called with correct URL and refresh param (covered by getPatentDashboardStatistics)\r\n      const response = await getPatentDashboardStatistics(refresh);\r\n      setDashboardData(response.data);\r\n    } catch (apiError) {\r\n      setError(apiError.message || 'Failed to fetch dashboard data.');\r\n      console.error(\"Error fetching dashboard data:\", apiError);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    fetchDashboardData(false);\r\n  }, [fetchDashboardData]);\r\n\r\n  const handleRefreshClick = () => {\r\n    fetchDashboardData(true);\r\n  };\r\n\r\n  return (\r\n    <Container maxWidth=\"lg\" sx={{ mt: 4, mb: 4 }}>\r\n      <Typography variant=\"h4\" gutterBottom component=\"h1\">\r\n        Patent Statistics Dashboard\r\n      </Typography>\r\n      <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\" mb={2}>\r\n        <Typography variant=\"caption\">\r\n          {dashboardData && dashboardData.last_updated_timestamp\r\n            ? `Last Updated: ${formatTimestamp(dashboardData.last_updated_timestamp)} (Cache: ${dashboardData.cache_status || 'N/A'})`\r\n            : 'Loading update time...'}\r\n        </Typography>\r\n        <Button variant=\"contained\" onClick={handleRefreshClick} disabled={isLoading}>\r\n          {isLoading ? 'Refreshing...' : 'Refresh Data'}\r\n        </Button>\r\n      </Box>\r\n\r\n      {isLoading && !dashboardData && ( // Show main loader only if no data yet\r\n        <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"400px\">\r\n          <CircularProgress />\r\n        </Box>\r\n      )}\r\n\r\n      {error && (\r\n        <Alert severity=\"error\" sx={{ mt: 2 }}>{error}</Alert>\r\n      )}\r\n\r\n      {dashboardData && !isLoading && !error && ( // Render sections if data is available, not loading, and no error\r\n        <Grid container spacing={3}>\r\n          <Grid item xs={12}>\r\n            <Typography variant=\"h5\" gutterBottom component=\"h2\">\r\n              Overall Database Statistics\r\n            </Typography>\r\n            {/* TDD: TEST: StatisticsSection for overall_statistics receives correct props */}\r\n            <StatisticsSection\r\n              statsData={dashboardData.overall_statistics}\r\n            />\r\n          </Grid>\r\n\r\n          <Grid item xs={12}>\r\n            <Typography variant=\"h5\" gutterBottom component=\"h2\">\r\n              TRO-Specific Statistics (TRO = True)\r\n            </Typography>\r\n            {/* TDD: TEST: StatisticsSection for tro_specific_statistics receives correct props */}\r\n            <StatisticsSection\r\n              statsData={dashboardData.tro_specific_statistics}\r\n            />\r\n          </Grid>\r\n        </Grid>\r\n      )}\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default PatentDashboardPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SACEC,SAAS,EACTC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,gBAAgB,EAChBC,KAAK,EACLC,GAAG,QACE,eAAe;AACtB,SAASC,4BAA4B,QAAQ,+BAA+B;AAC5E,OAAOC,iBAAiB,MAAM,+CAA+C,CAAC,CAAC;;AAE/E;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,eAAe,GAAIC,SAAS,IAAK;EACrC,IAAI,CAACA,SAAS,EAAE,OAAO,KAAK;EAC5B,IAAI;IACF,OAAO,IAAIC,IAAI,CAACD,SAAS,CAAC,CAACE,cAAc,CAAC,CAAC;EAC7C,CAAC,CAAC,OAAOC,CAAC,EAAE;IACV,OAAO,cAAc;EACvB;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACuB,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACyB,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAExC,MAAM2B,kBAAkB,GAAGzB,WAAW,CAAC,OAAO0B,OAAO,GAAG,KAAK,KAAK;IAChEJ,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IACd,IAAI;MACF;MACA,MAAMG,QAAQ,GAAG,MAAMnB,4BAA4B,CAACkB,OAAO,CAAC;MAC5DN,gBAAgB,CAACO,QAAQ,CAACC,IAAI,CAAC;IACjC,CAAC,CAAC,OAAOC,QAAQ,EAAE;MACjBL,QAAQ,CAACK,QAAQ,CAACC,OAAO,IAAI,iCAAiC,CAAC;MAC/DC,OAAO,CAACR,KAAK,CAAC,gCAAgC,EAAEM,QAAQ,CAAC;IAC3D,CAAC,SAAS;MACRP,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,EAAE,CAAC;EAENvB,SAAS,CAAC,MAAM;IACd0B,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC,EAAE,CAACA,kBAAkB,CAAC,CAAC;EAExB,MAAMO,kBAAkB,GAAGA,CAAA,KAAM;IAC/BP,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,oBACEd,OAAA,CAACV,SAAS;IAACgC,QAAQ,EAAC,IAAI;IAACC,EAAE,EAAE;MAAEC,EAAE,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAC5C1B,OAAA,CAACT,UAAU;MAACoC,OAAO,EAAC,IAAI;MAACC,YAAY;MAACC,SAAS,EAAC,IAAI;MAAAH,QAAA,EAAC;IAErD;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACbjC,OAAA,CAACJ,GAAG;MAACsC,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,eAAe;MAACC,UAAU,EAAC,QAAQ;MAACX,EAAE,EAAE,CAAE;MAAAC,QAAA,gBAC3E1B,OAAA,CAACT,UAAU;QAACoC,OAAO,EAAC,SAAS;QAAAD,QAAA,EAC1BlB,aAAa,IAAIA,aAAa,CAAC6B,sBAAsB,GAClD,iBAAiBpC,eAAe,CAACO,aAAa,CAAC6B,sBAAsB,CAAC,YAAY7B,aAAa,CAAC8B,YAAY,IAAI,KAAK,GAAG,GACxH;MAAwB;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,eACbjC,OAAA,CAACR,MAAM;QAACmC,OAAO,EAAC,WAAW;QAACY,OAAO,EAAElB,kBAAmB;QAACmB,QAAQ,EAAE9B,SAAU;QAAAgB,QAAA,EAC1EhB,SAAS,GAAG,eAAe,GAAG;MAAc;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAELvB,SAAS,IAAI,CAACF,aAAa;IAAA;IAAM;IAChCR,OAAA,CAACJ,GAAG;MAACsC,OAAO,EAAC,MAAM;MAACC,cAAc,EAAC,QAAQ;MAACC,UAAU,EAAC,QAAQ;MAACK,SAAS,EAAC,OAAO;MAAAf,QAAA,eAC/E1B,OAAA,CAACN,gBAAgB;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACN,EAEArB,KAAK,iBACJZ,OAAA,CAACL,KAAK;MAAC+C,QAAQ,EAAC,OAAO;MAACnB,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAE,QAAA,EAAEd;IAAK;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CACtD,EAEAzB,aAAa,IAAI,CAACE,SAAS,IAAI,CAACE,KAAK;IAAA;IAAM;IAC1CZ,OAAA,CAACP,IAAI;MAACkD,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAlB,QAAA,gBACzB1B,OAAA,CAACP,IAAI;QAACoD,IAAI;QAACC,EAAE,EAAE,EAAG;QAAApB,QAAA,gBAChB1B,OAAA,CAACT,UAAU;UAACoC,OAAO,EAAC,IAAI;UAACC,YAAY;UAACC,SAAS,EAAC,IAAI;UAAAH,QAAA,EAAC;QAErD;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbjC,OAAA,CAACF,iBAAiB;UAChBiD,SAAS,EAAEvC,aAAa,CAACwC;QAAmB;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEPjC,OAAA,CAACP,IAAI;QAACoD,IAAI;QAACC,EAAE,EAAE,EAAG;QAAApB,QAAA,gBAChB1B,OAAA,CAACT,UAAU;UAACoC,OAAO,EAAC,IAAI;UAACC,YAAY;UAACC,SAAS,EAAC,IAAI;UAAAH,QAAA,EAAC;QAErD;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbjC,OAAA,CAACF,iBAAiB;UAChBiD,SAAS,EAAEvC,aAAa,CAACyC;QAAwB;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAEhB,CAAC;AAAC1B,EAAA,CA/EID,mBAAmB;AAAA4C,EAAA,GAAnB5C,mBAAmB;AAiFzB,eAAeA,mBAAmB;AAAC,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}