import os, sys, logging
sys.path.append(os.getcwd())
from flask import Flask
from dotenv import load_dotenv
from backend.models.registration import sync_models_from_config
from backend.celery_app import celery # Import Celery instance
from backend.extensions import db # Import db from extensions
from flask_cors import CORS # Import CORS
from flask_migrate import Migrate # Import Flask-Migrate

# Load environment variables from .env file
load_dotenv()

# Initialize SQLAlchemy extension

def create_app():
    """Create and configure an instance of the Flask application."""
    app = Flask(__name__)

    # --- Configuration ---
    app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'default-secret-key-for-dev')
    # Construct the main database URI using individual environment variables
    db_user = os.getenv('POSTGRES_USER')
    db_password = os.getenv('POSTGRES_PASSWORD')
    db_host = os.getenv('POSTGRES_HOST')
    db_port = os.getenv('POSTGRES_PORT')
    db_name = os.getenv('POSTGRES_DB_MODEL_TEST_WORKBENCH') # Use the specific DB name for the main app

    if not all([db_user, db_password, db_host, db_port, db_name]):
        raise ValueError("One or more required PostgreSQL environment variables (POSTGRES_USER, POSTGRES_PASSWORD, POSTGRES_HOST, POSTGRES_PORT, POSTGRES_DB_MODEL_TEST_WORKBENCH) are not set.")

    app.config['SQLALCHEMY_DATABASE_URI'] = f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"

    # Construct PATENT_DB_URI for the second database
    PATENT_DB_URI = f"postgresql://{os.getenv('POSTGRES_USER')}:{os.getenv('POSTGRES_PASSWORD')}@{os.getenv('POSTGRES_HOST')}:{os.getenv('POSTGRES_PORT')}/{os.getenv('POSTGRES_DB_MAIDALV')}"
    if not all([os.getenv('POSTGRES_USER'), os.getenv('POSTGRES_PASSWORD'), os.getenv('POSTGRES_HOST'), os.getenv('POSTGRES_PORT'), os.getenv('POSTGRES_DB_MAIDALV')]):
        app.logger.warning("One or more environment variables for PATENT_DB_URI are not set. Patent database functionality may be affected.")
        # Decide if this should be a fatal error or just a warning
        # For now, we'll let it proceed with a warning, but patent_db might not connect.
        PATENT_DB_URI = None # Or some default/fallback if appropriate

    app.config['SQLALCHEMY_BINDS'] = {
        'patent_db': PATENT_DB_URI
    }
    app.logger.info(f"PATENT_DB_URI configured for 'patent_db' bind: {PATENT_DB_URI}")

    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SQLALCHEMY_POOL_RECYCLE'] = 280  # Recycle connections older than 280 seconds
    app.config['SQLALCHEMY_POOL_PRE_PING'] = True # Check connection validity before use
    # Celery config is loaded in celery_app.py and updated via init_celery

    # --- Logging ---
    logging.basicConfig(level=logging.INFO,
                        format='%(asctime)s %(levelname)s %(name)s %(threadName)s : %(message)s')
    app.logger.info("Flask app configured.")

    # --- Database Initialization ---
    db.init_app(app)
    Migrate(app, db, directory='backend/alembic') # Initialize Flask-Migrate
    app.logger.info("SQLAlchemy and Flask-Migrate initialized.")

    # --- CORS Configuration ---
    # Allow requests from the frontend development server
    CORS(app, resources={r"/api/*": {"origins": "http://localhost:3000"}})
    app.logger.info("Flask-CORS initialized, allowing origin http://localhost:3000 for /api/* routes.")

    # --- Blueprints ---
    # Import and register blueprints
    from .api.data_management import data_management_bp
    from .api.model_management import model_management_bp
    from .api.tasks import tasks_bp # Import tasks blueprint
    from .api.combined_scores import combined_scores_bp # Import combined scores blueprint
    from .api.results import results_bp # Import results blueprint
    from .api.dashboard import dashboard_bp # Import dashboard blueprint
    from .api.qdrant_management import qdrant_bp # Import Qdrant management blueprint
    from .api.patents_api import patents_api_bp # Import patents API blueprint
    from .api.bounding_box_api import bounding_box_bp # Import bounding box API blueprint
    app.register_blueprint(data_management_bp, url_prefix='/api/data')
    app.logger.info("Registered data_management blueprint.")
    app.register_blueprint(model_management_bp, url_prefix='/api/models')
    app.logger.info("Registered model_management blueprint.")
    app.register_blueprint(tasks_bp, url_prefix='/api/tasks') # Register tasks blueprint
    app.logger.info("Registered tasks blueprint.")
    app.register_blueprint(combined_scores_bp) # Uses url_prefix defined in blueprint
    app.logger.info("Registered combined_scores blueprint.")
    app.register_blueprint(results_bp) # Uses url_prefix defined in blueprint (/api/results)
    app.logger.info("Registered results blueprint.")
    app.register_blueprint(dashboard_bp) # Uses url_prefix defined in blueprint (/api/dashboard)
    app.logger.info("Registered dashboard blueprint.")
    app.register_blueprint(qdrant_bp) # Uses url_prefix defined in blueprint (/api/qdrant)
    app.logger.info("Registered qdrant_management blueprint.")
    app.register_blueprint(patents_api_bp) # Uses url_prefix defined in blueprint (/api/v1/patents)
    app.logger.info("Registered patents_api blueprint.")
    app.register_blueprint(bounding_box_bp) # Uses url_prefix defined in blueprint (/api/v1/boundingbox)
    app.logger.info("Registered bounding_box_api blueprint.")

    # --- Celery Initialization ---
    # Celery is configured in celery_app.py; no explicit init needed here for the Flask app itself.
    # The worker manages its own context.
    app.logger.info("Celery instance configured (worker manages its own context).")

    # --- Sync Models on Startup ---
    # Ensure this runs after extensions like DB and Celery are initialized
    with app.app_context():
        try:
            # Get a session within the app context
            session = db.session
            sync_models_from_config(session)
            app.logger.info("Model synchronization from config completed.")
        except Exception as e:
            app.logger.error(f"Error during initial model synchronization: {e}", exc_info=True)
            # Depending on severity, you might want to raise the error or just log it

    # --- Root Route (Optional) ---
    @app.route('/')
    def index():
        return "ModelTests Workbench Backend is running!"

    return app