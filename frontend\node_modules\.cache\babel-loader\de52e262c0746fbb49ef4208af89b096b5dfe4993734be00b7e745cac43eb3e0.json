{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents\\\\Programing\\\\TRO\\\\ModelTestsWorkbench\\\\frontend\\\\src\\\\components\\\\navigation\\\\PlatformSwitcher.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Select, MenuItem, FormControl, InputLabel } from '@mui/material';\nimport { useLocation, useNavigate } from 'react-router-dom';\n\n// TDD: TEST: PlatformSwitcher renders a dropdown menu\n// TDD: TEST: PlatformSwitcher lists all available platforms\n// TDD: TEST: PlatformSwitcher correctly identifies and highlights the current platform based on URL\n// TDD: TEST: PlatformSwitcher navigates to the correct base path when a new platform is selected\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst availablePlatforms = [{\n  name: \"ModelTestWorkbench\",\n  path_prefix: \"/\",\n  default_sub_route: \"/dashboard\"\n},\n// Or \"\" if root has no default sub-route\n{\n  name: \"Patent Visualization\",\n  path_prefix: \"/patent-viz\",\n  default_sub_route: \"/dashboard\"\n}, {\n  name: \"Bounding Box\",\n  path_prefix: \"/boundingbox\",\n  default_sub_route: \"\"\n}\n// Add other platforms here if needed\n];\nconst PlatformSwitcher = () => {\n  _s();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const getCurrentSelectedPlatform = () => {\n    for (const platform of availablePlatforms) {\n      // Handle the root path case more specifically\n      if (platform.path_prefix === \"/\" && location.pathname === \"/\") {\n        return platform;\n      }\n      // For other platforms, or if root path has sub-routes\n      if (platform.path_prefix !== \"/\" && location.pathname.startsWith(platform.path_prefix)) {\n        return platform;\n      }\n    }\n    // Fallback for root if no other platform matches, or if current path is a sub-route of root\n    const rootPlatform = availablePlatforms.find(p => p.path_prefix === \"/\");\n    if (rootPlatform && location.pathname.startsWith(rootPlatform.path_prefix)) {\n      return rootPlatform;\n    }\n    return availablePlatforms[0]; // Default to the first platform if no match\n  };\n  const selectedPlatform = getCurrentSelectedPlatform();\n  const selectedPlatformValue = selectedPlatform ? selectedPlatform.path_prefix : availablePlatforms[0].path_prefix;\n  const handlePlatformChange = event => {\n    const newPlatformPathPrefix = event.target.value;\n    const platformConfig = availablePlatforms.find(p => p.path_prefix === newPlatformPathPrefix);\n    if (platformConfig) {\n      navigate(platformConfig.path_prefix + (platformConfig.default_sub_route || \"\"));\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(FormControl, {\n    sx: {\n      m: 1,\n      minWidth: 120\n    },\n    size: \"small\",\n    children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n      id: \"platform-switcher-label\",\n      children: \"Platform\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Select, {\n      labelId: \"platform-switcher-label\",\n      id: \"platform-switcher-select\",\n      value: selectedPlatformValue,\n      label: \"Platform\",\n      onChange: handlePlatformChange,\n      sx: {\n        '.MuiSelect-select': {\n          color: 'inherit',\n          borderColor: 'inherit'\n        }\n      },\n      children: availablePlatforms.map(platform => /*#__PURE__*/_jsxDEV(MenuItem, {\n        value: platform.path_prefix,\n        children: platform.name\n      }, platform.path_prefix, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n};\n_s(PlatformSwitcher, \"ZmJpvzBBf8J7VCgSKqUUk0eHjAY=\", false, function () {\n  return [useLocation, useNavigate];\n});\n_c = PlatformSwitcher;\nexport default PlatformSwitcher;\nvar _c;\n$RefreshReg$(_c, \"PlatformSwitcher\");", "map": {"version": 3, "names": ["React", "Select", "MenuItem", "FormControl", "InputLabel", "useLocation", "useNavigate", "jsxDEV", "_jsxDEV", "availablePlatforms", "name", "path_prefix", "default_sub_route", "PlatformSwitcher", "_s", "location", "navigate", "getCurrentSelectedPlatform", "platform", "pathname", "startsWith", "rootPlatform", "find", "p", "selectedPlatform", "selectedPlatformValue", "handlePlatformChange", "event", "newPlatformPathPrefix", "target", "value", "platformConfig", "sx", "m", "min<PERSON><PERSON><PERSON>", "size", "children", "id", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "labelId", "label", "onChange", "color", "borderColor", "map", "_c", "$RefreshReg$"], "sources": ["D:/Documents/Programing/TRO/ModelTestsWorkbench/frontend/src/components/navigation/PlatformSwitcher.js"], "sourcesContent": ["import React from 'react';\r\nimport { Select, MenuItem, FormControl, InputLabel } from '@mui/material';\r\nimport { useLocation, useNavigate } from 'react-router-dom';\r\n\r\n// TDD: TEST: PlatformSwitcher renders a dropdown menu\r\n// TDD: TEST: PlatformSwitcher lists all available platforms\r\n// TDD: TEST: PlatformSwitcher correctly identifies and highlights the current platform based on URL\r\n// TDD: TEST: PlatformSwitcher navigates to the correct base path when a new platform is selected\r\n\r\nconst availablePlatforms = [\r\n  { name: \"ModelTestWorkbench\", path_prefix: \"/\", default_sub_route: \"/dashboard\" }, // Or \"\" if root has no default sub-route\r\n  { name: \"Patent Visualization\", path_prefix: \"/patent-viz\", default_sub_route: \"/dashboard\" },\r\n  { name: \"Bounding Box\", path_prefix: \"/boundingbox\", default_sub_route: \"\" }\r\n  // Add other platforms here if needed\r\n];\r\n\r\nconst PlatformSwitcher = () => {\r\n  const location = useLocation();\r\n  const navigate = useNavigate();\r\n\r\n  const getCurrentSelectedPlatform = () => {\r\n    for (const platform of availablePlatforms) {\r\n      // Handle the root path case more specifically\r\n      if (platform.path_prefix === \"/\" && location.pathname === \"/\") {\r\n        return platform;\r\n      }\r\n      // For other platforms, or if root path has sub-routes\r\n      if (platform.path_prefix !== \"/\" && location.pathname.startsWith(platform.path_prefix)) {\r\n        return platform;\r\n      }\r\n    }\r\n    // Fallback for root if no other platform matches, or if current path is a sub-route of root\r\n    const rootPlatform = availablePlatforms.find(p => p.path_prefix === \"/\");\r\n    if (rootPlatform && location.pathname.startsWith(rootPlatform.path_prefix)) {\r\n        return rootPlatform;\r\n    }\r\n    return availablePlatforms[0]; // Default to the first platform if no match\r\n  };\r\n\r\n  const selectedPlatform = getCurrentSelectedPlatform();\r\n  const selectedPlatformValue = selectedPlatform ? selectedPlatform.path_prefix : availablePlatforms[0].path_prefix;\r\n\r\n  const handlePlatformChange = (event) => {\r\n    const newPlatformPathPrefix = event.target.value;\r\n    const platformConfig = availablePlatforms.find(p => p.path_prefix === newPlatformPathPrefix);\r\n\r\n    if (platformConfig) {\r\n      navigate(platformConfig.path_prefix + (platformConfig.default_sub_route || \"\"));\r\n    }\r\n  };\r\n\r\n  return (\r\n    <FormControl sx={{ m: 1, minWidth: 120 }} size=\"small\">\r\n      <InputLabel id=\"platform-switcher-label\">Platform</InputLabel>\r\n      <Select\r\n        labelId=\"platform-switcher-label\"\r\n        id=\"platform-switcher-select\"\r\n        value={selectedPlatformValue}\r\n        label=\"Platform\"\r\n        onChange={handlePlatformChange}\r\n        sx={{ '.MuiSelect-select': { color: 'inherit', borderColor: 'inherit' } }}\r\n      >\r\n        {availablePlatforms.map((platform) => (\r\n          <MenuItem key={platform.path_prefix} value={platform.path_prefix}>\r\n            {platform.name}\r\n          </MenuItem>\r\n        ))}\r\n      </Select>\r\n    </FormControl>\r\n  );\r\n};\r\n\r\nexport default PlatformSwitcher;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,UAAU,QAAQ,eAAe;AACzE,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;;AAE3D;AACA;AACA;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,kBAAkB,GAAG,CACzB;EAAEC,IAAI,EAAE,oBAAoB;EAAEC,WAAW,EAAE,GAAG;EAAEC,iBAAiB,EAAE;AAAa,CAAC;AAAE;AACnF;EAAEF,IAAI,EAAE,sBAAsB;EAAEC,WAAW,EAAE,aAAa;EAAEC,iBAAiB,EAAE;AAAa,CAAC,EAC7F;EAAEF,IAAI,EAAE,cAAc;EAAEC,WAAW,EAAE,cAAc;EAAEC,iBAAiB,EAAE;AAAG;AAC3E;AAAA,CACD;AAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAMW,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAE9B,MAAMW,0BAA0B,GAAGA,CAAA,KAAM;IACvC,KAAK,MAAMC,QAAQ,IAAIT,kBAAkB,EAAE;MACzC;MACA,IAAIS,QAAQ,CAACP,WAAW,KAAK,GAAG,IAAII,QAAQ,CAACI,QAAQ,KAAK,GAAG,EAAE;QAC7D,OAAOD,QAAQ;MACjB;MACA;MACA,IAAIA,QAAQ,CAACP,WAAW,KAAK,GAAG,IAAII,QAAQ,CAACI,QAAQ,CAACC,UAAU,CAACF,QAAQ,CAACP,WAAW,CAAC,EAAE;QACtF,OAAOO,QAAQ;MACjB;IACF;IACA;IACA,MAAMG,YAAY,GAAGZ,kBAAkB,CAACa,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACZ,WAAW,KAAK,GAAG,CAAC;IACxE,IAAIU,YAAY,IAAIN,QAAQ,CAACI,QAAQ,CAACC,UAAU,CAACC,YAAY,CAACV,WAAW,CAAC,EAAE;MACxE,OAAOU,YAAY;IACvB;IACA,OAAOZ,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;EAChC,CAAC;EAED,MAAMe,gBAAgB,GAAGP,0BAA0B,CAAC,CAAC;EACrD,MAAMQ,qBAAqB,GAAGD,gBAAgB,GAAGA,gBAAgB,CAACb,WAAW,GAAGF,kBAAkB,CAAC,CAAC,CAAC,CAACE,WAAW;EAEjH,MAAMe,oBAAoB,GAAIC,KAAK,IAAK;IACtC,MAAMC,qBAAqB,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK;IAChD,MAAMC,cAAc,GAAGtB,kBAAkB,CAACa,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACZ,WAAW,KAAKiB,qBAAqB,CAAC;IAE5F,IAAIG,cAAc,EAAE;MAClBf,QAAQ,CAACe,cAAc,CAACpB,WAAW,IAAIoB,cAAc,CAACnB,iBAAiB,IAAI,EAAE,CAAC,CAAC;IACjF;EACF,CAAC;EAED,oBACEJ,OAAA,CAACL,WAAW;IAAC6B,EAAE,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAI,CAAE;IAACC,IAAI,EAAC,OAAO;IAAAC,QAAA,gBACpD5B,OAAA,CAACJ,UAAU;MAACiC,EAAE,EAAC,yBAAyB;MAAAD,QAAA,EAAC;IAAQ;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAC9DjC,OAAA,CAACP,MAAM;MACLyC,OAAO,EAAC,yBAAyB;MACjCL,EAAE,EAAC,0BAA0B;MAC7BP,KAAK,EAAEL,qBAAsB;MAC7BkB,KAAK,EAAC,UAAU;MAChBC,QAAQ,EAAElB,oBAAqB;MAC/BM,EAAE,EAAE;QAAE,mBAAmB,EAAE;UAAEa,KAAK,EAAE,SAAS;UAAEC,WAAW,EAAE;QAAU;MAAE,CAAE;MAAAV,QAAA,EAEzE3B,kBAAkB,CAACsC,GAAG,CAAE7B,QAAQ,iBAC/BV,OAAA,CAACN,QAAQ;QAA4B4B,KAAK,EAAEZ,QAAQ,CAACP,WAAY;QAAAyB,QAAA,EAC9DlB,QAAQ,CAACR;MAAI,GADDQ,QAAQ,CAACP,WAAW;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEzB,CACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAElB,CAAC;AAAC3B,EAAA,CAtDID,gBAAgB;EAAA,QACHR,WAAW,EACXC,WAAW;AAAA;AAAA0C,EAAA,GAFxBnC,gBAAgB;AAwDtB,eAAeA,gBAAgB;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}