{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents\\\\Programing\\\\TRO\\\\ModelTestsWorkbench\\\\frontend\\\\src\\\\pages\\\\boundingbox\\\\PictureManagementPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Container, Typography, Box, Button, CircularProgress, Alert, List, ListItem, ListItemText, Paper, Input, TextField, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, IconButton, Tooltip, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, Pagination, Grid } from '@mui/material';\nimport { uploadBbPicture, getBbPictures, updateBbPictureName, deleteBbPicture } from '../../services/api_bounding_box';\nimport EditIcon from '@mui/icons-material/Edit';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport PhotoCameraBackIcon from '@mui/icons-material/PhotoCameraBack'; // Placeholder for thumbnail\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ACCEPTED_FILE_TYPES = ['.jpg', '.jpeg', '.png'];\nconst ACCEPTED_FILE_TYPES_STRING = ACCEPTED_FILE_TYPES.join(', ');\nconst ITEMS_PER_PAGE = 5; // For image browser pagination\n\nconst BoundingBoxPictureManagementPage = () => {\n  _s();\n  // Upload states\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [filePreview, setFilePreview] = useState(null);\n  const [uploadError, setUploadError] = useState('');\n  const [uploadSuccess, setUploadSuccess] = useState('');\n  const [isUploading, setIsUploading] = useState(false);\n\n  // Image Browser states\n  const [picturesList, setPicturesList] = useState([]);\n  const [isLoadingPictures, setIsLoadingPictures] = useState(false);\n  const [picturesError, setPicturesError] = useState('');\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(0);\n  const [totalPictures, setTotalPictures] = useState(0);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');\n\n  // Edit states\n  const [editPictureId, setEditPictureId] = useState(null);\n  const [editPictureName, setEditPictureName] = useState('');\n  const [isRenameModalOpen, setIsRenameModalOpen] = useState(false);\n  const [renameError, setRenameError] = useState('');\n\n  // Delete states\n  const [pictureToDeleteId, setPictureToDeleteId] = useState(null);\n  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);\n\n  // Debounce search term\n  useEffect(() => {\n    const handler = setTimeout(() => {\n      setDebouncedSearchTerm(searchTerm);\n      setCurrentPage(1); // Reset to page 1 on new search\n    }, 500); // 500ms delay\n    return () => clearTimeout(handler);\n  }, [searchTerm]);\n  const fetchPicturesList = useCallback(async (page, search) => {\n    setIsLoadingPictures(true);\n    setPicturesError('');\n    try {\n      const data = await getBbPictures(page, search, ITEMS_PER_PAGE);\n      setPicturesList(data.pictures || []);\n      setTotalPages(data.totalPages || 0);\n      setTotalPictures(data.totalPictures || 0);\n      setCurrentPage(data.currentPage || 1);\n    } catch (error) {\n      console.error('Fetch pictures error:', error);\n      setPicturesError(error.message || 'Failed to fetch pictures.');\n      setPicturesList([]);\n      setTotalPages(0);\n      setTotalPictures(0);\n    } finally {\n      setIsLoadingPictures(false);\n    }\n  }, []);\n  useEffect(() => {\n    fetchPicturesList(currentPage, debouncedSearchTerm);\n  }, [currentPage, debouncedSearchTerm, fetchPicturesList]);\n  const handleFileSelect = event => {\n    const file = event.target.files[0];\n    setUploadSuccess('');\n    setUploadError('');\n    if (!file) {\n      setSelectedFile(null);\n      setFilePreview(null);\n      return;\n    }\n    const fileType = `.${file.name.split('.').pop().toLowerCase()}`;\n    if (!ACCEPTED_FILE_TYPES.includes(fileType)) {\n      setUploadError(`Invalid file type. Please select ${ACCEPTED_FILE_TYPES_STRING}.`);\n      setSelectedFile(null);\n      setFilePreview(null);\n      event.target.value = null;\n      return;\n    }\n    setSelectedFile(file);\n    const reader = new FileReader();\n    reader.onloadend = () => setFilePreview(reader.result);\n    reader.readAsDataURL(file);\n  };\n  const handleUpload = async () => {\n    if (!selectedFile) {\n      setUploadError('No file selected.');\n      return;\n    }\n    setIsUploading(true);\n    setUploadError('');\n    setUploadSuccess('');\n    const formData = new FormData();\n    formData.append('picture', selectedFile);\n    try {\n      const response = await uploadBbPicture(formData);\n      setUploadSuccess(`File \"${response.name}\" uploaded! ID: ${response.id}`);\n      setSelectedFile(null);\n      setFilePreview(null);\n      if (document.getElementById('bb-picture-upload-input')) {\n        document.getElementById('bb-picture-upload-input').value = null;\n      }\n      fetchPicturesList(1, ''); // Refresh list on page 1, clear search\n    } catch (error) {\n      setUploadError(error.message || 'Upload failed.');\n    } finally {\n      setIsUploading(false);\n    }\n  };\n  const handleSearchChange = event => {\n    setSearchTerm(event.target.value);\n  };\n  const handlePageChange = (event, value) => {\n    setCurrentPage(value);\n  };\n\n  // Rename handlers\n  const openRenameModal = picture => {\n    setEditPictureId(picture.id);\n    setEditPictureName(picture.name);\n    setIsRenameModalOpen(true);\n    setRenameError('');\n  };\n  const closeRenameModal = () => {\n    setIsRenameModalOpen(false);\n    setEditPictureId(null);\n    setEditPictureName('');\n    setRenameError('');\n  };\n  const handleRenamePicture = async () => {\n    if (!editPictureName.trim()) {\n      setRenameError('Filename cannot be empty.');\n      return;\n    }\n    try {\n      await updateBbPictureName(editPictureId, editPictureName.trim());\n      fetchPicturesList(currentPage, debouncedSearchTerm); // Refresh\n      closeRenameModal();\n    } catch (error) {\n      setRenameError(error.message || 'Failed to rename picture.');\n    }\n  };\n\n  // Delete handlers\n  const openDeleteConfirm = pictureId => {\n    setPictureToDeleteId(pictureId);\n    setIsDeleteConfirmOpen(true);\n  };\n  const closeDeleteConfirm = () => {\n    setIsDeleteConfirmOpen(false);\n    setPictureToDeleteId(null);\n  };\n  const handleDeletePicture = async () => {\n    try {\n      await deleteBbPicture(pictureToDeleteId);\n      fetchPicturesList(1, ''); // Refresh list, go to page 1 and clear search\n      closeDeleteConfirm();\n    } catch (error) {\n      setPicturesError(error.message || 'Failed to delete picture.'); // Show error in main list area\n      closeDeleteConfirm();\n    }\n  };\n  const formatDate = isoString => isoString ? new Date(isoString).toLocaleString() : 'N/A';\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"lg\",\n    sx: {\n      mt: 4,\n      mb: 4\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      component: \"h1\",\n      children: \"Picture Management (Bounding Box)\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 2,\n      sx: {\n        p: 3,\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Upload New Picture\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"textSecondary\",\n        sx: {\n          mb: 1\n        },\n        children: [\"Accepted file types: \", ACCEPTED_FILE_TYPES_STRING]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          gap: 2,\n          alignItems: 'flex-start'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Input, {\n          type: \"file\",\n          id: \"bb-picture-upload-input\",\n          onChange: handleFileSelect,\n          inputProps: {\n            accept: ACCEPTED_FILE_TYPES_STRING\n          },\n          sx: {\n            display: 'block',\n            mb: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this), filePreview && selectedFile && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            my: 1,\n            textAlign: 'left',\n            border: '1px solid #ddd',\n            p: 1,\n            borderRadius: 1,\n            maxWidth: 220\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            children: \"Preview:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n            src: filePreview,\n            alt: selectedFile.name,\n            style: {\n              width: '200px',\n              height: 'auto',\n              maxHeight: '200px',\n              marginTop: '10px',\n              objectFit: 'contain'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            display: \"block\",\n            children: selectedFile.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 13\n        }, this), uploadError && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            width: '100%',\n            mt: 1\n          },\n          children: uploadError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 27\n        }, this), uploadSuccess && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"success\",\n          sx: {\n            width: '100%',\n            mt: 1\n          },\n          children: uploadSuccess\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          onClick: handleUpload,\n          disabled: !selectedFile || isUploading,\n          startIcon: isUploading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20,\n            color: \"inherit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 143\n          }, this) : null,\n          children: isUploading ? 'Uploading...' : 'Upload Picture'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      gutterBottom: true,\n      sx: {\n        mt: 4\n      },\n      children: \"Image Browser\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TextField, {\n      fullWidth: true,\n      label: \"Search by filename\",\n      variant: \"outlined\",\n      size: \"small\",\n      value: searchTerm,\n      onChange: handleSearchChange,\n      sx: {\n        mb: 2\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 7\n    }, this), isLoadingPictures && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        my: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 87\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 29\n    }, this), picturesError && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: picturesError\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 25\n    }, this), !isLoadingPictures && picturesList.length === 0 && /*#__PURE__*/_jsxDEV(Typography, {\n      sx: {\n        textAlign: 'center',\n        my: 2\n      },\n      children: \"No images to display. Use the upload tool above to add pictures.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 9\n    }, this), !isLoadingPictures && picturesList.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        children: /*#__PURE__*/_jsxDEV(Table, {\n          sx: {\n            minWidth: 650\n          },\n          \"aria-label\": \"pictures table\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                sx: {\n                  width: 120\n                },\n                children: \"Thumbnail\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Filename\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: \"Created At\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: picturesList.map(pic => /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: pic.file_path ? /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: pic.file_path,\n                  alt: pic.name,\n                  style: {\n                    width: 100,\n                    height: 'auto',\n                    maxHeight: 70,\n                    objectFit: 'contain'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 25\n                }, this) : /*#__PURE__*/_jsxDEV(PhotoCameraBackIcon, {\n                  sx: {\n                    fontSize: 40,\n                    color: 'grey.400'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: pic.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: formatDate(pic.created_at)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Edit Filename\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: () => openRenameModal(pic),\n                    size: \"small\",\n                    children: /*#__PURE__*/_jsxDEV(EditIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 227,\n                      columnNumber: 87\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 227,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Delete Picture\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: () => openDeleteConfirm(pic.id),\n                    size: \"small\",\n                    color: \"error\",\n                    children: /*#__PURE__*/_jsxDEV(DeleteIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 230,\n                      columnNumber: 106\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 230,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 21\n              }, this)]\n            }, pic.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 205,\n        columnNumber: 11\n      }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          mt: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Pagination, {\n          count: totalPages,\n          page: currentPage,\n          onChange: handlePageChange,\n          color: \"primary\",\n          disabled: isLoadingPictures\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        sx: {\n          display: 'block',\n          textAlign: 'right',\n          mt: 1\n        },\n        children: [\"Total pictures: \", totalPictures]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: isRenameModalOpen,\n      onClose: closeRenameModal,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Rename Picture\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(DialogContentText, {\n          sx: {\n            mb: 1\n          },\n          children: \"Enter a new filename for the picture:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this), renameError && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            mb: 2\n          },\n          children: renameError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 27\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          id: \"name\",\n          label: \"New Filename\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: editPictureName,\n          onChange: e => setEditPictureName(e.target.value)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeRenameModal,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleRenamePicture,\n          disabled: !editPictureName.trim(),\n          children: \"Save\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: isDeleteConfirmOpen,\n      onClose: closeDeleteConfirm,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Confirm Delete\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(DialogContentText, {\n          children: \"Are you sure you want to delete this picture? This action may also affect associated experiments and results. This cannot be undone.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: closeDeleteConfirm,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleDeletePicture,\n          color: \"error\",\n          children: \"Delete\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 170,\n    columnNumber: 5\n  }, this);\n};\n_s(BoundingBoxPictureManagementPage, \"6YHxnFBeTXDOW27JajUg2A1VK5I=\");\n_c = BoundingBoxPictureManagementPage;\nexport default BoundingBoxPictureManagementPage;\nvar _c;\n$RefreshReg$(_c, \"BoundingBoxPictureManagementPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Container", "Typography", "Box", "<PERSON><PERSON>", "CircularProgress", "<PERSON><PERSON>", "List", "ListItem", "ListItemText", "Paper", "Input", "TextField", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "IconButton", "<PERSON><PERSON><PERSON>", "Dialog", "DialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogTitle", "Pagination", "Grid", "uploadBbPicture", "getBbPictures", "updateBbPictureName", "deleteBbPicture", "EditIcon", "DeleteIcon", "PhotoCameraBackIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ACCEPTED_FILE_TYPES", "ACCEPTED_FILE_TYPES_STRING", "join", "ITEMS_PER_PAGE", "BoundingBoxPictureManagementPage", "_s", "selectedFile", "setSelectedFile", "filePreview", "setFilePreview", "uploadError", "setUploadError", "uploadSuccess", "setUploadSuccess", "isUploading", "setIsUploading", "picturesList", "setPicturesList", "isLoadingPictures", "setIsLoadingPictures", "picturesError", "setPicturesError", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "totalPictures", "setTotalPictures", "searchTerm", "setSearchTerm", "debouncedSearchTerm", "setDebouncedSearchTerm", "editPictureId", "setEditPictureId", "editPictureName", "setEditPictureName", "isRenameModalOpen", "setIsRenameModalOpen", "renameError", "setRenameError", "pictureToDeleteId", "setPictureToDeleteId", "isDeleteConfirmOpen", "setIsDeleteConfirmOpen", "handler", "setTimeout", "clearTimeout", "fetchPicturesList", "page", "search", "data", "pictures", "error", "console", "message", "handleFileSelect", "event", "file", "target", "files", "fileType", "name", "split", "pop", "toLowerCase", "includes", "value", "reader", "FileReader", "onloadend", "result", "readAsDataURL", "handleUpload", "formData", "FormData", "append", "response", "id", "document", "getElementById", "handleSearchChange", "handlePageChange", "openRenameModal", "picture", "closeRenameModal", "handleRenamePicture", "trim", "openDeleteConfirm", "pictureId", "closeDeleteConfirm", "handleDeletePicture", "formatDate", "isoString", "Date", "toLocaleString", "max<PERSON><PERSON><PERSON>", "sx", "mt", "mb", "children", "variant", "gutterBottom", "component", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "elevation", "p", "color", "display", "flexDirection", "gap", "alignItems", "type", "onChange", "inputProps", "accept", "my", "textAlign", "border", "borderRadius", "src", "alt", "style", "width", "height", "maxHeight", "marginTop", "objectFit", "severity", "onClick", "disabled", "startIcon", "size", "fullWidth", "label", "justifyContent", "length", "min<PERSON><PERSON><PERSON>", "align", "map", "pic", "file_path", "fontSize", "created_at", "title", "count", "open", "onClose", "autoFocus", "margin", "e", "_c", "$RefreshReg$"], "sources": ["D:/Documents/Programing/TRO/ModelTestsWorkbench/frontend/src/pages/boundingbox/PictureManagementPage.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\r\nimport {\r\n  Container, Typo<PERSON>, Box, Button, CircularProgress, Alert,\r\n  List, ListItem, ListItemText, Paper, Input, TextField,\r\n  Table, TableBody, TableCell, TableContainer, TableHead, TableRow,\r\n  IconButton, Tooltip, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle,\r\n  Pagination, Grid\r\n} from '@mui/material';\r\nimport { uploadBbPicture, getBbPictures, updateBbPictureName, deleteBbPicture } from '../../services/api_bounding_box';\r\nimport EditIcon from '@mui/icons-material/Edit';\r\nimport DeleteIcon from '@mui/icons-material/Delete';\r\nimport PhotoCameraBackIcon from '@mui/icons-material/PhotoCameraBack'; // Placeholder for thumbnail\r\n\r\nconst ACCEPTED_FILE_TYPES = ['.jpg', '.jpeg', '.png'];\r\nconst ACCEPTED_FILE_TYPES_STRING = ACCEPTED_FILE_TYPES.join(', ');\r\nconst ITEMS_PER_PAGE = 5; // For image browser pagination\r\n\r\nconst BoundingBoxPictureManagementPage = () => {\r\n  // Upload states\r\n  const [selectedFile, setSelectedFile] = useState(null);\r\n  const [filePreview, setFilePreview] = useState(null);\r\n  const [uploadError, setUploadError] = useState('');\r\n  const [uploadSuccess, setUploadSuccess] = useState('');\r\n  const [isUploading, setIsUploading] = useState(false);\r\n\r\n  // Image Browser states\r\n  const [picturesList, setPicturesList] = useState([]);\r\n  const [isLoadingPictures, setIsLoadingPictures] = useState(false);\r\n  const [picturesError, setPicturesError] = useState('');\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [totalPages, setTotalPages] = useState(0);\r\n  const [totalPictures, setTotalPictures] = useState(0);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n\r\n  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');\r\n\r\n  // Edit states\r\n  const [editPictureId, setEditPictureId] = useState(null);\r\n  const [editPictureName, setEditPictureName] = useState('');\r\n  const [isRenameModalOpen, setIsRenameModalOpen] = useState(false);\r\n  const [renameError, setRenameError] = useState('');\r\n\r\n  // Delete states\r\n  const [pictureToDeleteId, setPictureToDeleteId] = useState(null);\r\n  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);\r\n\r\n\r\n  // Debounce search term\r\n  useEffect(() => {\r\n    const handler = setTimeout(() => {\r\n      setDebouncedSearchTerm(searchTerm);\r\n      setCurrentPage(1); // Reset to page 1 on new search\r\n    }, 500); // 500ms delay\r\n    return () => clearTimeout(handler);\r\n  }, [searchTerm]);\r\n\r\n\r\n  const fetchPicturesList = useCallback(async (page, search) => {\r\n    setIsLoadingPictures(true);\r\n    setPicturesError('');\r\n    try {\r\n      const data = await getBbPictures(page, search, ITEMS_PER_PAGE);\r\n      setPicturesList(data.pictures || []);\r\n      setTotalPages(data.totalPages || 0);\r\n      setTotalPictures(data.totalPictures || 0);\r\n      setCurrentPage(data.currentPage || 1);\r\n    } catch (error) {\r\n      console.error('Fetch pictures error:', error);\r\n      setPicturesError(error.message || 'Failed to fetch pictures.');\r\n      setPicturesList([]);\r\n      setTotalPages(0);\r\n      setTotalPictures(0);\r\n    } finally {\r\n      setIsLoadingPictures(false);\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    fetchPicturesList(currentPage, debouncedSearchTerm);\r\n  }, [currentPage, debouncedSearchTerm, fetchPicturesList]);\r\n\r\n  const handleFileSelect = (event) => {\r\n    const file = event.target.files[0];\r\n    setUploadSuccess(''); setUploadError('');\r\n    if (!file) {\r\n      setSelectedFile(null); setFilePreview(null); return;\r\n    }\r\n    const fileType = `.${file.name.split('.').pop().toLowerCase()}`;\r\n    if (!ACCEPTED_FILE_TYPES.includes(fileType)) {\r\n      setUploadError(`Invalid file type. Please select ${ACCEPTED_FILE_TYPES_STRING}.`);\r\n      setSelectedFile(null); setFilePreview(null); event.target.value = null; return;\r\n    }\r\n    setSelectedFile(file);\r\n    const reader = new FileReader();\r\n    reader.onloadend = () => setFilePreview(reader.result);\r\n    reader.readAsDataURL(file);\r\n  };\r\n\r\n  const handleUpload = async () => {\r\n    if (!selectedFile) { setUploadError('No file selected.'); return; }\r\n    setIsUploading(true); setUploadError(''); setUploadSuccess('');\r\n    const formData = new FormData();\r\n    formData.append('picture', selectedFile);\r\n    try {\r\n      const response = await uploadBbPicture(formData);\r\n      setUploadSuccess(`File \"${response.name}\" uploaded! ID: ${response.id}`);\r\n      setSelectedFile(null); setFilePreview(null);\r\n      if(document.getElementById('bb-picture-upload-input')) {\r\n        document.getElementById('bb-picture-upload-input').value = null;\r\n      }\r\n      fetchPicturesList(1, ''); // Refresh list on page 1, clear search\r\n    } catch (error) {\r\n      setUploadError(error.message || 'Upload failed.');\r\n    } finally {\r\n      setIsUploading(false);\r\n    }\r\n  };\r\n\r\n  const handleSearchChange = (event) => {\r\n    setSearchTerm(event.target.value);\r\n  };\r\n\r\n  const handlePageChange = (event, value) => {\r\n    setCurrentPage(value);\r\n  };\r\n\r\n  // Rename handlers\r\n  const openRenameModal = (picture) => {\r\n    setEditPictureId(picture.id);\r\n    setEditPictureName(picture.name);\r\n    setIsRenameModalOpen(true);\r\n    setRenameError('');\r\n  };\r\n  const closeRenameModal = () => {\r\n    setIsRenameModalOpen(false); setEditPictureId(null); setEditPictureName(''); setRenameError('');\r\n  };\r\n  const handleRenamePicture = async () => {\r\n    if (!editPictureName.trim()) { setRenameError('Filename cannot be empty.'); return; }\r\n    try {\r\n      await updateBbPictureName(editPictureId, editPictureName.trim());\r\n      fetchPicturesList(currentPage, debouncedSearchTerm); // Refresh\r\n      closeRenameModal();\r\n    } catch (error) {\r\n      setRenameError(error.message || 'Failed to rename picture.');\r\n    }\r\n  };\r\n\r\n  // Delete handlers\r\n  const openDeleteConfirm = (pictureId) => {\r\n    setPictureToDeleteId(pictureId);\r\n    setIsDeleteConfirmOpen(true);\r\n  };\r\n  const closeDeleteConfirm = () => {\r\n    setIsDeleteConfirmOpen(false); setPictureToDeleteId(null);\r\n  };\r\n  const handleDeletePicture = async () => {\r\n    try {\r\n      await deleteBbPicture(pictureToDeleteId);\r\n      fetchPicturesList(1, ''); // Refresh list, go to page 1 and clear search\r\n      closeDeleteConfirm();\r\n    } catch (error) {\r\n      setPicturesError(error.message || 'Failed to delete picture.'); // Show error in main list area\r\n      closeDeleteConfirm();\r\n    }\r\n  };\r\n\r\n  const formatDate = (isoString) => isoString ? new Date(isoString).toLocaleString() : 'N/A';\r\n\r\n  return (\r\n    <Container maxWidth=\"lg\" sx={{ mt: 4, mb: 4 }}>\r\n      <Typography variant=\"h4\" gutterBottom component=\"h1\">Picture Management (Bounding Box)</Typography>\r\n\r\n      <Paper elevation={2} sx={{ p: 3, mb: 4 }}>\r\n        <Typography variant=\"h6\" gutterBottom>Upload New Picture</Typography>\r\n        <Typography variant=\"body2\" color=\"textSecondary\" sx={{ mb: 1 }}>Accepted file types: {ACCEPTED_FILE_TYPES_STRING}</Typography>\r\n        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, alignItems: 'flex-start' }}>\r\n          <Input type=\"file\" id=\"bb-picture-upload-input\" onChange={handleFileSelect} inputProps={{ accept: ACCEPTED_FILE_TYPES_STRING }} sx={{ display: 'block', mb: 1 }}/>\r\n          {filePreview && selectedFile && (\r\n            <Box sx={{ my: 1, textAlign: 'left', border: '1px solid #ddd', p:1, borderRadius:1, maxWidth:220 }}>\r\n              <Typography variant=\"subtitle2\">Preview:</Typography>\r\n              <img src={filePreview} alt={selectedFile.name} style={{ width: '200px', height: 'auto', maxHeight: '200px', marginTop: '10px', objectFit:'contain' }} />\r\n              <Typography variant=\"caption\" display=\"block\">{selectedFile.name}</Typography>\r\n            </Box>\r\n          )}\r\n          {uploadError && <Alert severity=\"error\" sx={{ width: '100%', mt:1 }}>{uploadError}</Alert>}\r\n          {uploadSuccess && <Alert severity=\"success\" sx={{ width: '100%', mt:1 }}>{uploadSuccess}</Alert>}\r\n          <Button variant=\"contained\" color=\"primary\" onClick={handleUpload} disabled={!selectedFile || isUploading} startIcon={isUploading ? <CircularProgress size={20} color=\"inherit\" /> : null}>\r\n            {isUploading ? 'Uploading...' : 'Upload Picture'}\r\n          </Button>\r\n        </Box>\r\n      </Paper>\r\n\r\n      <Typography variant=\"h6\" gutterBottom sx={{mt:4}}>Image Browser</Typography>\r\n      <TextField fullWidth label=\"Search by filename\" variant=\"outlined\" size=\"small\" value={searchTerm} onChange={handleSearchChange} sx={{ mb: 2 }}/>\r\n\r\n      {isLoadingPictures && <Box sx={{display:'flex', justifyContent:'center', my:2}}><CircularProgress /></Box>}\r\n      {picturesError && <Alert severity=\"error\" sx={{ mb: 2 }}>{picturesError}</Alert>}\r\n\r\n      {!isLoadingPictures && picturesList.length === 0 && (\r\n        <Typography sx={{textAlign:'center', my:2}}>No images to display. Use the upload tool above to add pictures.</Typography>\r\n      )}\r\n\r\n      {!isLoadingPictures && picturesList.length > 0 && (\r\n        <>\r\n          <TableContainer component={Paper}>\r\n            <Table sx={{ minWidth: 650 }} aria-label=\"pictures table\">\r\n              <TableHead>\r\n                <TableRow>\r\n                  <TableCell sx={{width:120}}>Thumbnail</TableCell>\r\n                  <TableCell>Filename</TableCell>\r\n                  <TableCell>Created At</TableCell>\r\n                  <TableCell align=\"right\">Actions</TableCell>\r\n                </TableRow>\r\n              </TableHead>\r\n              <TableBody>\r\n                {picturesList.map((pic) => (\r\n                  <TableRow key={pic.id}>\r\n                    <TableCell>\r\n                      {pic.file_path ? (\r\n                        <img src={pic.file_path} alt={pic.name} style={{ width: 100, height: 'auto', maxHeight: 70, objectFit: 'contain' }} />\r\n                      ) : <PhotoCameraBackIcon sx={{fontSize: 40, color: 'grey.400'}} />}\r\n                    </TableCell>\r\n                    <TableCell>{pic.name}</TableCell>\r\n                    <TableCell>{formatDate(pic.created_at)}</TableCell>\r\n                    <TableCell align=\"right\">\r\n                      <Tooltip title=\"Edit Filename\">\r\n                        <IconButton onClick={() => openRenameModal(pic)} size=\"small\"><EditIcon /></IconButton>\r\n                      </Tooltip>\r\n                      <Tooltip title=\"Delete Picture\">\r\n                        <IconButton onClick={() => openDeleteConfirm(pic.id)} size=\"small\" color=\"error\"><DeleteIcon /></IconButton>\r\n                      </Tooltip>\r\n                    </TableCell>\r\n                  </TableRow>\r\n                ))}\r\n              </TableBody>\r\n            </Table>\r\n          </TableContainer>\r\n          {totalPages > 1 && (\r\n            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>\r\n              <Pagination count={totalPages} page={currentPage} onChange={handlePageChange} color=\"primary\" disabled={isLoadingPictures} />\r\n            </Box>\r\n          )}\r\n          <Typography variant=\"caption\" sx={{display:'block', textAlign:'right', mt:1}}>Total pictures: {totalPictures}</Typography>\r\n        </>\r\n      )}\r\n\r\n      {/* Rename Modal */}\r\n      <Dialog open={isRenameModalOpen} onClose={closeRenameModal}>\r\n        <DialogTitle>Rename Picture</DialogTitle>\r\n        <DialogContent>\r\n          <DialogContentText sx={{mb:1}}>Enter a new filename for the picture:</DialogContentText>\r\n          {renameError && <Alert severity=\"error\" sx={{mb:2}}>{renameError}</Alert>}\r\n          <TextField autoFocus margin=\"dense\" id=\"name\" label=\"New Filename\" type=\"text\" fullWidth variant=\"outlined\" value={editPictureName} onChange={(e) => setEditPictureName(e.target.value)} />\r\n        </DialogContent>\r\n        <DialogActions>\r\n          <Button onClick={closeRenameModal}>Cancel</Button>\r\n          <Button onClick={handleRenamePicture} disabled={!editPictureName.trim()}>Save</Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n\r\n      {/* Delete Confirmation Dialog */}\r\n      <Dialog open={isDeleteConfirmOpen} onClose={closeDeleteConfirm}>\r\n        <DialogTitle>Confirm Delete</DialogTitle>\r\n        <DialogContent>\r\n          <DialogContentText>Are you sure you want to delete this picture? This action may also affect associated experiments and results. This cannot be undone.</DialogContentText>\r\n        </DialogContent>\r\n        <DialogActions>\r\n          <Button onClick={closeDeleteConfirm}>Cancel</Button>\r\n          <Button onClick={handleDeletePicture} color=\"error\">Delete</Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default BoundingBoxPictureManagementPage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SACEC,SAAS,EAAEC,UAAU,EAAEC,GAAG,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,KAAK,EAC3DC,IAAI,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EACrDC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAEC,cAAc,EAAEC,SAAS,EAAEC,QAAQ,EAChEC,UAAU,EAAEC,OAAO,EAAEC,MAAM,EAAEC,aAAa,EAAEC,aAAa,EAAEC,iBAAiB,EAAEC,WAAW,EACzFC,UAAU,EAAEC,IAAI,QACX,eAAe;AACtB,SAASC,eAAe,EAAEC,aAAa,EAAEC,mBAAmB,EAAEC,eAAe,QAAQ,iCAAiC;AACtH,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,mBAAmB,MAAM,qCAAqC,CAAC,CAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvE,MAAMC,mBAAmB,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC;AACrD,MAAMC,0BAA0B,GAAGD,mBAAmB,CAACE,IAAI,CAAC,IAAI,CAAC;AACjE,MAAMC,cAAc,GAAG,CAAC,CAAC,CAAC;;AAE1B,MAAMC,gCAAgC,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7C;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACiD,WAAW,EAAEC,cAAc,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACmD,WAAW,EAAEC,cAAc,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACqD,aAAa,EAAEC,gBAAgB,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACuD,WAAW,EAAEC,cAAc,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACA,MAAM,CAACyD,YAAY,EAAEC,eAAe,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC2D,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC6D,aAAa,EAAEC,gBAAgB,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC+D,WAAW,EAAEC,cAAc,CAAC,GAAGhE,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACiE,UAAU,EAAEC,aAAa,CAAC,GAAGlE,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACmE,aAAa,EAAEC,gBAAgB,CAAC,GAAGpE,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAACqE,UAAU,EAAEC,aAAa,CAAC,GAAGtE,QAAQ,CAAC,EAAE,CAAC;EAEhD,MAAM,CAACuE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;;EAElE;EACA,MAAM,CAACyE,aAAa,EAAEC,gBAAgB,CAAC,GAAG1E,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC2E,eAAe,EAAEC,kBAAkB,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC6E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC+E,WAAW,EAAEC,cAAc,CAAC,GAAGhF,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACA,MAAM,CAACiF,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlF,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACmF,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGpF,QAAQ,CAAC,KAAK,CAAC;;EAGrE;EACAC,SAAS,CAAC,MAAM;IACd,MAAMoF,OAAO,GAAGC,UAAU,CAAC,MAAM;MAC/Bd,sBAAsB,CAACH,UAAU,CAAC;MAClCL,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACT,OAAO,MAAMuB,YAAY,CAACF,OAAO,CAAC;EACpC,CAAC,EAAE,CAAChB,UAAU,CAAC,CAAC;EAGhB,MAAMmB,iBAAiB,GAAGtF,WAAW,CAAC,OAAOuF,IAAI,EAAEC,MAAM,KAAK;IAC5D9B,oBAAoB,CAAC,IAAI,CAAC;IAC1BE,gBAAgB,CAAC,EAAE,CAAC;IACpB,IAAI;MACF,MAAM6B,IAAI,GAAG,MAAM5D,aAAa,CAAC0D,IAAI,EAAEC,MAAM,EAAE9C,cAAc,CAAC;MAC9Dc,eAAe,CAACiC,IAAI,CAACC,QAAQ,IAAI,EAAE,CAAC;MACpC1B,aAAa,CAACyB,IAAI,CAAC1B,UAAU,IAAI,CAAC,CAAC;MACnCG,gBAAgB,CAACuB,IAAI,CAACxB,aAAa,IAAI,CAAC,CAAC;MACzCH,cAAc,CAAC2B,IAAI,CAAC5B,WAAW,IAAI,CAAC,CAAC;IACvC,CAAC,CAAC,OAAO8B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C/B,gBAAgB,CAAC+B,KAAK,CAACE,OAAO,IAAI,2BAA2B,CAAC;MAC9DrC,eAAe,CAAC,EAAE,CAAC;MACnBQ,aAAa,CAAC,CAAC,CAAC;MAChBE,gBAAgB,CAAC,CAAC,CAAC;IACrB,CAAC,SAAS;MACRR,oBAAoB,CAAC,KAAK,CAAC;IAC7B;EACF,CAAC,EAAE,EAAE,CAAC;EAEN3D,SAAS,CAAC,MAAM;IACduF,iBAAiB,CAACzB,WAAW,EAAEQ,mBAAmB,CAAC;EACrD,CAAC,EAAE,CAACR,WAAW,EAAEQ,mBAAmB,EAAEiB,iBAAiB,CAAC,CAAC;EAEzD,MAAMQ,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC9C,gBAAgB,CAAC,EAAE,CAAC;IAAEF,cAAc,CAAC,EAAE,CAAC;IACxC,IAAI,CAAC8C,IAAI,EAAE;MACTlD,eAAe,CAAC,IAAI,CAAC;MAAEE,cAAc,CAAC,IAAI,CAAC;MAAE;IAC/C;IACA,MAAMmD,QAAQ,GAAG,IAAIH,IAAI,CAACI,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,EAAE;IAC/D,IAAI,CAAChE,mBAAmB,CAACiE,QAAQ,CAACL,QAAQ,CAAC,EAAE;MAC3CjD,cAAc,CAAC,oCAAoCV,0BAA0B,GAAG,CAAC;MACjFM,eAAe,CAAC,IAAI,CAAC;MAAEE,cAAc,CAAC,IAAI,CAAC;MAAE+C,KAAK,CAACE,MAAM,CAACQ,KAAK,GAAG,IAAI;MAAE;IAC1E;IACA3D,eAAe,CAACkD,IAAI,CAAC;IACrB,MAAMU,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAC/BD,MAAM,CAACE,SAAS,GAAG,MAAM5D,cAAc,CAAC0D,MAAM,CAACG,MAAM,CAAC;IACtDH,MAAM,CAACI,aAAa,CAACd,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMe,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAAClE,YAAY,EAAE;MAAEK,cAAc,CAAC,mBAAmB,CAAC;MAAE;IAAQ;IAClEI,cAAc,CAAC,IAAI,CAAC;IAAEJ,cAAc,CAAC,EAAE,CAAC;IAAEE,gBAAgB,CAAC,EAAE,CAAC;IAC9D,MAAM4D,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAErE,YAAY,CAAC;IACxC,IAAI;MACF,MAAMsE,QAAQ,GAAG,MAAMvF,eAAe,CAACoF,QAAQ,CAAC;MAChD5D,gBAAgB,CAAC,SAAS+D,QAAQ,CAACf,IAAI,mBAAmBe,QAAQ,CAACC,EAAE,EAAE,CAAC;MACxEtE,eAAe,CAAC,IAAI,CAAC;MAAEE,cAAc,CAAC,IAAI,CAAC;MAC3C,IAAGqE,QAAQ,CAACC,cAAc,CAAC,yBAAyB,CAAC,EAAE;QACrDD,QAAQ,CAACC,cAAc,CAAC,yBAAyB,CAAC,CAACb,KAAK,GAAG,IAAI;MACjE;MACAnB,iBAAiB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdzC,cAAc,CAACyC,KAAK,CAACE,OAAO,IAAI,gBAAgB,CAAC;IACnD,CAAC,SAAS;MACRvC,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,MAAMiE,kBAAkB,GAAIxB,KAAK,IAAK;IACpC3B,aAAa,CAAC2B,KAAK,CAACE,MAAM,CAACQ,KAAK,CAAC;EACnC,CAAC;EAED,MAAMe,gBAAgB,GAAGA,CAACzB,KAAK,EAAEU,KAAK,KAAK;IACzC3C,cAAc,CAAC2C,KAAK,CAAC;EACvB,CAAC;;EAED;EACA,MAAMgB,eAAe,GAAIC,OAAO,IAAK;IACnClD,gBAAgB,CAACkD,OAAO,CAACN,EAAE,CAAC;IAC5B1C,kBAAkB,CAACgD,OAAO,CAACtB,IAAI,CAAC;IAChCxB,oBAAoB,CAAC,IAAI,CAAC;IAC1BE,cAAc,CAAC,EAAE,CAAC;EACpB,CAAC;EACD,MAAM6C,gBAAgB,GAAGA,CAAA,KAAM;IAC7B/C,oBAAoB,CAAC,KAAK,CAAC;IAAEJ,gBAAgB,CAAC,IAAI,CAAC;IAAEE,kBAAkB,CAAC,EAAE,CAAC;IAAEI,cAAc,CAAC,EAAE,CAAC;EACjG,CAAC;EACD,MAAM8C,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI,CAACnD,eAAe,CAACoD,IAAI,CAAC,CAAC,EAAE;MAAE/C,cAAc,CAAC,2BAA2B,CAAC;MAAE;IAAQ;IACpF,IAAI;MACF,MAAMhD,mBAAmB,CAACyC,aAAa,EAAEE,eAAe,CAACoD,IAAI,CAAC,CAAC,CAAC;MAChEvC,iBAAiB,CAACzB,WAAW,EAAEQ,mBAAmB,CAAC,CAAC,CAAC;MACrDsD,gBAAgB,CAAC,CAAC;IACpB,CAAC,CAAC,OAAOhC,KAAK,EAAE;MACdb,cAAc,CAACa,KAAK,CAACE,OAAO,IAAI,2BAA2B,CAAC;IAC9D;EACF,CAAC;;EAED;EACA,MAAMiC,iBAAiB,GAAIC,SAAS,IAAK;IACvC/C,oBAAoB,CAAC+C,SAAS,CAAC;IAC/B7C,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EACD,MAAM8C,kBAAkB,GAAGA,CAAA,KAAM;IAC/B9C,sBAAsB,CAAC,KAAK,CAAC;IAAEF,oBAAoB,CAAC,IAAI,CAAC;EAC3D,CAAC;EACD,MAAMiD,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMlG,eAAe,CAACgD,iBAAiB,CAAC;MACxCO,iBAAiB,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;MAC1B0C,kBAAkB,CAAC,CAAC;IACtB,CAAC,CAAC,OAAOrC,KAAK,EAAE;MACd/B,gBAAgB,CAAC+B,KAAK,CAACE,OAAO,IAAI,2BAA2B,CAAC,CAAC,CAAC;MAChEmC,kBAAkB,CAAC,CAAC;IACtB;EACF,CAAC;EAED,MAAME,UAAU,GAAIC,SAAS,IAAKA,SAAS,GAAG,IAAIC,IAAI,CAACD,SAAS,CAAC,CAACE,cAAc,CAAC,CAAC,GAAG,KAAK;EAE1F,oBACEjG,OAAA,CAACnC,SAAS;IAACqI,QAAQ,EAAC,IAAI;IAACC,EAAE,EAAE;MAAEC,EAAE,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAC5CtG,OAAA,CAAClC,UAAU;MAACyI,OAAO,EAAC,IAAI;MAACC,YAAY;MAACC,SAAS,EAAC,IAAI;MAAAH,QAAA,EAAC;IAAiC;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEnG7G,OAAA,CAAC1B,KAAK;MAACwI,SAAS,EAAE,CAAE;MAACX,EAAE,EAAE;QAAEY,CAAC,EAAE,CAAC;QAAEV,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACvCtG,OAAA,CAAClC,UAAU;QAACyI,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,EAAC;MAAkB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACrE7G,OAAA,CAAClC,UAAU;QAACyI,OAAO,EAAC,OAAO;QAACS,KAAK,EAAC,eAAe;QAACb,EAAE,EAAE;UAAEE,EAAE,EAAE;QAAE,CAAE;QAAAC,QAAA,GAAC,uBAAqB,EAAClG,0BAA0B;MAAA;QAAAsG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC/H7G,OAAA,CAACjC,GAAG;QAACoI,EAAE,EAAE;UAAEc,OAAO,EAAE,MAAM;UAAEC,aAAa,EAAE,QAAQ;UAAEC,GAAG,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAa,CAAE;QAAAd,QAAA,gBACtFtG,OAAA,CAACzB,KAAK;UAAC8I,IAAI,EAAC,MAAM;UAACrC,EAAE,EAAC,yBAAyB;UAACsC,QAAQ,EAAE5D,gBAAiB;UAAC6D,UAAU,EAAE;YAAEC,MAAM,EAAEpH;UAA2B,CAAE;UAAC+F,EAAE,EAAE;YAAEc,OAAO,EAAE,OAAO;YAAEZ,EAAE,EAAE;UAAE;QAAE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC,EACjKlG,WAAW,IAAIF,YAAY,iBAC1BT,OAAA,CAACjC,GAAG;UAACoI,EAAE,EAAE;YAAEsB,EAAE,EAAE,CAAC;YAAEC,SAAS,EAAE,MAAM;YAAEC,MAAM,EAAE,gBAAgB;YAAEZ,CAAC,EAAC,CAAC;YAAEa,YAAY,EAAC,CAAC;YAAE1B,QAAQ,EAAC;UAAI,CAAE;UAAAI,QAAA,gBACjGtG,OAAA,CAAClC,UAAU;YAACyI,OAAO,EAAC,WAAW;YAAAD,QAAA,EAAC;UAAQ;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACrD7G,OAAA;YAAK6H,GAAG,EAAElH,WAAY;YAACmH,GAAG,EAAErH,YAAY,CAACuD,IAAK;YAAC+D,KAAK,EAAE;cAAEC,KAAK,EAAE,OAAO;cAAEC,MAAM,EAAE,MAAM;cAAEC,SAAS,EAAE,OAAO;cAAEC,SAAS,EAAE,MAAM;cAAEC,SAAS,EAAC;YAAU;UAAE;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACxJ7G,OAAA,CAAClC,UAAU;YAACyI,OAAO,EAAC,SAAS;YAACU,OAAO,EAAC,OAAO;YAAAX,QAAA,EAAE7F,YAAY,CAACuD;UAAI;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CACN,EACAhG,WAAW,iBAAIb,OAAA,CAAC9B,KAAK;UAACmK,QAAQ,EAAC,OAAO;UAAClC,EAAE,EAAE;YAAE6B,KAAK,EAAE,MAAM;YAAE5B,EAAE,EAAC;UAAE,CAAE;UAAAE,QAAA,EAAEzF;QAAW;UAAA6F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACzF9F,aAAa,iBAAIf,OAAA,CAAC9B,KAAK;UAACmK,QAAQ,EAAC,SAAS;UAAClC,EAAE,EAAE;YAAE6B,KAAK,EAAE,MAAM;YAAE5B,EAAE,EAAC;UAAE,CAAE;UAAAE,QAAA,EAAEvF;QAAa;UAAA2F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAChG7G,OAAA,CAAChC,MAAM;UAACuI,OAAO,EAAC,WAAW;UAACS,KAAK,EAAC,SAAS;UAACsB,OAAO,EAAE3D,YAAa;UAAC4D,QAAQ,EAAE,CAAC9H,YAAY,IAAIQ,WAAY;UAACuH,SAAS,EAAEvH,WAAW,gBAAGjB,OAAA,CAAC/B,gBAAgB;YAACwK,IAAI,EAAE,EAAG;YAACzB,KAAK,EAAC;UAAS;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG,IAAK;UAAAP,QAAA,EACvLrF,WAAW,GAAG,cAAc,GAAG;QAAgB;UAAAyF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAER7G,OAAA,CAAClC,UAAU;MAACyI,OAAO,EAAC,IAAI;MAACC,YAAY;MAACL,EAAE,EAAE;QAACC,EAAE,EAAC;MAAC,CAAE;MAAAE,QAAA,EAAC;IAAa;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAC5E7G,OAAA,CAACxB,SAAS;MAACkK,SAAS;MAACC,KAAK,EAAC,oBAAoB;MAACpC,OAAO,EAAC,UAAU;MAACkC,IAAI,EAAC,OAAO;MAACpE,KAAK,EAAEtC,UAAW;MAACuF,QAAQ,EAAEnC,kBAAmB;MAACgB,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE;IAAE;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC,CAAC,EAEhJxF,iBAAiB,iBAAIrB,OAAA,CAACjC,GAAG;MAACoI,EAAE,EAAE;QAACc,OAAO,EAAC,MAAM;QAAE2B,cAAc,EAAC,QAAQ;QAAEnB,EAAE,EAAC;MAAC,CAAE;MAAAnB,QAAA,eAACtG,OAAA,CAAC/B,gBAAgB;QAAAyI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,EACzGtF,aAAa,iBAAIvB,OAAA,CAAC9B,KAAK;MAACmK,QAAQ,EAAC,OAAO;MAAClC,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,EAAE/E;IAAa;MAAAmF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,EAE/E,CAACxF,iBAAiB,IAAIF,YAAY,CAAC0H,MAAM,KAAK,CAAC,iBAC9C7I,OAAA,CAAClC,UAAU;MAACqI,EAAE,EAAE;QAACuB,SAAS,EAAC,QAAQ;QAAED,EAAE,EAAC;MAAC,CAAE;MAAAnB,QAAA,EAAC;IAAgE;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CACzH,EAEA,CAACxF,iBAAiB,IAAIF,YAAY,CAAC0H,MAAM,GAAG,CAAC,iBAC5C7I,OAAA,CAAAE,SAAA;MAAAoG,QAAA,gBACEtG,OAAA,CAACpB,cAAc;QAAC6H,SAAS,EAAEnI,KAAM;QAAAgI,QAAA,eAC/BtG,OAAA,CAACvB,KAAK;UAAC0H,EAAE,EAAE;YAAE2C,QAAQ,EAAE;UAAI,CAAE;UAAC,cAAW,gBAAgB;UAAAxC,QAAA,gBACvDtG,OAAA,CAACnB,SAAS;YAAAyH,QAAA,eACRtG,OAAA,CAAClB,QAAQ;cAAAwH,QAAA,gBACPtG,OAAA,CAACrB,SAAS;gBAACwH,EAAE,EAAE;kBAAC6B,KAAK,EAAC;gBAAG,CAAE;gBAAA1B,QAAA,EAAC;cAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjD7G,OAAA,CAACrB,SAAS;gBAAA2H,QAAA,EAAC;cAAQ;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eAC/B7G,OAAA,CAACrB,SAAS;gBAAA2H,QAAA,EAAC;cAAU;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,eACjC7G,OAAA,CAACrB,SAAS;gBAACoK,KAAK,EAAC,OAAO;gBAAAzC,QAAA,EAAC;cAAO;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZ7G,OAAA,CAACtB,SAAS;YAAA4H,QAAA,EACPnF,YAAY,CAAC6H,GAAG,CAAEC,GAAG,iBACpBjJ,OAAA,CAAClB,QAAQ;cAAAwH,QAAA,gBACPtG,OAAA,CAACrB,SAAS;gBAAA2H,QAAA,EACP2C,GAAG,CAACC,SAAS,gBACZlJ,OAAA;kBAAK6H,GAAG,EAAEoB,GAAG,CAACC,SAAU;kBAACpB,GAAG,EAAEmB,GAAG,CAACjF,IAAK;kBAAC+D,KAAK,EAAE;oBAAEC,KAAK,EAAE,GAAG;oBAAEC,MAAM,EAAE,MAAM;oBAAEC,SAAS,EAAE,EAAE;oBAAEE,SAAS,EAAE;kBAAU;gBAAE;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBACpH7G,OAAA,CAACF,mBAAmB;kBAACqG,EAAE,EAAE;oBAACgD,QAAQ,EAAE,EAAE;oBAAEnC,KAAK,EAAE;kBAAU;gBAAE;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACZ7G,OAAA,CAACrB,SAAS;gBAAA2H,QAAA,EAAE2C,GAAG,CAACjF;cAAI;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACjC7G,OAAA,CAACrB,SAAS;gBAAA2H,QAAA,EAAER,UAAU,CAACmD,GAAG,CAACG,UAAU;cAAC;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnD7G,OAAA,CAACrB,SAAS;gBAACoK,KAAK,EAAC,OAAO;gBAAAzC,QAAA,gBACtBtG,OAAA,CAAChB,OAAO;kBAACqK,KAAK,EAAC,eAAe;kBAAA/C,QAAA,eAC5BtG,OAAA,CAACjB,UAAU;oBAACuJ,OAAO,EAAEA,CAAA,KAAMjD,eAAe,CAAC4D,GAAG,CAAE;oBAACR,IAAI,EAAC,OAAO;oBAAAnC,QAAA,eAACtG,OAAA,CAACJ,QAAQ;sBAAA8G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChF,CAAC,eACV7G,OAAA,CAAChB,OAAO;kBAACqK,KAAK,EAAC,gBAAgB;kBAAA/C,QAAA,eAC7BtG,OAAA,CAACjB,UAAU;oBAACuJ,OAAO,EAAEA,CAAA,KAAM5C,iBAAiB,CAACuD,GAAG,CAACjE,EAAE,CAAE;oBAACyD,IAAI,EAAC,OAAO;oBAACzB,KAAK,EAAC,OAAO;oBAAAV,QAAA,eAACtG,OAAA,CAACH,UAAU;sBAAA6G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA,GAfCoC,GAAG,CAACjE,EAAE;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgBX,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,EAChBlF,UAAU,GAAG,CAAC,iBACb3B,OAAA,CAACjC,GAAG;QAACoI,EAAE,EAAE;UAAEc,OAAO,EAAE,MAAM;UAAE2B,cAAc,EAAE,QAAQ;UAAExC,EAAE,EAAE;QAAE,CAAE;QAAAE,QAAA,eAC5DtG,OAAA,CAACV,UAAU;UAACgK,KAAK,EAAE3H,UAAW;UAACwB,IAAI,EAAE1B,WAAY;UAAC6F,QAAQ,EAAElC,gBAAiB;UAAC4B,KAAK,EAAC,SAAS;UAACuB,QAAQ,EAAElH;QAAkB;UAAAqF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1H,CACN,eACD7G,OAAA,CAAClC,UAAU;QAACyI,OAAO,EAAC,SAAS;QAACJ,EAAE,EAAE;UAACc,OAAO,EAAC,OAAO;UAAES,SAAS,EAAC,OAAO;UAAEtB,EAAE,EAAC;QAAC,CAAE;QAAAE,QAAA,GAAC,kBAAgB,EAACzE,aAAa;MAAA;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC;IAAA,eAC1H,CACH,eAGD7G,OAAA,CAACf,MAAM;MAACsK,IAAI,EAAEhH,iBAAkB;MAACiH,OAAO,EAAEjE,gBAAiB;MAAAe,QAAA,gBACzDtG,OAAA,CAACX,WAAW;QAAAiH,QAAA,EAAC;MAAc;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACzC7G,OAAA,CAACb,aAAa;QAAAmH,QAAA,gBACZtG,OAAA,CAACZ,iBAAiB;UAAC+G,EAAE,EAAE;YAACE,EAAE,EAAC;UAAC,CAAE;UAAAC,QAAA,EAAC;QAAqC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB,CAAC,EACvFpE,WAAW,iBAAIzC,OAAA,CAAC9B,KAAK;UAACmK,QAAQ,EAAC,OAAO;UAAClC,EAAE,EAAE;YAACE,EAAE,EAAC;UAAC,CAAE;UAAAC,QAAA,EAAE7D;QAAW;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACzE7G,OAAA,CAACxB,SAAS;UAACiL,SAAS;UAACC,MAAM,EAAC,OAAO;UAAC1E,EAAE,EAAC,MAAM;UAAC2D,KAAK,EAAC,cAAc;UAACtB,IAAI,EAAC,MAAM;UAACqB,SAAS;UAACnC,OAAO,EAAC,UAAU;UAAClC,KAAK,EAAEhC,eAAgB;UAACiF,QAAQ,EAAGqC,CAAC,IAAKrH,kBAAkB,CAACqH,CAAC,CAAC9F,MAAM,CAACQ,KAAK;QAAE;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9K,CAAC,eAChB7G,OAAA,CAACd,aAAa;QAAAoH,QAAA,gBACZtG,OAAA,CAAChC,MAAM;UAACsK,OAAO,EAAE/C,gBAAiB;UAAAe,QAAA,EAAC;QAAM;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClD7G,OAAA,CAAChC,MAAM;UAACsK,OAAO,EAAE9C,mBAAoB;UAAC+C,QAAQ,EAAE,CAAClG,eAAe,CAACoD,IAAI,CAAC,CAAE;UAAAa,QAAA,EAAC;QAAI;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGT7G,OAAA,CAACf,MAAM;MAACsK,IAAI,EAAE1G,mBAAoB;MAAC2G,OAAO,EAAE5D,kBAAmB;MAAAU,QAAA,gBAC7DtG,OAAA,CAACX,WAAW;QAAAiH,QAAA,EAAC;MAAc;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACzC7G,OAAA,CAACb,aAAa;QAAAmH,QAAA,eACZtG,OAAA,CAACZ,iBAAiB;UAAAkH,QAAA,EAAC;QAAoI;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9J,CAAC,eAChB7G,OAAA,CAACd,aAAa;QAAAoH,QAAA,gBACZtG,OAAA,CAAChC,MAAM;UAACsK,OAAO,EAAE1C,kBAAmB;UAAAU,QAAA,EAAC;QAAM;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACpD7G,OAAA,CAAChC,MAAM;UAACsK,OAAO,EAAEzC,mBAAoB;UAACmB,KAAK,EAAC,OAAO;UAAAV,QAAA,EAAC;QAAM;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEA,CAAC;AAEhB,CAAC;AAACrG,EAAA,CAjQID,gCAAgC;AAAAqJ,EAAA,GAAhCrJ,gCAAgC;AAmQtC,eAAeA,gCAAgC;AAAC,IAAAqJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}