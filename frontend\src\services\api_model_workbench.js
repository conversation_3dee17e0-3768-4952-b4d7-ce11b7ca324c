import apiClient from './api'; // Assuming api.js will now export apiClient

// --- Data Management ---
export const uploadImages = (formData) => {
  return apiClient.post(`/data/images`, formData, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
};

export const listImages = (params) => {
  const queryParams = { ...params };
  if (typeof queryParams.missing_ip_owner === 'boolean') {
    queryParams.missing_ip_owner = String(queryParams.missing_ip_owner);
  }
  return apiClient.get(`/data/images`, { params: queryParams });
};

export const getImageFile = (imageId) => {
  return apiClient.get(`/data/images/file/${imageId}`, { responseType: 'blob' });
};

export const deleteImage = (imageId) => {
  return apiClient.delete(`/data/images/${imageId}`);
};

export const updateImageIpOwner = (imageId, ipOwner) => {
  return apiClient.put(`/data/images/${imageId}/ip_owner`, { ip_owner: ipOwner });
};

// --- Model Management ---
export const listModels = (params) => {
  const queryParams = { ...params };
  delete queryParams.ip_category; // API does not use ip_category here
  return apiClient.get(`/models`, { params: queryParams });
};

export const refreshModels = () => {
  return apiClient.post(`/models/refresh`);
};

// --- Feature Computation ---
export const computeFeatures = (ipCategory) => {
  return apiClient.post(`/tasks/compute-features/${ipCategory}`);
};

// --- Combined Scores ---
export const getCombinedScores = (params) => {
  const queryParams = { ...params };
  if (typeof queryParams.is_active === 'boolean') {
    queryParams.is_active = String(queryParams.is_active);
  }
  return apiClient.get(`/combined-scores`, { params: queryParams });
};

export const updateCombinedScoreConfig = (configId, configData) => {
  const payload = { ...configData };
  delete payload.ip_category;
  return apiClient.put(`/combined-scores/${configId}`, payload);
};

export const createCombinedScoreConfig = (configData) => {
  return apiClient.post(`/combined-scores`, configData);
};

export const deleteCombinedScoreConfig = (configId) => {
  return apiClient.delete(`/combined-scores/${configId}`);
};

// --- Results ---
export const getResultsByModel = (params) => {
  return apiClient.get(`/results/by-model`, { params });
};

export const getResultsByProduct = (productImageId, params) => {
  return apiClient.get(`/results/by-product/${productImageId}`, { params });
};

export const addGroundTruth = (productImageId, correctIpImageId) => {
  return apiClient.post(`/data/ground_truth`, {
    product_image_id: productImageId,
    correct_ip_image_id: correctIpImageId
  });
};

export const removeGroundTruth = (productImageId, correctIpImageId) => {
  return apiClient.delete(`/data/ground_truth`, {
    data: {
      product_image_id: productImageId,
      correct_ip_image_id: correctIpImageId
    }
  });
};

// --- Dashboard ---
export const getPerformanceSummary = (ipCategory) => {
  return apiClient.get(`/dashboard/performance-summary`, { params: { ip_category: ipCategory } });
};

export const getScoreDistribution = (modelId, ipCategory) => {
  return apiClient.get(`/dashboard/score-distribution`, { params: { model_id: modelId, ip_category: ipCategory } });
};

export const getConfusionMatrix = (modelId, ipCategory, threshold) => {
  return apiClient.get(`/dashboard/confusion-matrix`, { params: { model_id: modelId, ip_category: ipCategory, threshold: threshold } });
};

// --- Qdrant Management ---
export const getQdrantCollections = async () => {
  try {
    const response = await apiClient.get(`/qdrant/collections`);
    return response.data;
  } catch (error) {
    console.error("Error fetching Qdrant collections:", error);
    throw error;
  }
};

export const deleteQdrantCollection = async (collectionName) => {
  try {
    const encodedCollectionName = encodeURIComponent(collectionName);
    const response = await apiClient.delete(`/qdrant/collections/${encodedCollectionName}`);
    return response.data;
  } catch (error) {
    console.error(`Error deleting Qdrant collection ${collectionName}:`, error);
    throw error;
  }
};

// --- Task Status ---
export const getTaskStatus = (taskId) => {
  return apiClient.get(`/tasks/status/${taskId}`);
};

export const triggerComputeCombinedScores = (ipCategory) => {
  return apiClient.post(`/tasks/compute-combined-scores/${ipCategory}`);
};
