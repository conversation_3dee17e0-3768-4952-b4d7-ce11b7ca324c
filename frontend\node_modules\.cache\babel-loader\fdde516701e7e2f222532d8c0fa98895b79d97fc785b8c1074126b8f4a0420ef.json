{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents\\\\Programing\\\\TRO\\\\ModelTestsWorkbench\\\\frontend\\\\src\\\\pages\\\\boundingbox\\\\ResultsPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Container, Typography, FormControl, InputLabel, Select, MenuItem, Box, Button, CircularProgress, Alert, Grid, Card, CardContent, TextField, Pagination, Tooltip, IconButton, Modal,\n// Added for New Experiment Modal\nPaper,\n// Added for Modal content styling\nStack // Added for Modal form layout\n} from '@mui/material';\nimport { getBbPictures, getBbExperiments, updateBbResultScore, getBbExperimentConfigurations, createBbExperiment } from '../../services/api_bounding_box';\nimport SaveIcon from '@mui/icons-material/Save';\nimport ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';\nimport AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline'; // For New Experiment button\n\n// Modal style\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst modalStyle = {\n  position: 'absolute',\n  top: '50%',\n  left: '50%',\n  transform: 'translate(-50%, -50%)',\n  width: '90%',\n  maxWidth: 600,\n  bgcolor: 'background.paper',\n  border: '2px solid #000',\n  boxShadow: 24,\n  p: 4\n};\nconst OUTPUT_TYPES = [\"Bounding Box\", \"Bounding Box + Segmentation Mask\"];\nconst ResultsPage = () => {\n  _s();\n  const [pictures, setPictures] = useState([]);\n  const [selectedPictureId, setSelectedPictureId] = useState('');\n  const [experiments, setExperiments] = useState([]);\n  const [isLoadingPictures, setIsLoadingPictures] = useState(false);\n  const [isLoadingExperiments, setIsLoadingExperiments] = useState(false);\n  const [error, setError] = useState(null);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(0);\n  const [totalExperiments, setTotalExperiments] = useState(0);\n  const [experimentScores, setExperimentScores] = useState({});\n  const [scoreErrors, setScoreErrors] = useState({});\n\n  // State for New Experiment Modal\n  const [isNewExperimentModalOpen, setIsNewExperimentModalOpen] = useState(false);\n  const [experimentConfigurations, setExperimentConfigurations] = useState([]);\n  const [selectedConfiguration, setSelectedConfiguration] = useState(null); // Store the whole config object\n\n  const [newExperimentPrompt, setNewExperimentPrompt] = useState('');\n  const [newExperimentResizeHeight, setNewExperimentResizeHeight] = useState(512);\n  const [newExperimentResizeWidth, setNewExperimentResizeWidth] = useState(512);\n  const [newExperimentOutputType, setNewExperimentOutputType] = useState(OUTPUT_TYPES[0]);\n  const [isCreatingExperiment, setIsCreatingExperiment] = useState(false);\n  const [newExperimentError, setNewExperimentError] = useState('');\n  const fetchPictures = useCallback(() => {\n    setIsLoadingPictures(true);\n    setError(null);\n    getBbPictures().then(data => {\n      // Ensure data is always an array\n      if (Array.isArray(data)) {\n        setPictures(data);\n      } else {\n        console.warn('getBbPictures returned non-array data:', data);\n        setPictures([]);\n      }\n    }).catch(err => {\n      console.error('Failed to load pictures:', err);\n      setError('Failed to load pictures.');\n      setPictures([]); // Ensure pictures is always an array\n    }).finally(() => setIsLoadingPictures(false));\n  }, []);\n  useEffect(() => {\n    fetchPictures();\n  }, [fetchPictures]);\n  const fetchExperiments = useCallback((pictureId, page) => {\n    if (!pictureId) {\n      setExperiments([]);\n      setTotalPages(0);\n      setTotalExperiments(0);\n      return;\n    }\n    ;\n    setIsLoadingExperiments(true);\n    setError(null);\n    getBbExperiments(pictureId, page).then(data => {\n      setExperiments(data.experiments || []);\n      setTotalPages(data.totalPages || 0);\n      setCurrentPage(data.currentPage || 1);\n      setTotalExperiments(data.totalExperiments || 0);\n      const initialScores = {};\n      (data.experiments || []).forEach(exp => (exp.results || []).forEach(res => {\n        if (res.score !== null && res.score !== undefined) initialScores[res.id] = res.score;\n      }));\n      setExperimentScores(prev => ({\n        ...prev,\n        ...initialScores\n      }));\n    }).catch(err => {\n      setError(`Failed to load experiments.`);\n      setExperiments([]);\n      setTotalPages(0);\n      setTotalExperiments(0);\n    }).finally(() => setIsLoadingExperiments(false));\n  }, []);\n  useEffect(() => {\n    if (selectedPictureId) fetchExperiments(selectedPictureId, currentPage);else {\n      setExperiments([]);\n      setTotalPages(0);\n      setTotalExperiments(0);\n    }\n  }, [selectedPictureId, currentPage, fetchExperiments]);\n  const fetchExpConfigs = useCallback(async () => {\n    try {\n      const configs = await getBbExperimentConfigurations();\n      setExperimentConfigurations(configs || []);\n    } catch (err) {\n      console.error(\"Failed to fetch experiment configurations:\", err);\n      // Optionally set an error state for config loading\n    }\n  }, []);\n  useEffect(() => {\n    // Fetch configurations when the page loads or modal is about to open\n    // For simplicity, fetching on page load for now.\n    fetchExpConfigs();\n  }, [fetchExpConfigs]);\n  const handlePictureChange = event => {\n    setSelectedPictureId(event.target.value);\n    setCurrentPage(1);\n    setError(null);\n    setExperimentScores({});\n    setScoreErrors({});\n  };\n  const handlePageChange = (event, value) => setCurrentPage(value);\n  const handleScoreChange = (resultId, value) => {\n    const numValue = value === '' ? '' : Number(value);\n    setExperimentScores(prev => ({\n      ...prev,\n      [resultId]: numValue\n    }));\n    if (numValue === '' || Number.isInteger(numValue) && numValue >= 0 && numValue <= 10) {\n      setScoreErrors(prev => ({\n        ...prev,\n        [resultId]: ''\n      }));\n    } else {\n      setScoreErrors(prev => ({\n        ...prev,\n        [resultId]: 'Score must be 0-10 (integer)'\n      }));\n    }\n  };\n  const handleSaveScore = async resultId => {\n    const score = experimentScores[resultId];\n    if (score === undefined || score === '' || scoreErrors[resultId]) {\n      alert('Invalid score.');\n      return;\n    }\n    try {\n      await updateBbResultScore(resultId, Number(score));\n      alert(`Score for result ${resultId} saved (mocked).`);\n      // Consider optimistic update or refetch experiment for this result\n    } catch (error) {\n      alert(`Failed to save score for result ${resultId}.`);\n    }\n  };\n  const formatDate = isoString => isoString ? new Date(isoString).toLocaleString() : 'N/A';\n\n  // New Experiment Modal Handlers\n  const handleOpenNewExperimentModal = () => {\n    setNewExperimentError('');\n    setSelectedConfiguration(null); // Reset selected config\n    // Reset form fields to default or last selected (if desired)\n    setNewExperimentPrompt('');\n    setNewExperimentResizeHeight(512);\n    setNewExperimentResizeWidth(512);\n    setNewExperimentOutputType(OUTPUT_TYPES[0]);\n    setIsNewExperimentModalOpen(true);\n    if (experimentConfigurations.length === 0) fetchExpConfigs(); // Fetch if not already loaded\n  };\n  const handleCloseNewExperimentModal = () => setIsNewExperimentModalOpen(false);\n  const handleSelectedConfigurationChange = event => {\n    const configIndex = event.target.value; // This will be the index\n    if (configIndex === \"\" || configIndex === undefined) {\n      setSelectedConfiguration(null);\n      setNewExperimentPrompt('');\n      setNewExperimentResizeHeight(512);\n      setNewExperimentResizeWidth(512);\n      setNewExperimentOutputType(OUTPUT_TYPES[0]);\n    } else {\n      const config = experimentConfigurations[configIndex];\n      setSelectedConfiguration(config); // Store the whole config object\n      setNewExperimentPrompt(config.prompt);\n      setNewExperimentResizeHeight(config.resize_height);\n      setNewExperimentResizeWidth(config.resize_width);\n      setNewExperimentOutputType(config.output_type);\n    }\n  };\n  const handleCreateNewExperiment = async () => {\n    if (!newExperimentPrompt.trim()) {\n      setNewExperimentError(\"Prompt cannot be empty.\");\n      return;\n    }\n    if (newExperimentResizeHeight <= 0 || newExperimentResizeWidth <= 0) {\n      setNewExperimentError(\"Resize dimensions must be positive.\");\n      return;\n    }\n    setIsCreatingExperiment(true);\n    setNewExperimentError('');\n    try {\n      const experimentData = {\n        picture_id: selectedPictureId,\n        prompt: newExperimentPrompt,\n        resize_height: parseInt(newExperimentResizeHeight, 10),\n        resize_width: parseInt(newExperimentResizeWidth, 10),\n        output_type: newExperimentOutputType\n      };\n      const newExp = await createBbExperiment(experimentData);\n      // Add to the start of the experiments list for immediate feedback\n      setExperiments(prev => [newExp, ...prev]);\n      setTotalExperiments(prev => prev + 1);\n      // If new experiment pushes list to new page, this won't show it unless we adjust current page.\n      // For simplicity, we'll just add it. A full refresh might be better.\n      // Or, if on page 1 and list is not full, it appears.\n      // fetchExperiments(selectedPictureId, currentPage); // Or refetch current page.\n      handleCloseNewExperimentModal();\n    } catch (err) {\n      setNewExperimentError(`Failed to create experiment: ${err.message || 'Unknown error'}`);\n    } finally {\n      setIsCreatingExperiment(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"xl\",\n    sx: {\n      mt: 4,\n      mb: 4\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      component: \"h1\",\n      children: \"Results Analysis by Product Pictures\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n      fullWidth: true,\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n        id: \"product-image-select-label\",\n        children: \"Select Product Image\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Select, {\n        labelId: \"product-image-select-label\",\n        value: selectedPictureId,\n        label: \"Select Product Image\",\n        onChange: handlePictureChange,\n        disabled: isLoadingPictures || pictures.length === 0,\n        children: [isLoadingPictures && /*#__PURE__*/_jsxDEV(MenuItem, {\n          value: \"\",\n          disabled: true,\n          children: /*#__PURE__*/_jsxDEV(\"em\", {\n            children: \"Loading images...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 61\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 33\n        }, this), !isLoadingPictures && pictures.length === 0 && /*#__PURE__*/_jsxDEV(MenuItem, {\n          value: \"\",\n          disabled: true,\n          children: /*#__PURE__*/_jsxDEV(\"em\", {\n            children: \"No pictures available.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 87\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 59\n        }, this), Array.isArray(pictures) && pictures.map(pic => /*#__PURE__*/_jsxDEV(MenuItem, {\n          value: pic.id,\n          children: [pic.name, \" (ID: \", pic.id, \")\"]\n        }, pic.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 62\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 7\n    }, this), !isLoadingPictures && pictures.length === 0 && !error && /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      sx: {\n        mb: 2,\n        textAlign: 'center'\n      },\n      children: \"No product pictures found.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 66\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'flex-end',\n        mb: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        color: \"primary\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddCircleOutlineIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 64\n        }, this),\n        onClick: handleOpenNewExperimentModal,\n        disabled: !selectedPictureId || isLoadingPictures || isLoadingExperiments,\n        children: \"+ New Experiment\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 256,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      open: isNewExperimentModalOpen,\n      onClose: handleCloseNewExperimentModal,\n      \"aria-labelledby\": \"new-experiment-modal-title\",\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: modalStyle,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          id: \"new-experiment-modal-title\",\n          variant: \"h6\",\n          component: \"h2\",\n          gutterBottom: true,\n          children: \"Create New Experiment\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this), newExperimentError && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            mb: 2\n          },\n          children: newExperimentError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 34\n        }, this), /*#__PURE__*/_jsxDEV(Stack, {\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              id: \"select-config-label\",\n              children: \"Select Existing Configuration (Optional)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              labelId: \"select-config-label\",\n              value: selectedConfiguration ? experimentConfigurations.indexOf(selectedConfiguration) : \"\",\n              label: \"Select Existing Configuration (Optional)\",\n              onChange: handleSelectedConfigurationChange,\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"\",\n                children: /*#__PURE__*/_jsxDEV(\"em\", {\n                  children: \"None - Manual Input\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 36\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 17\n              }, this), experimentConfigurations.map((config, index) => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: index,\n                children: [\"Prompt: \", config.prompt.substring(0, 30), \"... | Size: \", config.resize_width, \"x\", config.resize_height, \" | Type: \", config.output_type]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            label: \"Prompt\",\n            value: newExperimentPrompt,\n            onChange: e => setNewExperimentPrompt(e.target.value),\n            fullWidth: true,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 2,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"Resize Height\",\n                type: \"number\",\n                value: newExperimentResizeHeight,\n                onChange: e => setNewExperimentResizeHeight(Number(e.target.value)),\n                fullWidth: true,\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 6,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"Resize Width\",\n                type: \"number\",\n                value: newExperimentResizeWidth,\n                onChange: e => setNewExperimentResizeWidth(Number(e.target.value)),\n                fullWidth: true,\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              id: \"output-type-label\",\n              children: \"Output Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              labelId: \"output-type-label\",\n              value: newExperimentOutputType,\n              label: \"Output Type\",\n              onChange: e => setNewExperimentOutputType(e.target.value),\n              children: OUTPUT_TYPES.map(type => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: type,\n                children: type\n              }, type, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 48\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'flex-end',\n              gap: 1,\n              mt: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              onClick: handleCloseNewExperimentModal,\n              disabled: isCreatingExperiment,\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              onClick: handleCreateNewExperiment,\n              disabled: isCreatingExperiment || !newExperimentPrompt.trim() || newExperimentResizeHeight <= 0 || newExperimentResizeWidth <= 0,\n              children: isCreatingExperiment ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 41\n              }, this) : \"Add\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 7\n    }, this), isLoadingExperiments && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        my: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 93\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 32\n    }, this), !isLoadingExperiments && selectedPictureId && experiments.length === 0 && !error && /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      sx: {\n        textAlign: 'center',\n        mt: 2\n      },\n      children: \"No experiments found. Create one!\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 93\n    }, this), experiments.map((exp, index) => /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: [\"Experiment \", totalExperiments - (currentPage - 1) * 2 - index, \" (ID: \", exp.id.substring(0, 8), \"...)\", /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            sx: {\n              ml: 1\n            },\n            children: [\"Created: \", formatDate(exp.created_at)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 3,\n            mb: 2,\n            flexWrap: 'wrap'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Prompt:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 43\n            }, this), \" \", exp.prompt]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Dimensions:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 43\n            }, this), \" \", exp.resize_width, \"w x \", exp.resize_height, \"h\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Output Type:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 43\n            }, this), \" \", exp.output_type]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          sx: {\n            mt: 2,\n            mb: 1\n          },\n          children: \"Model Outputs:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 2,\n          children: (exp.results || []).map(result => /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Paper, {\n              elevation: 2,\n              sx: {\n                p: 2,\n                height: '100%',\n                display: 'flex',\n                flexDirection: 'column',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                gutterBottom: true,\n                children: result.model_name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  flexGrow: 1,\n                  display: 'flex',\n                  flexDirection: 'column',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  minHeight: 180,\n                  mb: 1\n                },\n                children: [result.status === 'success' && result.output_image_path && /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: result.output_image_path,\n                  alt: `Output for ${result.model_name}`,\n                  style: {\n                    maxWidth: '100%',\n                    maxHeight: '150px',\n                    objectFit: 'contain'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 84\n                }, this), result.status === 'success' && !result.output_image_path && /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"textSecondary\",\n                  children: \"Image path missing\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 85\n                }, this), result.status === 'failed' && /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    textAlign: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(ErrorOutlineIcon, {\n                    color: \"error\",\n                    sx: {\n                      fontSize: 40\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 334,\n                    columnNumber: 87\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    color: \"error\",\n                    children: \"Failed\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 334,\n                    columnNumber: 143\n                  }, this), result.error_message && /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: result.error_message,\n                    children: /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"caption\",\n                      sx: {\n                        cursor: 'help'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(\"i\", {\n                        children: \"Details\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 334,\n                        columnNumber: 319\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 334,\n                      columnNumber: 267\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 334,\n                    columnNumber: 229\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 55\n                }, this), result.status === 'processing' && /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    textAlign: 'center'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n                    size: 30\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 335,\n                    columnNumber: 91\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      mt: 1\n                    },\n                    children: \"Processing...\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 335,\n                    columnNumber: 121\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 59\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 21\n              }, this), result.status === 'success' && /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 1,\n                  mt: 'auto'\n                },\n                children: [/*#__PURE__*/_jsxDEV(TextField, {\n                  label: \"Score (0-10)\",\n                  type: \"number\",\n                  variant: \"outlined\",\n                  size: \"small\",\n                  value: experimentScores[result.id] === undefined ? '' : experimentScores[result.id],\n                  onChange: e => handleScoreChange(result.id, e.target.value),\n                  error: !!scoreErrors[result.id],\n                  helperText: scoreErrors[result.id],\n                  inputProps: {\n                    min: 0,\n                    max: 10,\n                    step: 1\n                  },\n                  sx: {\n                    flexGrow: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Save Score\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      onClick: () => handleSaveScore(result.id),\n                      disabled: !!scoreErrors[result.id] || experimentScores[result.id] === undefined || String(experimentScores[result.id]).trim() === '',\n                      color: \"primary\",\n                      children: /*#__PURE__*/_jsxDEV(SaveIcon, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 340,\n                        columnNumber: 264\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 340,\n                      columnNumber: 59\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 340,\n                    columnNumber: 53\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 19\n            }, this)\n          }, result.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 11\n      }, this)\n    }, exp.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 314,\n      columnNumber: 9\n    }, this)), totalPages > 1 && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        mt: 3,\n        mb: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Pagination, {\n        count: totalPages,\n        page: currentPage,\n        onChange: handlePageChange,\n        color: \"primary\",\n        disabled: isLoadingExperiments\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 97\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 351,\n      columnNumber: 27\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 242,\n    columnNumber: 5\n  }, this);\n};\n_s(ResultsPage, \"wpbobbnAu5Hlwv1sKuiTHvs6bvs=\");\n_c = ResultsPage;\nexport default ResultsPage;\nvar _c;\n$RefreshReg$(_c, \"ResultsPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Container", "Typography", "FormControl", "InputLabel", "Select", "MenuItem", "Box", "<PERSON><PERSON>", "CircularProgress", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "TextField", "Pagination", "<PERSON><PERSON><PERSON>", "IconButton", "Modal", "Paper", "<PERSON><PERSON>", "getBbPictures", "getBbExperiments", "updateBbResultScore", "getBbExperimentConfigurations", "createBbExperiment", "SaveIcon", "ErrorOutlineIcon", "AddCircleOutlineIcon", "jsxDEV", "_jsxDEV", "modalStyle", "position", "top", "left", "transform", "width", "max<PERSON><PERSON><PERSON>", "bgcolor", "border", "boxShadow", "p", "OUTPUT_TYPES", "ResultsPage", "_s", "pictures", "setPictures", "selectedPictureId", "setSelectedPictureId", "experiments", "setExperiments", "isLoadingPictures", "setIsLoadingPictures", "isLoadingExperiments", "setIsLoadingExperiments", "error", "setError", "currentPage", "setCurrentPage", "totalPages", "setTotalPages", "totalExperiments", "setTotalExperiments", "experimentScores", "setExperimentScores", "scoreErrors", "setScoreErrors", "isNewExperimentModalOpen", "setIsNewExperimentModalOpen", "experimentConfigurations", "setExperimentConfigurations", "selectedConfiguration", "setSelectedConfiguration", "newExperimentPrompt", "setNewExperimentPrompt", "newExperimentResizeHeight", "setNewExperimentResizeHeight", "newExperimentResizeWidth", "setNewExperimentResizeWidth", "newExperimentOutputType", "setNewExperimentOutputType", "isCreatingExperiment", "setIsCreatingExperiment", "newExperimentError", "setNewExperimentError", "fetchPictures", "then", "data", "Array", "isArray", "console", "warn", "catch", "err", "finally", "fetchExperiments", "pictureId", "page", "initialScores", "for<PERSON>ach", "exp", "results", "res", "score", "undefined", "id", "prev", "fetchExpConfigs", "configs", "handlePictureChange", "event", "target", "value", "handlePageChange", "handleScoreChange", "resultId", "numValue", "Number", "isInteger", "handleSaveScore", "alert", "formatDate", "isoString", "Date", "toLocaleString", "handleOpenNewExperimentModal", "length", "handleCloseNewExperimentModal", "handleSelectedConfigurationChange", "configIndex", "config", "prompt", "resize_height", "resize_width", "output_type", "handleCreateNewExperiment", "trim", "experimentData", "picture_id", "parseInt", "newExp", "message", "sx", "mt", "mb", "children", "variant", "gutterBottom", "component", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "fullWidth", "labelId", "label", "onChange", "disabled", "map", "pic", "name", "textAlign", "display", "justifyContent", "color", "startIcon", "onClick", "open", "onClose", "spacing", "indexOf", "index", "substring", "e", "required", "container", "item", "xs", "type", "gap", "size", "my", "ml", "created_at", "flexWrap", "result", "sm", "md", "elevation", "height", "flexDirection", "model_name", "flexGrow", "alignItems", "minHeight", "status", "output_image_path", "src", "alt", "style", "maxHeight", "objectFit", "fontSize", "error_message", "title", "cursor", "helperText", "inputProps", "min", "max", "step", "String", "count", "_c", "$RefreshReg$"], "sources": ["D:/Documents/Programing/TRO/ModelTestsWorkbench/frontend/src/pages/boundingbox/ResultsPage.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\r\nimport {\r\n  Container,\r\n  Typography,\r\n  FormControl,\r\n  InputLabel,\r\n  Select,\r\n  MenuItem,\r\n  Box,\r\n  Button,\r\n  CircularProgress,\r\n  Alert,\r\n  Grid,\r\n  Card,\r\n  CardContent,\r\n  TextField,\r\n  Pagination,\r\n  Tooltip,\r\n  IconButton,\r\n  Modal, // Added for New Experiment Modal\r\n  Paper, // Added for Modal content styling\r\n  Stack // Added for Modal form layout\r\n} from '@mui/material';\r\nimport { getBbPictures, getBbExperiments, updateBbResultScore, getBbExperimentConfigurations, createBbExperiment } from '../../services/api_bounding_box';\r\nimport SaveIcon from '@mui/icons-material/Save';\r\nimport ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';\r\nimport AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline'; // For New Experiment button\r\n\r\n// Modal style\r\nconst modalStyle = {\r\n  position: 'absolute',\r\n  top: '50%',\r\n  left: '50%',\r\n  transform: 'translate(-50%, -50%)',\r\n  width: '90%',\r\n  maxWidth: 600,\r\n  bgcolor: 'background.paper',\r\n  border: '2px solid #000',\r\n  boxShadow: 24,\r\n  p: 4,\r\n};\r\n\r\nconst OUTPUT_TYPES = [\"Bounding Box\", \"Bounding Box + Segmentation Mask\"];\r\n\r\n\r\nconst ResultsPage = () => {\r\n  const [pictures, setPictures] = useState([]);\r\n  const [selectedPictureId, setSelectedPictureId] = useState('');\r\n  const [experiments, setExperiments] = useState([]);\r\n  const [isLoadingPictures, setIsLoadingPictures] = useState(false);\r\n  const [isLoadingExperiments, setIsLoadingExperiments] = useState(false);\r\n  const [error, setError] = useState(null);\r\n\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [totalPages, setTotalPages] = useState(0);\r\n  const [totalExperiments, setTotalExperiments] = useState(0);\r\n\r\n  const [experimentScores, setExperimentScores] = useState({});\r\n  const [scoreErrors, setScoreErrors] = useState({});\r\n\r\n  // State for New Experiment Modal\r\n  const [isNewExperimentModalOpen, setIsNewExperimentModalOpen] = useState(false);\r\n  const [experimentConfigurations, setExperimentConfigurations] = useState([]);\r\n  const [selectedConfiguration, setSelectedConfiguration] = useState(null); // Store the whole config object\r\n\r\n  const [newExperimentPrompt, setNewExperimentPrompt] = useState('');\r\n  const [newExperimentResizeHeight, setNewExperimentResizeHeight] = useState(512);\r\n  const [newExperimentResizeWidth, setNewExperimentResizeWidth] = useState(512);\r\n  const [newExperimentOutputType, setNewExperimentOutputType] = useState(OUTPUT_TYPES[0]);\r\n  const [isCreatingExperiment, setIsCreatingExperiment] = useState(false);\r\n  const [newExperimentError, setNewExperimentError] = useState('');\r\n\r\n\r\n  const fetchPictures = useCallback(() => {\r\n    setIsLoadingPictures(true);\r\n    setError(null);\r\n    getBbPictures()\r\n      .then(data => {\r\n        // Ensure data is always an array\r\n        if (Array.isArray(data)) {\r\n          setPictures(data);\r\n        } else {\r\n          console.warn('getBbPictures returned non-array data:', data);\r\n          setPictures([]);\r\n        }\r\n      })\r\n      .catch(err => {\r\n        console.error('Failed to load pictures:', err);\r\n        setError('Failed to load pictures.');\r\n        setPictures([]); // Ensure pictures is always an array\r\n      })\r\n      .finally(() => setIsLoadingPictures(false));\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    fetchPictures();\r\n  }, [fetchPictures]);\r\n\r\n  const fetchExperiments = useCallback((pictureId, page) => {\r\n    if (!pictureId) {\r\n        setExperiments([]); setTotalPages(0); setTotalExperiments(0); return;\r\n    };\r\n    setIsLoadingExperiments(true); setError(null);\r\n    getBbExperiments(pictureId, page)\r\n      .then(data => {\r\n        setExperiments(data.experiments || []);\r\n        setTotalPages(data.totalPages || 0);\r\n        setCurrentPage(data.currentPage || 1);\r\n        setTotalExperiments(data.totalExperiments || 0);\r\n        const initialScores = {};\r\n        (data.experiments || []).forEach(exp => (exp.results || []).forEach(res => {\r\n          if (res.score !== null && res.score !== undefined) initialScores[res.id] = res.score;\r\n        }));\r\n        setExperimentScores(prev => ({...prev, ...initialScores}));\r\n      })\r\n      .catch(err => {\r\n        setError(`Failed to load experiments.`);\r\n        setExperiments([]); setTotalPages(0); setTotalExperiments(0);\r\n      })\r\n      .finally(() => setIsLoadingExperiments(false));\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (selectedPictureId) fetchExperiments(selectedPictureId, currentPage);\r\n    else { setExperiments([]); setTotalPages(0); setTotalExperiments(0); }\r\n  }, [selectedPictureId, currentPage, fetchExperiments]);\r\n\r\n  const fetchExpConfigs = useCallback(async () => {\r\n    try {\r\n        const configs = await getBbExperimentConfigurations();\r\n        setExperimentConfigurations(configs || []);\r\n    } catch (err) {\r\n        console.error(\"Failed to fetch experiment configurations:\", err);\r\n        // Optionally set an error state for config loading\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    // Fetch configurations when the page loads or modal is about to open\r\n    // For simplicity, fetching on page load for now.\r\n    fetchExpConfigs();\r\n  }, [fetchExpConfigs]);\r\n\r\n\r\n  const handlePictureChange = (event) => {\r\n    setSelectedPictureId(event.target.value);\r\n    setCurrentPage(1); setError(null); setExperimentScores({}); setScoreErrors({});\r\n  };\r\n\r\n  const handlePageChange = (event, value) => setCurrentPage(value);\r\n\r\n  const handleScoreChange = (resultId, value) => {\r\n    const numValue = value === '' ? '' : Number(value);\r\n    setExperimentScores(prev => ({ ...prev, [resultId]: numValue }));\r\n    if (numValue === '' || (Number.isInteger(numValue) && numValue >= 0 && numValue <= 10)) {\r\n      setScoreErrors(prev => ({ ...prev, [resultId]: '' }));\r\n    } else {\r\n      setScoreErrors(prev => ({ ...prev, [resultId]: 'Score must be 0-10 (integer)' }));\r\n    }\r\n  };\r\n\r\n  const handleSaveScore = async (resultId) => {\r\n    const score = experimentScores[resultId];\r\n    if (score === undefined || score === '' || scoreErrors[resultId]) {\r\n      alert('Invalid score.'); return;\r\n    }\r\n    try {\r\n      await updateBbResultScore(resultId, Number(score));\r\n      alert(`Score for result ${resultId} saved (mocked).`);\r\n      // Consider optimistic update or refetch experiment for this result\r\n    } catch (error) { alert(`Failed to save score for result ${resultId}.`); }\r\n  };\r\n\r\n  const formatDate = (isoString) => isoString ? new Date(isoString).toLocaleString() : 'N/A';\r\n\r\n  // New Experiment Modal Handlers\r\n  const handleOpenNewExperimentModal = () => {\r\n    setNewExperimentError('');\r\n    setSelectedConfiguration(null); // Reset selected config\r\n    // Reset form fields to default or last selected (if desired)\r\n    setNewExperimentPrompt('');\r\n    setNewExperimentResizeHeight(512);\r\n    setNewExperimentResizeWidth(512);\r\n    setNewExperimentOutputType(OUTPUT_TYPES[0]);\r\n    setIsNewExperimentModalOpen(true);\r\n    if(experimentConfigurations.length === 0) fetchExpConfigs(); // Fetch if not already loaded\r\n  };\r\n\r\n  const handleCloseNewExperimentModal = () => setIsNewExperimentModalOpen(false);\r\n\r\n  const handleSelectedConfigurationChange = (event) => {\r\n    const configIndex = event.target.value; // This will be the index\r\n    if (configIndex === \"\" || configIndex === undefined) {\r\n        setSelectedConfiguration(null);\r\n        setNewExperimentPrompt('');\r\n        setNewExperimentResizeHeight(512);\r\n        setNewExperimentResizeWidth(512);\r\n        setNewExperimentOutputType(OUTPUT_TYPES[0]);\r\n    } else {\r\n        const config = experimentConfigurations[configIndex];\r\n        setSelectedConfiguration(config); // Store the whole config object\r\n        setNewExperimentPrompt(config.prompt);\r\n        setNewExperimentResizeHeight(config.resize_height);\r\n        setNewExperimentResizeWidth(config.resize_width);\r\n        setNewExperimentOutputType(config.output_type);\r\n    }\r\n  };\r\n\r\n  const handleCreateNewExperiment = async () => {\r\n    if (!newExperimentPrompt.trim()) {\r\n      setNewExperimentError(\"Prompt cannot be empty.\"); return;\r\n    }\r\n    if (newExperimentResizeHeight <= 0 || newExperimentResizeWidth <= 0) {\r\n      setNewExperimentError(\"Resize dimensions must be positive.\"); return;\r\n    }\r\n    setIsCreatingExperiment(true); setNewExperimentError('');\r\n    try {\r\n      const experimentData = {\r\n        picture_id: selectedPictureId,\r\n        prompt: newExperimentPrompt,\r\n        resize_height: parseInt(newExperimentResizeHeight, 10),\r\n        resize_width: parseInt(newExperimentResizeWidth, 10),\r\n        output_type: newExperimentOutputType,\r\n      };\r\n      const newExp = await createBbExperiment(experimentData);\r\n      // Add to the start of the experiments list for immediate feedback\r\n      setExperiments(prev => [newExp, ...prev]);\r\n      setTotalExperiments(prev => prev + 1);\r\n      // If new experiment pushes list to new page, this won't show it unless we adjust current page.\r\n      // For simplicity, we'll just add it. A full refresh might be better.\r\n      // Or, if on page 1 and list is not full, it appears.\r\n      // fetchExperiments(selectedPictureId, currentPage); // Or refetch current page.\r\n      handleCloseNewExperimentModal();\r\n    } catch (err) {\r\n      setNewExperimentError(`Failed to create experiment: ${err.message || 'Unknown error'}`);\r\n    } finally {\r\n      setIsCreatingExperiment(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Container maxWidth=\"xl\" sx={{ mt: 4, mb: 4 }}>\r\n      <Typography variant=\"h4\" gutterBottom component=\"h1\">Results Analysis by Product Pictures</Typography>\r\n      {error && <Alert severity=\"error\" sx={{ mb: 2 }}>{error}</Alert>}\r\n      <FormControl fullWidth sx={{ mb: 3 }}>\r\n        <InputLabel id=\"product-image-select-label\">Select Product Image</InputLabel>\r\n        <Select labelId=\"product-image-select-label\" value={selectedPictureId} label=\"Select Product Image\" onChange={handlePictureChange} disabled={isLoadingPictures || pictures.length === 0}>\r\n          {isLoadingPictures && <MenuItem value=\"\" disabled><em>Loading images...</em></MenuItem>}\r\n          {!isLoadingPictures && pictures.length === 0 && <MenuItem value=\"\" disabled><em>No pictures available.</em></MenuItem>}\r\n          {Array.isArray(pictures) && pictures.map((pic) => (<MenuItem key={pic.id} value={pic.id}>{pic.name} (ID: {pic.id})</MenuItem>))}\r\n        </Select>\r\n      </FormControl>\r\n\r\n      {!isLoadingPictures && pictures.length === 0 && !error && (<Typography variant=\"body1\" sx={{ mb: 2, textAlign: 'center' }}>No product pictures found.</Typography>)}\r\n\r\n      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>\r\n        <Button variant=\"contained\" color=\"primary\" startIcon={<AddCircleOutlineIcon />} onClick={handleOpenNewExperimentModal} disabled={!selectedPictureId || isLoadingPictures || isLoadingExperiments}>\r\n          + New Experiment\r\n        </Button>\r\n      </Box>\r\n\r\n      {/* New Experiment Modal */}\r\n      <Modal open={isNewExperimentModalOpen} onClose={handleCloseNewExperimentModal} aria-labelledby=\"new-experiment-modal-title\">\r\n        <Paper sx={modalStyle}>\r\n          <Typography id=\"new-experiment-modal-title\" variant=\"h6\" component=\"h2\" gutterBottom>Create New Experiment</Typography>\r\n          {newExperimentError && <Alert severity=\"error\" sx={{mb:2}}>{newExperimentError}</Alert>}\r\n          <Stack spacing={2}>\r\n            <FormControl fullWidth>\r\n              <InputLabel id=\"select-config-label\">Select Existing Configuration (Optional)</InputLabel>\r\n              <Select\r\n                labelId=\"select-config-label\"\r\n                value={selectedConfiguration ? experimentConfigurations.indexOf(selectedConfiguration) : \"\"}\r\n                label=\"Select Existing Configuration (Optional)\"\r\n                onChange={handleSelectedConfigurationChange}\r\n              >\r\n                <MenuItem value=\"\"><em>None - Manual Input</em></MenuItem>\r\n                {experimentConfigurations.map((config, index) => (\r\n                  <MenuItem key={index} value={index}>\r\n                    Prompt: {config.prompt.substring(0,30)}... | Size: {config.resize_width}x{config.resize_height} | Type: {config.output_type}\r\n                  </MenuItem>\r\n                ))}\r\n              </Select>\r\n            </FormControl>\r\n            <TextField label=\"Prompt\" value={newExperimentPrompt} onChange={(e) => setNewExperimentPrompt(e.target.value)} fullWidth required />\r\n            <Grid container spacing={2}>\r\n                <Grid item xs={6}>\r\n                    <TextField label=\"Resize Height\" type=\"number\" value={newExperimentResizeHeight} onChange={(e) => setNewExperimentResizeHeight(Number(e.target.value))} fullWidth required />\r\n                </Grid>\r\n                <Grid item xs={6}>\r\n                    <TextField label=\"Resize Width\" type=\"number\" value={newExperimentResizeWidth} onChange={(e) => setNewExperimentResizeWidth(Number(e.target.value))} fullWidth required />\r\n                </Grid>\r\n            </Grid>\r\n            <FormControl fullWidth required>\r\n                <InputLabel id=\"output-type-label\">Output Type</InputLabel>\r\n                <Select labelId=\"output-type-label\" value={newExperimentOutputType} label=\"Output Type\" onChange={(e) => setNewExperimentOutputType(e.target.value)}>\r\n                    {OUTPUT_TYPES.map(type => (<MenuItem key={type} value={type}>{type}</MenuItem>))}\r\n                </Select>\r\n            </FormControl>\r\n            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1, mt: 2 }}>\r\n              <Button onClick={handleCloseNewExperimentModal} disabled={isCreatingExperiment}>Cancel</Button>\r\n              <Button variant=\"contained\" onClick={handleCreateNewExperiment} disabled={isCreatingExperiment || !newExperimentPrompt.trim() || newExperimentResizeHeight <=0 || newExperimentResizeWidth <=0}>\r\n                {isCreatingExperiment ? <CircularProgress size={24} /> : \"Add\"}\r\n              </Button>\r\n            </Box>\r\n          </Stack>\r\n        </Paper>\r\n      </Modal>\r\n\r\n      {/* Experiment List */}\r\n      {isLoadingExperiments && <Box sx={{display: 'flex', justifyContent: 'center', my: 3}}><CircularProgress /></Box>}\r\n      {!isLoadingExperiments && selectedPictureId && experiments.length === 0 && !error && (<Typography variant=\"body1\" sx={{ textAlign: 'center', mt: 2 }}>No experiments found. Create one!</Typography>)}\r\n\r\n      {experiments.map((exp, index) => (\r\n        <Card key={exp.id} sx={{ mb: 3 }}>\r\n          <CardContent>\r\n            <Typography variant=\"h6\" gutterBottom>\r\n              Experiment {totalExperiments - ((currentPage - 1) * 2) - index} (ID: {exp.id.substring(0,8)}...)\r\n              <Typography variant=\"caption\" sx={{ml: 1}}>Created: {formatDate(exp.created_at)}</Typography>\r\n            </Typography>\r\n            <Box sx={{ display: 'flex', gap: 3, mb: 2, flexWrap: 'wrap' }}>\r\n              <Typography variant=\"body2\"><strong>Prompt:</strong> {exp.prompt}</Typography>\r\n              <Typography variant=\"body2\"><strong>Dimensions:</strong> {exp.resize_width}w x {exp.resize_height}h</Typography>\r\n              <Typography variant=\"body2\"><strong>Output Type:</strong> {exp.output_type}</Typography>\r\n            </Box>\r\n            <Typography variant=\"subtitle1\" sx={{mt: 2, mb: 1}}>Model Outputs:</Typography>\r\n            <Grid container spacing={2}>\r\n              {(exp.results || []).map((result) => (\r\n                <Grid item xs={12} sm={6} md={4} key={result.id}>\r\n                  <Paper elevation={2} sx={{ p: 2, height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>\r\n                    <Typography variant=\"subtitle2\" gutterBottom>{result.model_name}</Typography>\r\n                    <Box sx={{ flexGrow: 1, display: 'flex', flexDirection:'column', alignItems: 'center', justifyContent: 'center', minHeight: 180, mb:1 }}>\r\n                      {result.status === 'success' && result.output_image_path && (<img src={result.output_image_path} alt={`Output for ${result.model_name}`} style={{ maxWidth: '100%', maxHeight: '150px', objectFit: 'contain' }} />)}\r\n                      {result.status === 'success' && !result.output_image_path && (<Typography variant=\"caption\" color=\"textSecondary\">Image path missing</Typography>)}\r\n                      {result.status === 'failed' && (<Box sx={{textAlign: 'center'}}><ErrorOutlineIcon color=\"error\" sx={{ fontSize: 40 }} /><Typography variant=\"body2\" color=\"error\">Failed</Typography>{result.error_message && <Tooltip title={result.error_message}><Typography variant=\"caption\" sx={{cursor: 'help'}}><i>Details</i></Typography></Tooltip>}</Box>)}\r\n                      {result.status === 'processing' && (<Box sx={{textAlign: 'center'}}><CircularProgress size={30} /><Typography variant=\"body2\" sx={{mt:1}}>Processing...</Typography></Box>)}\r\n                    </Box>\r\n                    {result.status === 'success' && (\r\n                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 'auto' }}>\r\n                        <TextField label=\"Score (0-10)\" type=\"number\" variant=\"outlined\" size=\"small\" value={experimentScores[result.id] === undefined ? '' : experimentScores[result.id]} onChange={(e) => handleScoreChange(result.id, e.target.value)} error={!!scoreErrors[result.id]} helperText={scoreErrors[result.id]} inputProps={{ min: 0, max: 10, step: 1 }} sx={{ flexGrow: 1 }}/>\r\n                        <Tooltip title=\"Save Score\"><span><IconButton onClick={() => handleSaveScore(result.id)} disabled={!!scoreErrors[result.id] || experimentScores[result.id] === undefined || String(experimentScores[result.id]).trim() === ''} color=\"primary\"><SaveIcon /></IconButton></span></Tooltip>\r\n                      </Box>\r\n                    )}\r\n                  </Paper>\r\n                </Grid>\r\n              ))}\r\n            </Grid>\r\n          </CardContent>\r\n        </Card>\r\n      ))}\r\n\r\n      {totalPages > 1 && (<Box sx={{ display: 'flex', justifyContent: 'center', mt: 3, mb: 2 }}><Pagination count={totalPages} page={currentPage} onChange={handlePageChange} color=\"primary\" disabled={isLoadingExperiments}/></Box>)}\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default ResultsPage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SACEC,SAAS,EACTC,UAAU,EACVC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,GAAG,EACHC,MAAM,EACNC,gBAAgB,EAChBC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,SAAS,EACTC,UAAU,EACVC,OAAO,EACPC,UAAU,EACVC,KAAK;AAAE;AACPC,KAAK;AAAE;AACPC,KAAK,CAAC;AAAA,OACD,eAAe;AACtB,SAASC,aAAa,EAAEC,gBAAgB,EAAEC,mBAAmB,EAAEC,6BAA6B,EAAEC,kBAAkB,QAAQ,iCAAiC;AACzJ,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,oBAAoB,MAAM,sCAAsC,CAAC,CAAC;;AAEzE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,UAAU,GAAG;EACjBC,QAAQ,EAAE,UAAU;EACpBC,GAAG,EAAE,KAAK;EACVC,IAAI,EAAE,KAAK;EACXC,SAAS,EAAE,uBAAuB;EAClCC,KAAK,EAAE,KAAK;EACZC,QAAQ,EAAE,GAAG;EACbC,OAAO,EAAE,kBAAkB;EAC3BC,MAAM,EAAE,gBAAgB;EACxBC,SAAS,EAAE,EAAE;EACbC,CAAC,EAAE;AACL,CAAC;AAED,MAAMC,YAAY,GAAG,CAAC,cAAc,EAAE,kCAAkC,CAAC;AAGzE,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlD,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAACmD,WAAW,EAAEC,cAAc,CAAC,GAAGpD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACqD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACuD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACyD,KAAK,EAAEC,QAAQ,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EAExC,MAAM,CAAC2D,WAAW,EAAEC,cAAc,CAAC,GAAG5D,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC6D,UAAU,EAAEC,aAAa,CAAC,GAAG9D,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC+D,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhE,QAAQ,CAAC,CAAC,CAAC;EAE3D,MAAM,CAACiE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACmE,WAAW,EAAEC,cAAc,CAAC,GAAGpE,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAElD;EACA,MAAM,CAACqE,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EAC/E,MAAM,CAACuE,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGxE,QAAQ,CAAC,EAAE,CAAC;EAC5E,MAAM,CAACyE,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG1E,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;;EAE1E,MAAM,CAAC2E,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EAClE,MAAM,CAAC6E,yBAAyB,EAAEC,4BAA4B,CAAC,GAAG9E,QAAQ,CAAC,GAAG,CAAC;EAC/E,MAAM,CAAC+E,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGhF,QAAQ,CAAC,GAAG,CAAC;EAC7E,MAAM,CAACiF,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGlF,QAAQ,CAAC4C,YAAY,CAAC,CAAC,CAAC,CAAC;EACvF,MAAM,CAACuC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGpF,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACqF,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtF,QAAQ,CAAC,EAAE,CAAC;EAGhE,MAAMuF,aAAa,GAAGrF,WAAW,CAAC,MAAM;IACtCoD,oBAAoB,CAAC,IAAI,CAAC;IAC1BI,QAAQ,CAAC,IAAI,CAAC;IACdnC,aAAa,CAAC,CAAC,CACZiE,IAAI,CAACC,IAAI,IAAI;MACZ;MACA,IAAIC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE;QACvBzC,WAAW,CAACyC,IAAI,CAAC;MACnB,CAAC,MAAM;QACLG,OAAO,CAACC,IAAI,CAAC,wCAAwC,EAAEJ,IAAI,CAAC;QAC5DzC,WAAW,CAAC,EAAE,CAAC;MACjB;IACF,CAAC,CAAC,CACD8C,KAAK,CAACC,GAAG,IAAI;MACZH,OAAO,CAACnC,KAAK,CAAC,0BAA0B,EAAEsC,GAAG,CAAC;MAC9CrC,QAAQ,CAAC,0BAA0B,CAAC;MACpCV,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CACDgD,OAAO,CAAC,MAAM1C,oBAAoB,CAAC,KAAK,CAAC,CAAC;EAC/C,CAAC,EAAE,EAAE,CAAC;EAENrD,SAAS,CAAC,MAAM;IACdsF,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EAEnB,MAAMU,gBAAgB,GAAG/F,WAAW,CAAC,CAACgG,SAAS,EAAEC,IAAI,KAAK;IACxD,IAAI,CAACD,SAAS,EAAE;MACZ9C,cAAc,CAAC,EAAE,CAAC;MAAEU,aAAa,CAAC,CAAC,CAAC;MAAEE,mBAAmB,CAAC,CAAC,CAAC;MAAE;IAClE;IAAC;IACDR,uBAAuB,CAAC,IAAI,CAAC;IAAEE,QAAQ,CAAC,IAAI,CAAC;IAC7ClC,gBAAgB,CAAC0E,SAAS,EAAEC,IAAI,CAAC,CAC9BX,IAAI,CAACC,IAAI,IAAI;MACZrC,cAAc,CAACqC,IAAI,CAACtC,WAAW,IAAI,EAAE,CAAC;MACtCW,aAAa,CAAC2B,IAAI,CAAC5B,UAAU,IAAI,CAAC,CAAC;MACnCD,cAAc,CAAC6B,IAAI,CAAC9B,WAAW,IAAI,CAAC,CAAC;MACrCK,mBAAmB,CAACyB,IAAI,CAAC1B,gBAAgB,IAAI,CAAC,CAAC;MAC/C,MAAMqC,aAAa,GAAG,CAAC,CAAC;MACxB,CAACX,IAAI,CAACtC,WAAW,IAAI,EAAE,EAAEkD,OAAO,CAACC,GAAG,IAAI,CAACA,GAAG,CAACC,OAAO,IAAI,EAAE,EAAEF,OAAO,CAACG,GAAG,IAAI;QACzE,IAAIA,GAAG,CAACC,KAAK,KAAK,IAAI,IAAID,GAAG,CAACC,KAAK,KAAKC,SAAS,EAAEN,aAAa,CAACI,GAAG,CAACG,EAAE,CAAC,GAAGH,GAAG,CAACC,KAAK;MACtF,CAAC,CAAC,CAAC;MACHvC,mBAAmB,CAAC0C,IAAI,KAAK;QAAC,GAAGA,IAAI;QAAE,GAAGR;MAAa,CAAC,CAAC,CAAC;IAC5D,CAAC,CAAC,CACDN,KAAK,CAACC,GAAG,IAAI;MACZrC,QAAQ,CAAC,6BAA6B,CAAC;MACvCN,cAAc,CAAC,EAAE,CAAC;MAAEU,aAAa,CAAC,CAAC,CAAC;MAAEE,mBAAmB,CAAC,CAAC,CAAC;IAC9D,CAAC,CAAC,CACDgC,OAAO,CAAC,MAAMxC,uBAAuB,CAAC,KAAK,CAAC,CAAC;EAClD,CAAC,EAAE,EAAE,CAAC;EAENvD,SAAS,CAAC,MAAM;IACd,IAAIgD,iBAAiB,EAAEgD,gBAAgB,CAAChD,iBAAiB,EAAEU,WAAW,CAAC,CAAC,KACnE;MAAEP,cAAc,CAAC,EAAE,CAAC;MAAEU,aAAa,CAAC,CAAC,CAAC;MAAEE,mBAAmB,CAAC,CAAC,CAAC;IAAE;EACvE,CAAC,EAAE,CAACf,iBAAiB,EAAEU,WAAW,EAAEsC,gBAAgB,CAAC,CAAC;EAEtD,MAAMY,eAAe,GAAG3G,WAAW,CAAC,YAAY;IAC9C,IAAI;MACA,MAAM4G,OAAO,GAAG,MAAMpF,6BAA6B,CAAC,CAAC;MACrD8C,2BAA2B,CAACsC,OAAO,IAAI,EAAE,CAAC;IAC9C,CAAC,CAAC,OAAOf,GAAG,EAAE;MACVH,OAAO,CAACnC,KAAK,CAAC,4CAA4C,EAAEsC,GAAG,CAAC;MAChE;IACJ;EACF,CAAC,EAAE,EAAE,CAAC;EAEN9F,SAAS,CAAC,MAAM;IACd;IACA;IACA4G,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;EAGrB,MAAME,mBAAmB,GAAIC,KAAK,IAAK;IACrC9D,oBAAoB,CAAC8D,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;IACxCtD,cAAc,CAAC,CAAC,CAAC;IAAEF,QAAQ,CAAC,IAAI,CAAC;IAAEQ,mBAAmB,CAAC,CAAC,CAAC,CAAC;IAAEE,cAAc,CAAC,CAAC,CAAC,CAAC;EAChF,CAAC;EAED,MAAM+C,gBAAgB,GAAGA,CAACH,KAAK,EAAEE,KAAK,KAAKtD,cAAc,CAACsD,KAAK,CAAC;EAEhE,MAAME,iBAAiB,GAAGA,CAACC,QAAQ,EAAEH,KAAK,KAAK;IAC7C,MAAMI,QAAQ,GAAGJ,KAAK,KAAK,EAAE,GAAG,EAAE,GAAGK,MAAM,CAACL,KAAK,CAAC;IAClDhD,mBAAmB,CAAC0C,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACS,QAAQ,GAAGC;IAAS,CAAC,CAAC,CAAC;IAChE,IAAIA,QAAQ,KAAK,EAAE,IAAKC,MAAM,CAACC,SAAS,CAACF,QAAQ,CAAC,IAAIA,QAAQ,IAAI,CAAC,IAAIA,QAAQ,IAAI,EAAG,EAAE;MACtFlD,cAAc,CAACwC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACS,QAAQ,GAAG;MAAG,CAAC,CAAC,CAAC;IACvD,CAAC,MAAM;MACLjD,cAAc,CAACwC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACS,QAAQ,GAAG;MAA+B,CAAC,CAAC,CAAC;IACnF;EACF,CAAC;EAED,MAAMI,eAAe,GAAG,MAAOJ,QAAQ,IAAK;IAC1C,MAAMZ,KAAK,GAAGxC,gBAAgB,CAACoD,QAAQ,CAAC;IACxC,IAAIZ,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,EAAE,IAAItC,WAAW,CAACkD,QAAQ,CAAC,EAAE;MAChEK,KAAK,CAAC,gBAAgB,CAAC;MAAE;IAC3B;IACA,IAAI;MACF,MAAMjG,mBAAmB,CAAC4F,QAAQ,EAAEE,MAAM,CAACd,KAAK,CAAC,CAAC;MAClDiB,KAAK,CAAC,oBAAoBL,QAAQ,kBAAkB,CAAC;MACrD;IACF,CAAC,CAAC,OAAO5D,KAAK,EAAE;MAAEiE,KAAK,CAAC,mCAAmCL,QAAQ,GAAG,CAAC;IAAE;EAC3E,CAAC;EAED,MAAMM,UAAU,GAAIC,SAAS,IAAKA,SAAS,GAAG,IAAIC,IAAI,CAACD,SAAS,CAAC,CAACE,cAAc,CAAC,CAAC,GAAG,KAAK;;EAE1F;EACA,MAAMC,4BAA4B,GAAGA,CAAA,KAAM;IACzCzC,qBAAqB,CAAC,EAAE,CAAC;IACzBZ,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC;IAChC;IACAE,sBAAsB,CAAC,EAAE,CAAC;IAC1BE,4BAA4B,CAAC,GAAG,CAAC;IACjCE,2BAA2B,CAAC,GAAG,CAAC;IAChCE,0BAA0B,CAACtC,YAAY,CAAC,CAAC,CAAC,CAAC;IAC3C0B,2BAA2B,CAAC,IAAI,CAAC;IACjC,IAAGC,wBAAwB,CAACyD,MAAM,KAAK,CAAC,EAAEnB,eAAe,CAAC,CAAC,CAAC,CAAC;EAC/D,CAAC;EAED,MAAMoB,6BAA6B,GAAGA,CAAA,KAAM3D,2BAA2B,CAAC,KAAK,CAAC;EAE9E,MAAM4D,iCAAiC,GAAIlB,KAAK,IAAK;IACnD,MAAMmB,WAAW,GAAGnB,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC;IACxC,IAAIiB,WAAW,KAAK,EAAE,IAAIA,WAAW,KAAKzB,SAAS,EAAE;MACjDhC,wBAAwB,CAAC,IAAI,CAAC;MAC9BE,sBAAsB,CAAC,EAAE,CAAC;MAC1BE,4BAA4B,CAAC,GAAG,CAAC;MACjCE,2BAA2B,CAAC,GAAG,CAAC;MAChCE,0BAA0B,CAACtC,YAAY,CAAC,CAAC,CAAC,CAAC;IAC/C,CAAC,MAAM;MACH,MAAMwF,MAAM,GAAG7D,wBAAwB,CAAC4D,WAAW,CAAC;MACpDzD,wBAAwB,CAAC0D,MAAM,CAAC,CAAC,CAAC;MAClCxD,sBAAsB,CAACwD,MAAM,CAACC,MAAM,CAAC;MACrCvD,4BAA4B,CAACsD,MAAM,CAACE,aAAa,CAAC;MAClDtD,2BAA2B,CAACoD,MAAM,CAACG,YAAY,CAAC;MAChDrD,0BAA0B,CAACkD,MAAM,CAACI,WAAW,CAAC;IAClD;EACF,CAAC;EAED,MAAMC,yBAAyB,GAAG,MAAAA,CAAA,KAAY;IAC5C,IAAI,CAAC9D,mBAAmB,CAAC+D,IAAI,CAAC,CAAC,EAAE;MAC/BpD,qBAAqB,CAAC,yBAAyB,CAAC;MAAE;IACpD;IACA,IAAIT,yBAAyB,IAAI,CAAC,IAAIE,wBAAwB,IAAI,CAAC,EAAE;MACnEO,qBAAqB,CAAC,qCAAqC,CAAC;MAAE;IAChE;IACAF,uBAAuB,CAAC,IAAI,CAAC;IAAEE,qBAAqB,CAAC,EAAE,CAAC;IACxD,IAAI;MACF,MAAMqD,cAAc,GAAG;QACrBC,UAAU,EAAE3F,iBAAiB;QAC7BoF,MAAM,EAAE1D,mBAAmB;QAC3B2D,aAAa,EAAEO,QAAQ,CAAChE,yBAAyB,EAAE,EAAE,CAAC;QACtD0D,YAAY,EAAEM,QAAQ,CAAC9D,wBAAwB,EAAE,EAAE,CAAC;QACpDyD,WAAW,EAAEvD;MACf,CAAC;MACD,MAAM6D,MAAM,GAAG,MAAMnH,kBAAkB,CAACgH,cAAc,CAAC;MACvD;MACAvF,cAAc,CAACwD,IAAI,IAAI,CAACkC,MAAM,EAAE,GAAGlC,IAAI,CAAC,CAAC;MACzC5C,mBAAmB,CAAC4C,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACrC;MACA;MACA;MACA;MACAqB,6BAA6B,CAAC,CAAC;IACjC,CAAC,CAAC,OAAOlC,GAAG,EAAE;MACZT,qBAAqB,CAAC,gCAAgCS,GAAG,CAACgD,OAAO,IAAI,eAAe,EAAE,CAAC;IACzF,CAAC,SAAS;MACR3D,uBAAuB,CAAC,KAAK,CAAC;IAChC;EACF,CAAC;EAED,oBACEpD,OAAA,CAAC7B,SAAS;IAACoC,QAAQ,EAAC,IAAI;IAACyG,EAAE,EAAE;MAAEC,EAAE,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAC5CnH,OAAA,CAAC5B,UAAU;MAACgJ,OAAO,EAAC,IAAI;MAACC,YAAY;MAACC,SAAS,EAAC,IAAI;MAAAH,QAAA,EAAC;IAAoC;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,EACrGjG,KAAK,iBAAIzB,OAAA,CAACpB,KAAK;MAAC+I,QAAQ,EAAC,OAAO;MAACX,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,EAAE1F;IAAK;MAAA8F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAChE1H,OAAA,CAAC3B,WAAW;MAACuJ,SAAS;MAACZ,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,gBACnCnH,OAAA,CAAC1B,UAAU;QAACqG,EAAE,EAAC,4BAA4B;QAAAwC,QAAA,EAAC;MAAoB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC7E1H,OAAA,CAACzB,MAAM;QAACsJ,OAAO,EAAC,4BAA4B;QAAC3C,KAAK,EAAEjE,iBAAkB;QAAC6G,KAAK,EAAC,sBAAsB;QAACC,QAAQ,EAAEhD,mBAAoB;QAACiD,QAAQ,EAAE3G,iBAAiB,IAAIN,QAAQ,CAACiF,MAAM,KAAK,CAAE;QAAAmB,QAAA,GACrL9F,iBAAiB,iBAAIrB,OAAA,CAACxB,QAAQ;UAAC0G,KAAK,EAAC,EAAE;UAAC8C,QAAQ;UAAAb,QAAA,eAACnH,OAAA;YAAAmH,QAAA,EAAI;UAAiB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,EACtF,CAACrG,iBAAiB,IAAIN,QAAQ,CAACiF,MAAM,KAAK,CAAC,iBAAIhG,OAAA,CAACxB,QAAQ;UAAC0G,KAAK,EAAC,EAAE;UAAC8C,QAAQ;UAAAb,QAAA,eAACnH,OAAA;YAAAmH,QAAA,EAAI;UAAsB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAU,CAAC,EACrHhE,KAAK,CAACC,OAAO,CAAC5C,QAAQ,CAAC,IAAIA,QAAQ,CAACkH,GAAG,CAAEC,GAAG,iBAAMlI,OAAA,CAACxB,QAAQ;UAAc0G,KAAK,EAAEgD,GAAG,CAACvD,EAAG;UAAAwC,QAAA,GAAEe,GAAG,CAACC,IAAI,EAAC,QAAM,EAACD,GAAG,CAACvD,EAAE,EAAC,GAAC;QAAA,GAAhDuD,GAAG,CAACvD,EAAE;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAoD,CAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAEb,CAACrG,iBAAiB,IAAIN,QAAQ,CAACiF,MAAM,KAAK,CAAC,IAAI,CAACvE,KAAK,iBAAKzB,OAAA,CAAC5B,UAAU;MAACgJ,OAAO,EAAC,OAAO;MAACJ,EAAE,EAAE;QAAEE,EAAE,EAAE,CAAC;QAAEkB,SAAS,EAAE;MAAS,CAAE;MAAAjB,QAAA,EAAC;IAA0B;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAE,eAEnK1H,OAAA,CAACvB,GAAG;MAACuI,EAAE,EAAE;QAAEqB,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,UAAU;QAAEpB,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,eAC9DnH,OAAA,CAACtB,MAAM;QAAC0I,OAAO,EAAC,WAAW;QAACmB,KAAK,EAAC,SAAS;QAACC,SAAS,eAAExI,OAAA,CAACF,oBAAoB;UAAAyH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAACe,OAAO,EAAE1C,4BAA6B;QAACiC,QAAQ,EAAE,CAAC/G,iBAAiB,IAAII,iBAAiB,IAAIE,oBAAqB;QAAA4F,QAAA,EAAC;MAEnM;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN1H,OAAA,CAACZ,KAAK;MAACsJ,IAAI,EAAErG,wBAAyB;MAACsG,OAAO,EAAE1C,6BAA8B;MAAC,mBAAgB,4BAA4B;MAAAkB,QAAA,eACzHnH,OAAA,CAACX,KAAK;QAAC2H,EAAE,EAAE/G,UAAW;QAAAkH,QAAA,gBACpBnH,OAAA,CAAC5B,UAAU;UAACuG,EAAE,EAAC,4BAA4B;UAACyC,OAAO,EAAC,IAAI;UAACE,SAAS,EAAC,IAAI;UAACD,YAAY;UAAAF,QAAA,EAAC;QAAqB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EACtHrE,kBAAkB,iBAAIrD,OAAA,CAACpB,KAAK;UAAC+I,QAAQ,EAAC,OAAO;UAACX,EAAE,EAAE;YAACE,EAAE,EAAC;UAAC,CAAE;UAAAC,QAAA,EAAE9D;QAAkB;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACvF1H,OAAA,CAACV,KAAK;UAACsJ,OAAO,EAAE,CAAE;UAAAzB,QAAA,gBAChBnH,OAAA,CAAC3B,WAAW;YAACuJ,SAAS;YAAAT,QAAA,gBACpBnH,OAAA,CAAC1B,UAAU;cAACqG,EAAE,EAAC,qBAAqB;cAAAwC,QAAA,EAAC;YAAwC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC1F1H,OAAA,CAACzB,MAAM;cACLsJ,OAAO,EAAC,qBAAqB;cAC7B3C,KAAK,EAAEzC,qBAAqB,GAAGF,wBAAwB,CAACsG,OAAO,CAACpG,qBAAqB,CAAC,GAAG,EAAG;cAC5FqF,KAAK,EAAC,0CAA0C;cAChDC,QAAQ,EAAE7B,iCAAkC;cAAAiB,QAAA,gBAE5CnH,OAAA,CAACxB,QAAQ;gBAAC0G,KAAK,EAAC,EAAE;gBAAAiC,QAAA,eAACnH,OAAA;kBAAAmH,QAAA,EAAI;gBAAmB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,EACzDnF,wBAAwB,CAAC0F,GAAG,CAAC,CAAC7B,MAAM,EAAE0C,KAAK,kBAC1C9I,OAAA,CAACxB,QAAQ;gBAAa0G,KAAK,EAAE4D,KAAM;gBAAA3B,QAAA,GAAC,UAC1B,EAACf,MAAM,CAACC,MAAM,CAAC0C,SAAS,CAAC,CAAC,EAAC,EAAE,CAAC,EAAC,cAAY,EAAC3C,MAAM,CAACG,YAAY,EAAC,GAAC,EAACH,MAAM,CAACE,aAAa,EAAC,WAAS,EAACF,MAAM,CAACI,WAAW;cAAA,GAD9GsC,KAAK;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEV,CACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACd1H,OAAA,CAAChB,SAAS;YAAC8I,KAAK,EAAC,QAAQ;YAAC5C,KAAK,EAAEvC,mBAAoB;YAACoF,QAAQ,EAAGiB,CAAC,IAAKpG,sBAAsB,CAACoG,CAAC,CAAC/D,MAAM,CAACC,KAAK,CAAE;YAAC0C,SAAS;YAACqB,QAAQ;UAAA;YAAA1B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpI1H,OAAA,CAACnB,IAAI;YAACqK,SAAS;YAACN,OAAO,EAAE,CAAE;YAAAzB,QAAA,gBACvBnH,OAAA,CAACnB,IAAI;cAACsK,IAAI;cAACC,EAAE,EAAE,CAAE;cAAAjC,QAAA,eACbnH,OAAA,CAAChB,SAAS;gBAAC8I,KAAK,EAAC,eAAe;gBAACuB,IAAI,EAAC,QAAQ;gBAACnE,KAAK,EAAErC,yBAA0B;gBAACkF,QAAQ,EAAGiB,CAAC,IAAKlG,4BAA4B,CAACyC,MAAM,CAACyD,CAAC,CAAC/D,MAAM,CAACC,KAAK,CAAC,CAAE;gBAAC0C,SAAS;gBAACqB,QAAQ;cAAA;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3K,CAAC,eACP1H,OAAA,CAACnB,IAAI;cAACsK,IAAI;cAACC,EAAE,EAAE,CAAE;cAAAjC,QAAA,eACbnH,OAAA,CAAChB,SAAS;gBAAC8I,KAAK,EAAC,cAAc;gBAACuB,IAAI,EAAC,QAAQ;gBAACnE,KAAK,EAAEnC,wBAAyB;gBAACgF,QAAQ,EAAGiB,CAAC,IAAKhG,2BAA2B,CAACuC,MAAM,CAACyD,CAAC,CAAC/D,MAAM,CAACC,KAAK,CAAC,CAAE;gBAAC0C,SAAS;gBAACqB,QAAQ;cAAA;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACP1H,OAAA,CAAC3B,WAAW;YAACuJ,SAAS;YAACqB,QAAQ;YAAA9B,QAAA,gBAC3BnH,OAAA,CAAC1B,UAAU;cAACqG,EAAE,EAAC,mBAAmB;cAAAwC,QAAA,EAAC;YAAW;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC3D1H,OAAA,CAACzB,MAAM;cAACsJ,OAAO,EAAC,mBAAmB;cAAC3C,KAAK,EAAEjC,uBAAwB;cAAC6E,KAAK,EAAC,aAAa;cAACC,QAAQ,EAAGiB,CAAC,IAAK9F,0BAA0B,CAAC8F,CAAC,CAAC/D,MAAM,CAACC,KAAK,CAAE;cAAAiC,QAAA,EAC/IvG,YAAY,CAACqH,GAAG,CAACoB,IAAI,iBAAKrJ,OAAA,CAACxB,QAAQ;gBAAY0G,KAAK,EAAEmE,IAAK;gBAAAlC,QAAA,EAAEkC;cAAI,GAAxBA,IAAI;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA+B,CAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5E,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACd1H,OAAA,CAACvB,GAAG;YAACuI,EAAE,EAAE;cAAEqB,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE,UAAU;cAAEgB,GAAG,EAAE,CAAC;cAAErC,EAAE,EAAE;YAAE,CAAE;YAAAE,QAAA,gBACtEnH,OAAA,CAACtB,MAAM;cAAC+J,OAAO,EAAExC,6BAA8B;cAAC+B,QAAQ,EAAE7E,oBAAqB;cAAAgE,QAAA,EAAC;YAAM;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC/F1H,OAAA,CAACtB,MAAM;cAAC0I,OAAO,EAAC,WAAW;cAACqB,OAAO,EAAEhC,yBAA0B;cAACuB,QAAQ,EAAE7E,oBAAoB,IAAI,CAACR,mBAAmB,CAAC+D,IAAI,CAAC,CAAC,IAAI7D,yBAAyB,IAAG,CAAC,IAAIE,wBAAwB,IAAG,CAAE;cAAAoE,QAAA,EAC5LhE,oBAAoB,gBAAGnD,OAAA,CAACrB,gBAAgB;gBAAC4K,IAAI,EAAE;cAAG;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,GAAG;YAAK;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGPnG,oBAAoB,iBAAIvB,OAAA,CAACvB,GAAG;MAACuI,EAAE,EAAE;QAACqB,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEkB,EAAE,EAAE;MAAC,CAAE;MAAArC,QAAA,eAACnH,OAAA,CAACrB,gBAAgB;QAAA4I,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,EAC/G,CAACnG,oBAAoB,IAAIN,iBAAiB,IAAIE,WAAW,CAAC6E,MAAM,KAAK,CAAC,IAAI,CAACvE,KAAK,iBAAKzB,OAAA,CAAC5B,UAAU;MAACgJ,OAAO,EAAC,OAAO;MAACJ,EAAE,EAAE;QAAEoB,SAAS,EAAE,QAAQ;QAAEnB,EAAE,EAAE;MAAE,CAAE;MAAAE,QAAA,EAAC;IAAiC;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAE,EAEpMvG,WAAW,CAAC8G,GAAG,CAAC,CAAC3D,GAAG,EAAEwE,KAAK,kBAC1B9I,OAAA,CAAClB,IAAI;MAAckI,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,eAC/BnH,OAAA,CAACjB,WAAW;QAAAoI,QAAA,gBACVnH,OAAA,CAAC5B,UAAU;UAACgJ,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAF,QAAA,GAAC,aACzB,EAACpF,gBAAgB,GAAI,CAACJ,WAAW,GAAG,CAAC,IAAI,CAAE,GAAGmH,KAAK,EAAC,QAAM,EAACxE,GAAG,CAACK,EAAE,CAACoE,SAAS,CAAC,CAAC,EAAC,CAAC,CAAC,EAAC,MAC5F,eAAA/I,OAAA,CAAC5B,UAAU;YAACgJ,OAAO,EAAC,SAAS;YAACJ,EAAE,EAAE;cAACyC,EAAE,EAAE;YAAC,CAAE;YAAAtC,QAAA,GAAC,WAAS,EAACxB,UAAU,CAACrB,GAAG,CAACoF,UAAU,CAAC;UAAA;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnF,CAAC,eACb1H,OAAA,CAACvB,GAAG;UAACuI,EAAE,EAAE;YAAEqB,OAAO,EAAE,MAAM;YAAEiB,GAAG,EAAE,CAAC;YAAEpC,EAAE,EAAE,CAAC;YAAEyC,QAAQ,EAAE;UAAO,CAAE;UAAAxC,QAAA,gBAC5DnH,OAAA,CAAC5B,UAAU;YAACgJ,OAAO,EAAC,OAAO;YAAAD,QAAA,gBAACnH,OAAA;cAAAmH,QAAA,EAAQ;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACpD,GAAG,CAAC+B,MAAM;UAAA;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAC9E1H,OAAA,CAAC5B,UAAU;YAACgJ,OAAO,EAAC,OAAO;YAAAD,QAAA,gBAACnH,OAAA;cAAAmH,QAAA,EAAQ;YAAW;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACpD,GAAG,CAACiC,YAAY,EAAC,MAAI,EAACjC,GAAG,CAACgC,aAAa,EAAC,GAAC;UAAA;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAChH1H,OAAA,CAAC5B,UAAU;YAACgJ,OAAO,EAAC,OAAO;YAAAD,QAAA,gBAACnH,OAAA;cAAAmH,QAAA,EAAQ;YAAY;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAACpD,GAAG,CAACkC,WAAW;UAAA;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrF,CAAC,eACN1H,OAAA,CAAC5B,UAAU;UAACgJ,OAAO,EAAC,WAAW;UAACJ,EAAE,EAAE;YAACC,EAAE,EAAE,CAAC;YAAEC,EAAE,EAAE;UAAC,CAAE;UAAAC,QAAA,EAAC;QAAc;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC/E1H,OAAA,CAACnB,IAAI;UAACqK,SAAS;UAACN,OAAO,EAAE,CAAE;UAAAzB,QAAA,EACxB,CAAC7C,GAAG,CAACC,OAAO,IAAI,EAAE,EAAE0D,GAAG,CAAE2B,MAAM,iBAC9B5J,OAAA,CAACnB,IAAI;YAACsK,IAAI;YAACC,EAAE,EAAE,EAAG;YAACS,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA3C,QAAA,eAC9BnH,OAAA,CAACX,KAAK;cAAC0K,SAAS,EAAE,CAAE;cAAC/C,EAAE,EAAE;gBAAErG,CAAC,EAAE,CAAC;gBAAEqJ,MAAM,EAAE,MAAM;gBAAE3B,OAAO,EAAE,MAAM;gBAAE4B,aAAa,EAAE,QAAQ;gBAAE3B,cAAc,EAAE;cAAgB,CAAE;cAAAnB,QAAA,gBAC3HnH,OAAA,CAAC5B,UAAU;gBAACgJ,OAAO,EAAC,WAAW;gBAACC,YAAY;gBAAAF,QAAA,EAAEyC,MAAM,CAACM;cAAU;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAC7E1H,OAAA,CAACvB,GAAG;gBAACuI,EAAE,EAAE;kBAAEmD,QAAQ,EAAE,CAAC;kBAAE9B,OAAO,EAAE,MAAM;kBAAE4B,aAAa,EAAC,QAAQ;kBAAEG,UAAU,EAAE,QAAQ;kBAAE9B,cAAc,EAAE,QAAQ;kBAAE+B,SAAS,EAAE,GAAG;kBAAEnD,EAAE,EAAC;gBAAE,CAAE;gBAAAC,QAAA,GACrIyC,MAAM,CAACU,MAAM,KAAK,SAAS,IAAIV,MAAM,CAACW,iBAAiB,iBAAKvK,OAAA;kBAAKwK,GAAG,EAAEZ,MAAM,CAACW,iBAAkB;kBAACE,GAAG,EAAE,cAAcb,MAAM,CAACM,UAAU,EAAG;kBAACQ,KAAK,EAAE;oBAAEnK,QAAQ,EAAE,MAAM;oBAAEoK,SAAS,EAAE,OAAO;oBAAEC,SAAS,EAAE;kBAAU;gBAAE;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE,EAClNkC,MAAM,CAACU,MAAM,KAAK,SAAS,IAAI,CAACV,MAAM,CAACW,iBAAiB,iBAAKvK,OAAA,CAAC5B,UAAU;kBAACgJ,OAAO,EAAC,SAAS;kBAACmB,KAAK,EAAC,eAAe;kBAAApB,QAAA,EAAC;gBAAkB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAE,EACjJkC,MAAM,CAACU,MAAM,KAAK,QAAQ,iBAAKtK,OAAA,CAACvB,GAAG;kBAACuI,EAAE,EAAE;oBAACoB,SAAS,EAAE;kBAAQ,CAAE;kBAAAjB,QAAA,gBAACnH,OAAA,CAACH,gBAAgB;oBAAC0I,KAAK,EAAC,OAAO;oBAACvB,EAAE,EAAE;sBAAE6D,QAAQ,EAAE;oBAAG;kBAAE;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAAA1H,OAAA,CAAC5B,UAAU;oBAACgJ,OAAO,EAAC,OAAO;oBAACmB,KAAK,EAAC,OAAO;oBAAApB,QAAA,EAAC;kBAAM;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,EAACkC,MAAM,CAACkB,aAAa,iBAAI9K,OAAA,CAACd,OAAO;oBAAC6L,KAAK,EAAEnB,MAAM,CAACkB,aAAc;oBAAA3D,QAAA,eAACnH,OAAA,CAAC5B,UAAU;sBAACgJ,OAAO,EAAC,SAAS;sBAACJ,EAAE,EAAE;wBAACgE,MAAM,EAAE;sBAAM,CAAE;sBAAA7D,QAAA,eAACnH,OAAA;wBAAAmH,QAAA,EAAG;sBAAO;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAE,EACpVkC,MAAM,CAACU,MAAM,KAAK,YAAY,iBAAKtK,OAAA,CAACvB,GAAG;kBAACuI,EAAE,EAAE;oBAACoB,SAAS,EAAE;kBAAQ,CAAE;kBAAAjB,QAAA,gBAACnH,OAAA,CAACrB,gBAAgB;oBAAC4K,IAAI,EAAE;kBAAG;oBAAAhC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAAA1H,OAAA,CAAC5B,UAAU;oBAACgJ,OAAO,EAAC,OAAO;oBAACJ,EAAE,EAAE;sBAACC,EAAE,EAAC;oBAAC,CAAE;oBAAAE,QAAA,EAAC;kBAAa;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxK,CAAC,EACLkC,MAAM,CAACU,MAAM,KAAK,SAAS,iBAC1BtK,OAAA,CAACvB,GAAG;gBAACuI,EAAE,EAAE;kBAAEqB,OAAO,EAAE,MAAM;kBAAE+B,UAAU,EAAE,QAAQ;kBAAEd,GAAG,EAAE,CAAC;kBAAErC,EAAE,EAAE;gBAAO,CAAE;gBAAAE,QAAA,gBACrEnH,OAAA,CAAChB,SAAS;kBAAC8I,KAAK,EAAC,cAAc;kBAACuB,IAAI,EAAC,QAAQ;kBAACjC,OAAO,EAAC,UAAU;kBAACmC,IAAI,EAAC,OAAO;kBAACrE,KAAK,EAAEjD,gBAAgB,CAAC2H,MAAM,CAACjF,EAAE,CAAC,KAAKD,SAAS,GAAG,EAAE,GAAGzC,gBAAgB,CAAC2H,MAAM,CAACjF,EAAE,CAAE;kBAACoD,QAAQ,EAAGiB,CAAC,IAAK5D,iBAAiB,CAACwE,MAAM,CAACjF,EAAE,EAAEqE,CAAC,CAAC/D,MAAM,CAACC,KAAK,CAAE;kBAACzD,KAAK,EAAE,CAAC,CAACU,WAAW,CAACyH,MAAM,CAACjF,EAAE,CAAE;kBAACsG,UAAU,EAAE9I,WAAW,CAACyH,MAAM,CAACjF,EAAE,CAAE;kBAACuG,UAAU,EAAE;oBAAEC,GAAG,EAAE,CAAC;oBAAEC,GAAG,EAAE,EAAE;oBAAEC,IAAI,EAAE;kBAAE,CAAE;kBAACrE,EAAE,EAAE;oBAAEmD,QAAQ,EAAE;kBAAE;gBAAE;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC,CAAC,eACvW1H,OAAA,CAACd,OAAO;kBAAC6L,KAAK,EAAC,YAAY;kBAAA5D,QAAA,eAACnH,OAAA;oBAAAmH,QAAA,eAAMnH,OAAA,CAACb,UAAU;sBAACsJ,OAAO,EAAEA,CAAA,KAAMhD,eAAe,CAACmE,MAAM,CAACjF,EAAE,CAAE;sBAACqD,QAAQ,EAAE,CAAC,CAAC7F,WAAW,CAACyH,MAAM,CAACjF,EAAE,CAAC,IAAI1C,gBAAgB,CAAC2H,MAAM,CAACjF,EAAE,CAAC,KAAKD,SAAS,IAAI4G,MAAM,CAACrJ,gBAAgB,CAAC2H,MAAM,CAACjF,EAAE,CAAC,CAAC,CAAC+B,IAAI,CAAC,CAAC,KAAK,EAAG;sBAAC6B,KAAK,EAAC,SAAS;sBAAApB,QAAA,eAACnH,OAAA,CAACJ,QAAQ;wBAAA2H,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtR,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI;UAAC,GAf4BkC,MAAM,CAACjF,EAAE;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgBzC,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC,GAjCLpD,GAAG,CAACK,EAAE;MAAA4C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAkCX,CACP,CAAC,EAED7F,UAAU,GAAG,CAAC,iBAAK7B,OAAA,CAACvB,GAAG;MAACuI,EAAE,EAAE;QAAEqB,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAErB,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,eAACnH,OAAA,CAACf,UAAU;QAACsM,KAAK,EAAE1J,UAAW;QAACsC,IAAI,EAAExC,WAAY;QAACoG,QAAQ,EAAE5C,gBAAiB;QAACoD,KAAK,EAAC,SAAS;QAACP,QAAQ,EAAEzG;MAAqB;QAAAgG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAE;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvN,CAAC;AAEhB,CAAC;AAAC5G,EAAA,CApTID,WAAW;AAAA2K,EAAA,GAAX3K,WAAW;AAsTjB,eAAeA,WAAW;AAAC,IAAA2K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}