from flask import Blueprint, jsonify, request, current_app
from backend.database.models_bounding_box import BoundingBoxModels, BoundingBoxPictures, BoundingBoxExperiments, BoundingBoxResults
from backend.extensions import db
from backend.AI.BoundingBox import get_image_with_bounding_boxes_async # Import the direct async function
import datetime
import os
import asyncio # Import asyncio

# Helper function to add to models for simple serialization
def to_dict_mixin(self):
    return {c.name: getattr(self, c.name) for c in self.__table__.columns}

BoundingBoxModels.to_dict = to_dict_mixin
BoundingBoxPictures.to_dict = to_dict_mixin
BoundingBoxExperiments.to_dict = to_dict_mixin
BoundingBoxResults.to_dict = to_dict_mixin


bounding_box_bp = Blueprint('bounding_box_api', __name__, url_prefix='/api/v1/boundingbox')

@bounding_box_bp.route('/models', methods=['GET'])
def get_models():
    """
    Get all bounding box models.
    """
    try:
        models = BoundingBoxModels.query.all()
        return jsonify([model.to_dict() for model in models]), 200
    except Exception as e:
        current_app.logger.error(f"Error fetching models: {e}")
        return jsonify({"error": "Failed to fetch models"}), 500

@bounding_box_bp.route('/models/<int:model_id>', methods=['PUT'])
def update_model(model_id):
    """
    Update a specific bounding box model.
    Expects `is_active` (boolean) and optionally `name` (string) and `description` (string).
    """
    data = request.json
    if not data:
        return jsonify({"error": "No data provided"}), 400

    model = BoundingBoxModels.query.get(model_id)
    if not model:
        return jsonify({"error": "Model not found"}), 404

    try:
        if 'name' in data:
            model.name = data['name']
        if 'description' in data:
            model.description = data['description']
        if 'is_active' in data:
            if not isinstance(data['is_active'], bool):
                return jsonify({"error": "'is_active' must be a boolean"}), 400
            model.is_active = data['is_active']

        model.updated_at = datetime.datetime.utcnow()
        db.session.commit()
        return jsonify(model.to_dict()), 200
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error updating model {model_id}: {e}")
        return jsonify({"error": f"Failed to update model: {str(e)}"}), 500


@bounding_box_bp.route('/experiments', methods=['GET'])
def get_experiments():
    """
    Get experiments for a required picture_id, paginated.
    Includes associated results and model names.
    """
    picture_id = request.args.get('picture_id', type=int)
    if not picture_id:
        return jsonify({"error": "picture_id query parameter is required"}), 400

    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 5, type=int)

    try:
        picture = BoundingBoxPictures.query.get(picture_id)
        if not picture:
            return jsonify({"error": "Picture not found"}), 404

        paginated_experiments = BoundingBoxExperiments.query.filter_by(picture_id=picture_id)\
            .order_by(BoundingBoxExperiments.created_at.desc())\
            .paginate(page=page, per_page=per_page, error_out=False)

        experiments_data = []
        for exp in paginated_experiments.items:
            exp_dict = exp.to_dict()
            exp_dict['results'] = []
            for result in exp.results: # Assumes exp.results is the relationship
                model = BoundingBoxModels.query.get(result.model_id) # Fetch model for name
                result_dict = result.to_dict()
                result_dict['model_name'] = model.name if model else "Unknown Model"
                exp_dict['results'].append(result_dict)
            experiments_data.append(exp_dict)

        return jsonify({
            "experiments": experiments_data,
            "total": paginated_experiments.total,
            "pages": paginated_experiments.pages,
            "current_page": paginated_experiments.page,
            "per_page": paginated_experiments.per_page
        }), 200

    except Exception as e:
        current_app.logger.error(f"Error fetching experiments for picture_id {picture_id}: {e}")
        return jsonify({"error": "Failed to fetch experiments"}), 500


@bounding_box_bp.route('/experiments/configurations', methods=['GET'])
def get_experiment_configurations():
    """
    Get all distinct experiment configurations.
    """
    try:
        # Query for distinct combinations of prompt, resize_height, resize_width, output_type
        distinct_configs = db.session.query(
            BoundingBoxExperiments.prompt,
            BoundingBoxExperiments.resize_height,
            BoundingBoxExperiments.resize_width,
            BoundingBoxExperiments.output_type
        ).distinct().all()

        configurations = [
            {
                "prompt": config.prompt,
                "resize_height": config.resize_height,
                "resize_width": config.resize_width,
                "output_type": config.output_type
            } for config in distinct_configs
        ]
        return jsonify(configurations), 200
    except Exception as e:
        current_app.logger.error(f"Error fetching experiment configurations: {e}")
        return jsonify({"error": "Failed to fetch experiment configurations"}), 500

@bounding_box_bp.route('/experiments', methods=['POST'])
async def create_experiment(): # Make route async
    """
    Create a new experiment and trigger async processing for active models.
    """
    data = request.json
    required_fields = ['picture_id', 'prompt', 'resize_height', 'resize_width', 'output_type']
    if not data or not all(field in data for field in required_fields):
        return jsonify({"error": "Missing required fields", "required": required_fields}), 400

    try:
        picture = db.session.get(BoundingBoxPictures, data['picture_id']) # Use .get for PK lookup
        if not picture:
            return jsonify({"error": f"Picture with id {data['picture_id']} not found."}), 404

        new_experiment = BoundingBoxExperiments(
            picture_id=data['picture_id'],
            prompt=data['prompt'],
            resize_height=data['resize_height'],
            resize_width=data['resize_width'],
            output_type=data['output_type']
        )
        db.session.add(new_experiment)
        # Must commit here to get new_experiment.id and new_result.id before passing to async tasks
        # Also, the async tasks will run in separate contexts and need these IDs to exist.

        active_models = BoundingBoxModels.query.filter_by(is_active=True).all()
        if not active_models:
            # No rollback needed if we haven't added new_experiment yet, or handle commit for new_experiment first
            return jsonify({"error": "No active models found to run the experiment against."}), 400

        # Create result entries first and commit them so they have IDs
        initial_results_for_response = []
        async_tasks = []

        db.session.add(new_experiment) # Add experiment
        db.session.flush() # Flush to get ID for new_experiment

        for model in active_models:
            new_result = BoundingBoxResults(
                experiment_id=new_experiment.id,
                model_id=model.id,
                status='processing' # Initial status
            )
            db.session.add(new_result)
            db.session.flush() # Flush to get ID for new_result

            result_dict = new_result.to_dict()
            result_dict['model_name'] = model.name
            initial_results_for_response.append(result_dict)

            pil_save_format = new_experiment.output_type.upper() if new_experiment.output_type.upper() in ['PNG', 'JPEG', 'GIF', 'BMP', 'TIFF'] else 'PNG'

            # Prepare the async call
            # Pass current_app.app_context() if BoundingBox.py functions need it explicitly (e.g. for logging)
            # However, db operations should ideally use the session within the task or be passed the necessary data.
            async_tasks.append(
                get_image_with_bounding_boxes_async(
                    result_id=new_result.id,
                    picture_file_path=picture.file_path,
                    prompt=new_experiment.prompt,
                    resize_height=new_experiment.resize_height,
                    resize_width=new_experiment.resize_width,
                    output_format=pil_save_format,
                    model_db_id=model.id,
                    # app_context=current_app.app_context() # If needed by the async func
                )
            )

        db.session.commit() # Commit experiment and all processing results

        current_app.logger.info(f"Starting {len(async_tasks)} AI processing tasks concurrently for experiment {new_experiment.id}.")
        # Run all AI processing tasks concurrently
        # Note: This will block the route response until all tasks in asyncio.gather complete.
        # For true background tasks, a proper task queue like Celery is needed.
        # This approach is for direct, concurrent async execution within the request lifecycle.
        try:
            await asyncio.gather(*async_tasks)
            current_app.logger.info(f"All AI processing tasks completed for experiment {new_experiment.id}.")
        except Exception as gather_error:
            current_app.logger.error(f"Error during asyncio.gather for experiment {new_experiment.id}: {gather_error}", exc_info=True)
            # Errors within tasks should be handled by the tasks themselves to update their specific result status.
            # This catch is for errors in asyncio.gather() itself, if any.

        experiment_dict = new_experiment.to_dict()
        experiment_dict['results'] = initial_results_for_response # Return initial state (all 'processing')
        # The actual results will be updated in DB by the async functions. Client might need to poll or use WebSocket.

        return jsonify(experiment_dict), 201

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Critical error in create_experiment: {e}", exc_info=True)
        return jsonify({"error": f"Failed to create experiment: {str(e)}"}), 500


@bounding_box_bp.route('/results/<int:result_id>', methods=['PUT'])
def update_result(result_id):
    """
    Update the score for a specific result.
    Expects `score` (integer 0-10).
    """
    data = request.json
    if not data or 'score' not in data:
        return jsonify({"error": "'score' is required in the request body"}), 400

    score = data['score']
    if not isinstance(score, int) or not (0 <= score <= 10):
        return jsonify({"error": "'score' must be an integer between 0 and 10"}), 400

    result = BoundingBoxResults.query.get(result_id)
    if not result:
        return jsonify({"error": "Result not found"}), 404

    try:
        result.score = score
        result.updated_at = datetime.datetime.utcnow()
        db.session.commit()
        return jsonify(result.to_dict()), 200
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Error updating result {result_id}: {e}")
        return jsonify({"error": f"Failed to update result: {str(e)}"}), 500

# Placeholder for image storage path logic - this should ideally be managed by the AI function
# or a centralized config. For now, just noting the requirement.
def get_model_output_storage_path():
    base_dir = os.environ.get("WIN_MAIN_DIR") or os.environ.get("LINUX_MAIN_DIR")
    if not base_dir:
        # Fallback or error if not set, depends on deployment strategy
        current_app.logger.warning("Main directory environment variable (WIN_MAIN_DIR or LINUX_MAIN_DIR) not set.")
        return os.path.join(os.getcwd(), "BoundingBoxOutputs") # Example fallback
    return os.path.join(base_dir, "BoundingBox")

# The get_image_with_bounding_boxes_async function would be defined in backend.AI.BoundingBox
# and would look something like this (if using Celery):
#
# from backend.extensions import db # For db access from task
# from backend.database.models_bounding_box import BoundingBoxResults
# from your_celery_app import celery_instance # Import your celery app
# import datetime
#
# @celery_instance.task
# def get_image_with_bounding_boxes_async(result_id, image_path, prompt, resize_dims, output_type, model_identifier):
#     try:
#         # 1. Perform AI processing to get bounding boxes and generate image
#         #    This is the core AI logic.
#         #    output_file_name = f"result_{result_id}_{model_identifier}.png" # Example naming
#         #    output_full_path = os.path.join(get_model_output_storage_path(), output_file_name)
#         #    # ... actual AI call, save image to output_full_path ...
#         #    generated_bounding_boxes = "[[...]]" # JSON string of boxes
#         #    inference_duration = 1.23 # seconds
#
#         # 2. Update BoundingBoxResults record
#         result = BoundingBoxResults.query.get(result_id)
#         if result:
#             result.status = 'success'
#             result.output_image_path = output_full_path # Relative or absolute, consistent with how it's served
#             result.bounding_boxes = generated_bounding_boxes
#             result.inference_time = inference_duration
#             result.updated_at = datetime.datetime.utcnow()
#             db.session.commit()
#         else:
#             # Log error: result not found
#             pass
#     except Exception as e:
#         # Log the exception
#         result = BoundingBoxResults.query.get(result_id)
#         if result:
#             result.status = 'failed'
#             result.updated_at = datetime.datetime.utcnow()
#             db.session.commit()
#     finally:
#         # Ensure db.session is removed in a Celery task context if using Flask-SQLAlchemy's scoped session
#         db.session.remove()
#
# This async task definition is illustrative. The actual implementation of the AI call and
# how it saves files and records results would be in backend/AI/BoundingBox.py.
# The key part for the API is dispatching this task with the correct parameters.
# The image storage path (point 7) is more relevant to the AI function itself.
# The API provides the necessary data; the AI function uses the data to store the image.
# BoundingBoxPictures.file_path will store paths to user-uploaded source images.
# BoundingBoxResults.output_image_path will store paths to model-generated images.
# The API needs to ensure picture.file_path is correctly populated when pictures are uploaded (not part of this subtask)
# and that the async task is called with picture.file_path.
# The value for BoundingBoxResults.output_image_path is written by the async task.
