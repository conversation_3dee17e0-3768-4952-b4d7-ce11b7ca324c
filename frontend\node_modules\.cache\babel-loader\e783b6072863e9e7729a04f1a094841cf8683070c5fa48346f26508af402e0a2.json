{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents\\\\Programing\\\\TRO\\\\ModelTestsWorkbench\\\\frontend\\\\src\\\\components\\\\model-test-workbench\\\\ByProductView.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useMemo, createElement as _createElement } from 'react';\nimport { Box, Typography, CircularProgress, Alert, Grid, Autocomplete, TextField, Card, CardContent, CardMedia, IconButton, Tooltip, Dialog, DialogContent, DialogTitle, Button } from '@mui/material';\nimport CheckCircleIcon from '@mui/icons-material/CheckCircle';\nimport AddTaskIcon from '@mui/icons-material/AddTask'; // Icon for Mark as Ground Truth\nimport RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline'; // Icon for Remove Ground Truth\nimport ZoomInIcon from '@mui/icons-material/ZoomIn';\nimport ClearIcon from '@mui/icons-material/Clear';\n// Use named imports for API functions\nimport { listImages, getResultsByProduct, addGroundTruth, removeGroundTruth } from '../../services/api_model_workbench';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst INITIAL_SUGGESTIONS_LIMIT = 50; // Total number of suggestions to fetch per model initially\nconst DEFAULT_VISIBLE_SUGGESTIONS = 5; // Number of suggestions to show initially and increment\n\nconst ByProductView = ({\n  ipCategory\n}) => {\n  _s();\n  var _results$product_imag, _results$product_imag2, _results$product_imag3, _results$product_imag5, _results$product_imag6;\n  console.log(`ByProductView received ipCategory: ${ipCategory}`); // Debug log for ipCategory\n  const [rawProductImages, setRawProductImages] = useState([]); // Renamed state to hold raw API data\n  const [selectedProduct, setSelectedProduct] = useState(null); // Store the whole product object\n  const [results, setResults] = useState(null);\n  const [loadingProducts, setLoadingProducts] = useState(false);\n  const [loadingResults, setLoadingResults] = useState(false);\n  const [error, setError] = useState(null);\n  const [imageToEnlarge, setImageToEnlarge] = useState(null);\n  const [visibleCounts, setVisibleCounts] = useState({}); // State to track visible suggestions per model\n\n  const imageBaseUrl = '/api/data/images'; // Added for useMemo\n\n  // useMemo hook as provided by the user for productImagesForAutocomplete\n  const productImagesForAutocomplete = useMemo(() => {\n    console.log('[useMemo] productImages state:', rawProductImages); // Log the input state (rawProductImages)\n\n    const imagesToMap = rawProductImages || [];\n    console.log('[useMemo] imagesToMap (rawProductImages || []):', imagesToMap);\n    console.log('[useMemo] imagesToMap length:', imagesToMap.length);\n    const mappedImages = imagesToMap.map((image, index) => {\n      if (index < 3) {\n        // Keep existing detailed log for first few items\n        console.log(`[useMemo map] Mapping image ${index}:`, JSON.stringify(image, null, 2));\n        console.log(`[useMemo map] Image ${index} - image_id:`, image.image_id, `original_filename:`, image.original_filename);\n      }\n      return {\n        id: image.image_id,\n        label: image.original_filename || 'Unnamed Image',\n        imageUrl: image.image_id ? `${imageBaseUrl}/file/${image.image_id}` : null,\n        // Ensure image.image_id exists for URL\n        ip_category: image.ip_category\n      };\n    });\n    console.log('[useMemo] mappedImages (after .map()):', mappedImages);\n    console.log('[useMemo] mappedImages length:', mappedImages.length);\n    const filteredImages = mappedImages.filter(option => option.id && String(option.id).trim() !== '');\n    console.log('[useMemo] filteredImages (after .filter()):', filteredImages); // This is the existing log that shows []\n\n    return filteredImages;\n  }, [rawProductImages, imageBaseUrl]);\n\n  // Consistent image URL construction\n  const getImageUrl = imageId => imageId ? `/api/data/images/file/${imageId}` : '';\n\n  // Fetch product images for the dropdown\n  useEffect(() => {\n    const fetchProductImages = async () => {\n      setLoadingProducts(true);\n      setError(null);\n      setRawProductImages([]); // Use renamed setter\n      setSelectedProduct(null);\n      setResults(null);\n      try {\n        const params = {\n          image_type: 'product',\n          ip_category: ipCategory\n        };\n        console.log('Params for api.listImages:', params); // Debug log for params\n        const response = await listImages(params);\n        // Enhanced console logging for response.data.images\n        console.log('Raw response.data.images from api.listImages (count):', response.data.images ? response.data.images.length : 0);\n        if (response.data.images && response.data.images.length > 0) {\n          console.log('First image object from API:', JSON.stringify(response.data.images[0], null, 2));\n          console.log('Keys of first image object from API:', Object.keys(response.data.images[0]));\n        }\n        // console.log('Raw response.data.images from api.listImages:', response.data.images); // Original Debug log for raw images\n\n        // Set raw product images; transformation is now handled by useMemo\n        setRawProductImages(response.data.images || []);\n      } catch (err) {\n        var _err$response, _err$response$data, _err$response2, _err$response2$data;\n        console.error('Error fetching product images:', err);\n        setError(`Failed to load product images for ${ipCategory}. ${((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.detail) || ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.error) || err.message}`);\n      } finally {\n        setLoadingProducts(false);\n      }\n    };\n    if (ipCategory) {\n      fetchProductImages();\n    }\n  }, [ipCategory]);\n\n  // Fetch results when a product is selected\n  useEffect(() => {\n    const fetchResultsForProduct = async () => {\n      if (!selectedProduct) {\n        setResults(null);\n        setVisibleCounts({}); // Reset visible counts when product is deselected\n        return;\n      }\n      setLoadingResults(true);\n      setError(null);\n      try {\n        // The API endpoint is /api/results/by-product/{product_image_id}\n        // It expects 'limit' as a query parameter.\n        const params = {\n          limit: INITIAL_SUGGESTIONS_LIMIT\n        }; // Use the initial fetch limit\n        const response = await getResultsByProduct(selectedProduct.id, params);\n        // Backend response structure:\n        // {\n        //   \"product_image\": { \"id\": \"uuid\", \"filename\": \"str\", \"ip_category\": \"str\" },\n        //   \"ground_truth_ips\": [ { \"id\": \"uuid\", \"filename\": \"str\", \"ip_owner\": \"str\" } ],\n        //   \"results_by_model\": {\n        //     \"model_name_or_id_1\": [ { \"ip_image_id\": \"uuid\", \"ip_filename\": \"str\", \"similarity_score\": \"float\", \"is_ground_truth\": \"bool\", \"ip_owner\": \"str\" } ],\n        //     \"model_name_or_id_2\": [ ... ]\n        //   }\n        // }\n        setResults(response.data);\n\n        // Initialize visible counts for each model\n        if (response.data && response.data.results_by_model) {\n          const initialCounts = {};\n          Object.keys(response.data.results_by_model).forEach(modelName => {\n            initialCounts[modelName] = DEFAULT_VISIBLE_SUGGESTIONS;\n          });\n          setVisibleCounts(initialCounts);\n        } else {\n          setVisibleCounts({}); // Reset if no models or data\n        }\n      } catch (err) {\n        var _err$response3, _err$response3$data, _err$response4, _err$response4$data;\n        console.error(`Error fetching results for product ${selectedProduct.id}:`, err);\n        setError(`Failed to load results for product ${selectedProduct.filename}. ${((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.detail) || ((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : (_err$response4$data = _err$response4.data) === null || _err$response4$data === void 0 ? void 0 : _err$response4$data.error) || err.message}`);\n        setResults(null);\n        setVisibleCounts({}); // Reset visible counts on error\n      } finally {\n        setLoadingResults(false);\n      }\n    };\n    fetchResultsForProduct();\n  }, [selectedProduct, INITIAL_SUGGESTIONS_LIMIT, DEFAULT_VISIBLE_SUGGESTIONS]); // Add constants to dependency array\n\n  const handleMarkAsGroundTruth = useCallback(async (suggestedIpImageId, isCurrentlyGroundTruth) => {\n    if (!selectedProduct || !selectedProduct.id) return;\n    setError(null);\n    try {\n      if (isCurrentlyGroundTruth) {\n        await removeGroundTruth(selectedProduct.id, suggestedIpImageId);\n      } else {\n        await addGroundTruth(selectedProduct.id, suggestedIpImageId);\n      }\n      // Refetch results with the initial limit to get updated ground truth status\n      const params = {\n        limit: INITIAL_SUGGESTIONS_LIMIT\n      }; // Use the initial fetch limit\n      const response = await getResultsByProduct(selectedProduct.id, params);\n      setResults(response.data);\n      // Note: visibleCounts state is intentionally NOT reset here,\n      // so the \"show more\" state is preserved after marking ground truth.\n    } catch (err) {\n      var _err$response5, _err$response5$data, _err$response6, _err$response6$data;\n      console.error('Error updating ground truth:', err);\n      setError(`Failed to update ground truth. ${((_err$response5 = err.response) === null || _err$response5 === void 0 ? void 0 : (_err$response5$data = _err$response5.data) === null || _err$response5$data === void 0 ? void 0 : _err$response5$data.detail) || ((_err$response6 = err.response) === null || _err$response6 === void 0 ? void 0 : (_err$response6$data = _err$response6.data) === null || _err$response6$data === void 0 ? void 0 : _err$response6$data.error) || err.message}`);\n    }\n  }, [selectedProduct, INITIAL_SUGGESTIONS_LIMIT]); // Add constant to dependency array\n\n  const handleImageClick = imageUrl => {\n    setImageToEnlarge(imageUrl);\n  };\n  const handleCloseDialog = () => {\n    setImageToEnlarge(null);\n  };\n\n  // suggestion object: { ip_image_id, ip_filename, similarity_score, is_ground_truth, ip_owner }\n  const renderSuggestion = suggestion => {\n    var _suggestion$similarit, _suggestion$similarit2;\n    return /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        display: 'flex',\n        mb: 1,\n        alignItems: 'center',\n        position: 'relative',\n        width: '100%'\n      },\n      children: [suggestion.is_ground_truth && /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"Known Correct Match\",\n        children: /*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n          color: \"success\",\n          sx: {\n            position: 'absolute',\n            top: 4,\n            left: 4,\n            zIndex: 1,\n            backgroundColor: 'white',\n            borderRadius: '50%'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(CardMedia, {\n        component: \"img\",\n        sx: {\n          width: 80,\n          height: 80,\n          objectFit: 'contain',\n          cursor: 'pointer',\n          p: 0.5\n        },\n        image: getImageUrl(suggestion.ip_image_id) // Use ip_image_id\n        ,\n        alt: suggestion.ip_filename || 'IP Image' // Use ip_filename\n        ,\n        onClick: () => handleImageClick(getImageUrl(suggestion.ip_image_id))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          flexGrow: 1,\n          justifyContent: 'center',\n          pl: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          sx: {\n            wordBreak: 'break-all'\n          },\n          children: [suggestion.ip_filename || 'N/A', \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          children: [\"Score: \", (_suggestion$similarit = (_suggestion$similarit2 = suggestion.similarity_score) === null || _suggestion$similarit2 === void 0 ? void 0 : _suggestion$similarit2.toFixed(4)) !== null && _suggestion$similarit !== void 0 ? _suggestion$similarit : 'N/A', \" \"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          children: [\"Owner: \", suggestion.ip_owner || 'N/A']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 18\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: suggestion.is_ground_truth ? \"Remove from Ground Truth\" : \"Mark as Ground Truth\",\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          onClick: () => handleMarkAsGroundTruth(suggestion.ip_image_id, suggestion.is_ground_truth),\n          color: suggestion.is_ground_truth ? \"error\" : \"success\",\n          sx: {\n            alignSelf: 'center',\n            mr: 1\n          },\n          children: suggestion.is_ground_truth ? /*#__PURE__*/_jsxDEV(RemoveCircleOutlineIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 51\n          }, this) : /*#__PURE__*/_jsxDEV(AddTaskIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 81\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 17\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 14\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 9\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 2\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      gutterBottom: true,\n      children: \"Results by Product Picture\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 13\n    }, this), loadingProducts && /*#__PURE__*/_jsxDEV(CircularProgress, {\n      size: 24\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 33\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 237,\n      columnNumber: 23\n    }, this), /*#__PURE__*/_jsxDEV(Autocomplete, {\n      options: productImagesForAutocomplete // Use memoized and transformed images\n      ,\n      getOptionLabel: option => option.label || \"\",\n      value: selectedProduct,\n      onChange: (event, newValue) => {\n        setSelectedProduct(newValue);\n      },\n      isOptionEqualToValue: (option, value) => option.id === value.id,\n      renderInput: params => /*#__PURE__*/_jsxDEV(TextField, {\n        ...params,\n        label: `Select Product Image (${ipCategory})`,\n        variant: \"outlined\",\n        size: \"small\",\n        disabled: loadingProducts || productImagesForAutocomplete.length === 0 // Use memoized images for disabled check\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 248,\n        columnNumber: 21\n      }, this),\n      renderOption: (htmlProps, option) => {\n        // Destructure 'key' from htmlProps to prevent spreading it if it exists,\n        // as this can cause React warnings. The actual React key for the list item\n        // should be option.id.\n        const {\n          key: muiProvidedKey,\n          ...otherHtmlProps\n        } = htmlProps;\n        return /*#__PURE__*/_createElement(Box, {\n          component: \"li\",\n          sx: {\n            '& > img': {\n              mr: 2,\n              flexShrink: 0\n            }\n          },\n          ...otherHtmlProps,\n          key: option.id,\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 25\n          }\n        }, /*#__PURE__*/_jsxDEV(\"img\", {\n          loading: \"lazy\",\n          width: \"40\"\n          // option.imageUrl should be valid due to filtering,\n          // but pass null if it's somehow empty to avoid empty src warning.\n          ,\n          src: option.imageUrl || null,\n          alt: option.filename || 'Product thumbnail' // Fallback for alt text\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 29\n        }, this), option.label || 'Unnamed Product', \" (\", option.id ? option.id.substring(0, 8) : 'N/A', \"...)\");\n      },\n      sx: {\n        mb: 2,\n        maxWidth: 500\n      },\n      disabled: loadingProducts\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 13\n    }, this), loadingResults && /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 32\n    }, this), selectedProduct && results && !loadingResults && /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 3,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Selected Product\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(CardMedia, {\n            component: \"img\",\n            sx: {\n              height: 200,\n              objectFit: 'contain',\n              cursor: 'pointer'\n            },\n            image: getImageUrl((_results$product_imag = results.product_image) === null || _results$product_imag === void 0 ? void 0 : _results$product_imag.id),\n            alt: `${((_results$product_imag2 = results.product_image) === null || _results$product_imag2 === void 0 ? void 0 : _results$product_imag2.filename) || 'N/A'} - ${((_results$product_imag3 = results.product_image) === null || _results$product_imag3 === void 0 ? void 0 : _results$product_imag3.ip_category) || 'N/A'}`,\n            onClick: () => {\n              var _results$product_imag4;\n              return ((_results$product_imag4 = results.product_image) === null || _results$product_imag4 === void 0 ? void 0 : _results$product_imag4.id) && handleImageClick(getImageUrl(results.product_image.id));\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                wordBreak: 'break-all'\n              },\n              children: ((_results$product_imag5 = results.product_image) === null || _results$product_imag5 === void 0 ? void 0 : _results$product_imag5.filename) || 'N/A'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              display: \"block\",\n              sx: {\n                fontStyle: 'italic',\n                mt: 0.5\n              },\n              children: [\"Category: \", ((_results$product_imag6 = results.product_image) === null || _results$product_imag6 === void 0 ? void 0 : _results$product_imag6.ip_category) || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 29\n          }, this), results.ground_truth_ips && results.ground_truth_ips.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n            mt: 2,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Known Correct Matches\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 1,\n              children: results.ground_truth_ips.map(gtImage => /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 6,\n                sm: 4,\n                md: 6,\n                children: /*#__PURE__*/_jsxDEV(Card, {\n                  sx: {\n                    position: 'relative',\n                    textAlign: 'center',\n                    p: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: gtImage.filename || 'Ground Truth Image',\n                    children: /*#__PURE__*/_jsxDEV(CardMedia, {\n                      component: \"img\",\n                      sx: {\n                        height: 80,\n                        objectFit: 'contain',\n                        cursor: 'pointer',\n                        margin: '0 auto'\n                      },\n                      image: getImageUrl(gtImage.id),\n                      alt: gtImage.filename || 'Ground Truth',\n                      onClick: () => handleImageClick(getImageUrl(gtImage.id))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 310,\n                      columnNumber: 53\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 309,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    sx: {\n                      mt: 1,\n                      wordBreak: 'break-all'\n                    },\n                    children: gtImage.filename || 'N/A'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 318,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                    onClick: () => handleMarkAsGroundTruth(gtImage.id, true),\n                    sx: {\n                      mt: 1,\n                      color: 'error.main',\n                      border: '1px solid',\n                      borderColor: 'error.main',\n                      borderRadius: '50%',\n                      width: 36,\n                      height: 36,\n                      display: 'inline-flex',\n                      justifyContent: 'center',\n                      alignItems: 'center'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(ClearIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 336,\n                      columnNumber: 53\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 321,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 45\n                }, this)\n              }, gtImage.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 41\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 9,\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Model Suggestions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 25\n        }, this), results.results_by_model && Object.entries(results.results_by_model).length > 0 ? Object.entries(results.results_by_model).map(([modelName, suggestions]) => /*#__PURE__*/_jsxDEV(Box, {\n          mb: 3,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            component: \"div\",\n            gutterBottom: true,\n            children: modelName\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 37\n          }, this), suggestions && suggestions.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              container: true,\n              spacing: 1,\n              children: suggestions.slice(0, visibleCounts[modelName] || DEFAULT_VISIBLE_SUGGESTIONS).map(suggestion => /*#__PURE__*/_jsxDEV(Grid, {\n                item: true,\n                xs: 12,\n                sm: 6,\n                md: 4,\n                children: renderSuggestion(suggestion)\n              }, `${modelName}-${suggestion.ip_image_id}`, false, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 53\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 45\n            }, this), (visibleCounts[modelName] || DEFAULT_VISIBLE_SUGGESTIONS) < suggestions.length && /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                justifyContent: 'center',\n                mt: 2,\n                width: '100%'\n              },\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                onClick: () => {\n                  setVisibleCounts(prevCounts => ({\n                    ...prevCounts,\n                    [modelName]: (prevCounts[modelName] || DEFAULT_VISIBLE_SUGGESTIONS) + DEFAULT_VISIBLE_SUGGESTIONS\n                  }));\n                },\n                children: [\"Show \", DEFAULT_VISIBLE_SUGGESTIONS, \" More Suggestions\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 53\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 49\n            }, this)]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"No suggestions found for this model.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 41\n          }, this)]\n        }, modelName, true, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 33\n        }, this)) : /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          children: \"No model results available for this product image.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 30\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: Boolean(imageToEnlarge),\n      onClose: handleCloseDialog,\n      maxWidth: \"md\",\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Enlarged Image\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: imageToEnlarge,\n          alt: \"Enlarged view\",\n          style: {\n            width: '100%',\n            height: 'auto'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleCloseDialog,\n        sx: {\n          m: 1\n        },\n        children: \"Close\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 399,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 394,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 233,\n    columnNumber: 9\n  }, this);\n};\n_s(ByProductView, \"Uv7NA1uZwOUkRRu/9xY7S/IklTE=\");\n_c = ByProductView;\nexport default ByProductView;\nvar _c;\n$RefreshReg$(_c, \"ByProductView\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "createElement", "_createElement", "Box", "Typography", "CircularProgress", "<PERSON><PERSON>", "Grid", "Autocomplete", "TextField", "Card", "<PERSON><PERSON><PERSON><PERSON>", "CardMedia", "IconButton", "<PERSON><PERSON><PERSON>", "Dialog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogTitle", "<PERSON><PERSON>", "CheckCircleIcon", "AddTaskIcon", "RemoveCircleOutlineIcon", "ZoomInIcon", "ClearIcon", "listImages", "getResultsByProduct", "addGroundTruth", "removeGroundTruth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "INITIAL_SUGGESTIONS_LIMIT", "DEFAULT_VISIBLE_SUGGESTIONS", "ByProductView", "ipCategory", "_s", "_results$product_imag", "_results$product_imag2", "_results$product_imag3", "_results$product_imag5", "_results$product_imag6", "console", "log", "rawProductImages", "setRawProductImages", "selectedProduct", "setSelectedProduct", "results", "setResults", "loadingProducts", "setLoadingProducts", "loadingResults", "setLoadingResults", "error", "setError", "imageToEnlarge", "setImageToEnlarge", "visibleCounts", "setVisibleCounts", "imageBaseUrl", "productImagesForAutocomplete", "imagesToMap", "length", "mappedImages", "map", "image", "index", "JSON", "stringify", "image_id", "original_filename", "id", "label", "imageUrl", "ip_category", "filteredImages", "filter", "option", "String", "trim", "getImageUrl", "imageId", "fetchProductImages", "params", "image_type", "response", "data", "images", "Object", "keys", "err", "_err$response", "_err$response$data", "_err$response2", "_err$response2$data", "detail", "message", "fetchResultsForProduct", "limit", "results_by_model", "initialCounts", "for<PERSON>ach", "modelName", "_err$response3", "_err$response3$data", "_err$response4", "_err$response4$data", "filename", "handleMarkAsGroundTruth", "suggestedIpImageId", "isCurrentlyGroundTruth", "_err$response5", "_err$response5$data", "_err$response6", "_err$response6$data", "handleImageClick", "handleCloseDialog", "renderSuggestion", "suggestion", "_suggestion$similarit", "_suggestion$similarit2", "sx", "display", "mb", "alignItems", "position", "width", "children", "is_ground_truth", "title", "color", "top", "left", "zIndex", "backgroundColor", "borderRadius", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "component", "height", "objectFit", "cursor", "p", "ip_image_id", "alt", "ip_filename", "onClick", "flexDirection", "flexGrow", "justifyContent", "pl", "variant", "wordBreak", "similarity_score", "toFixed", "ip_owner", "size", "alignSelf", "mr", "gutterBottom", "severity", "options", "getOptionLabel", "value", "onChange", "event", "newValue", "isOptionEqualToValue", "renderInput", "disabled", "renderOption", "htmlProps", "key", "muiProvided<PERSON>ey", "otherHtmlProps", "flexShrink", "__self", "__source", "loading", "src", "substring", "max<PERSON><PERSON><PERSON>", "container", "spacing", "item", "xs", "md", "product_image", "_results$product_imag4", "fontStyle", "mt", "ground_truth_ips", "gtImage", "sm", "textAlign", "margin", "border", "borderColor", "entries", "suggestions", "slice", "prevCounts", "open", "Boolean", "onClose", "style", "m", "_c", "$RefreshReg$"], "sources": ["D:/Documents/Programing/TRO/ModelTestsWorkbench/frontend/src/components/model-test-workbench/ByProductView.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useMemo } from 'react';\r\nimport {\r\n    Box,\r\n    Typography,\r\n    CircularProgress,\r\n    Alert,\r\n    Grid,\r\n    Autocomplete,\r\n    TextField,\r\n    Card,\r\n    CardContent,\r\n    CardMedia,\r\n    IconButton,\r\n    Tooltip,\r\n    Dialog,\r\n    DialogContent,\r\n    <PERSON>alogTitle,\r\n    <PERSON><PERSON>,\r\n} from '@mui/material';\r\nimport CheckCircleIcon from '@mui/icons-material/CheckCircle';\r\nimport AddTaskIcon from '@mui/icons-material/AddTask'; // Icon for Mark as Ground Truth\r\nimport RemoveCircleOutlineIcon from '@mui/icons-material/RemoveCircleOutline'; // Icon for Remove Ground Truth\r\nimport ZoomInIcon from '@mui/icons-material/ZoomIn';\r\nimport ClearIcon from '@mui/icons-material/Clear';\r\n// Use named imports for API functions\r\nimport { listImages, getResultsByProduct, addGroundTruth, removeGroundTruth } from '../../services/api_model_workbench';\r\n\r\nconst INITIAL_SUGGESTIONS_LIMIT = 50; // Total number of suggestions to fetch per model initially\r\nconst DEFAULT_VISIBLE_SUGGESTIONS = 5; // Number of suggestions to show initially and increment\r\n\r\nconst ByProductView = ({ ipCategory }) => {\r\n    console.log(`ByProductView received ipCategory: ${ipCategory}`); // Debug log for ipCategory\r\n    const [rawProductImages, setRawProductImages] = useState([]); // Renamed state to hold raw API data\r\n    const [selectedProduct, setSelectedProduct] = useState(null); // Store the whole product object\r\n    const [results, setResults] = useState(null);\r\n    const [loadingProducts, setLoadingProducts] = useState(false);\r\n    const [loadingResults, setLoadingResults] = useState(false);\r\n    const [error, setError] = useState(null);\r\n    const [imageToEnlarge, setImageToEnlarge] = useState(null);\r\n    const [visibleCounts, setVisibleCounts] = useState({}); // State to track visible suggestions per model\r\n\r\n    const imageBaseUrl = '/api/data/images'; // Added for useMemo\r\n\r\n    // useMemo hook as provided by the user for productImagesForAutocomplete\r\n    const productImagesForAutocomplete = useMemo(() => {\r\n        console.log('[useMemo] productImages state:', rawProductImages); // Log the input state (rawProductImages)\r\n        \r\n        const imagesToMap = rawProductImages || [];\r\n        console.log('[useMemo] imagesToMap (rawProductImages || []):', imagesToMap);\r\n        console.log('[useMemo] imagesToMap length:', imagesToMap.length);\r\n\r\n        const mappedImages = imagesToMap.map((image, index) => {\r\n            if (index < 3) { // Keep existing detailed log for first few items\r\n                console.log(`[useMemo map] Mapping image ${index}:`, JSON.stringify(image, null, 2));\r\n                console.log(`[useMemo map] Image ${index} - image_id:`, image.image_id, `original_filename:`, image.original_filename);\r\n            }\r\n            return {\r\n                id: image.image_id,\r\n                label: image.original_filename || 'Unnamed Image',\r\n                imageUrl: image.image_id ? `${imageBaseUrl}/file/${image.image_id}` : null, // Ensure image.image_id exists for URL\r\n                ip_category: image.ip_category\r\n            };\r\n        });\r\n        console.log('[useMemo] mappedImages (after .map()):', mappedImages);\r\n        console.log('[useMemo] mappedImages length:', mappedImages.length);\r\n\r\n        const filteredImages = mappedImages.filter(option => option.id && String(option.id).trim() !== '');\r\n        console.log('[useMemo] filteredImages (after .filter()):', filteredImages); // This is the existing log that shows []\r\n        \r\n        return filteredImages;\r\n    }, [rawProductImages, imageBaseUrl]);\r\n\r\n    // Consistent image URL construction\r\n    const getImageUrl = (imageId) => imageId ? `/api/data/images/file/${imageId}` : '';\r\n\r\n    // Fetch product images for the dropdown\r\n    useEffect(() => {\r\n        const fetchProductImages = async () => {\r\n            setLoadingProducts(true);\r\n            setError(null);\r\n            setRawProductImages([]); // Use renamed setter\r\n            setSelectedProduct(null);\r\n            setResults(null);\r\n            try {\r\n                const params = {\r\n                    image_type: 'product',\r\n                    ip_category: ipCategory,\r\n                };\r\n                console.log('Params for api.listImages:', params); // Debug log for params\r\n                const response = await listImages(params);\r\n                // Enhanced console logging for response.data.images\r\n                console.log('Raw response.data.images from api.listImages (count):', response.data.images ? response.data.images.length : 0);\r\n                if (response.data.images && response.data.images.length > 0) {\r\n                    console.log('First image object from API:', JSON.stringify(response.data.images[0], null, 2));\r\n                    console.log('Keys of first image object from API:', Object.keys(response.data.images[0]));\r\n                }\r\n                // console.log('Raw response.data.images from api.listImages:', response.data.images); // Original Debug log for raw images\r\n\r\n                // Set raw product images; transformation is now handled by useMemo\r\n                setRawProductImages(response.data.images || []);\r\n            } catch (err) {\r\n                console.error('Error fetching product images:', err);\r\n                setError(`Failed to load product images for ${ipCategory}. ${err.response?.data?.detail || err.response?.data?.error || err.message}`);\r\n            } finally {\r\n                setLoadingProducts(false);\r\n            }\r\n        };\r\n\r\n        if (ipCategory) {\r\n            fetchProductImages();\r\n        }\r\n    }, [ipCategory]);\r\n\r\n    // Fetch results when a product is selected\r\n    useEffect(() => {\r\n        const fetchResultsForProduct = async () => {\r\n            if (!selectedProduct) {\r\n                setResults(null);\r\n                setVisibleCounts({}); // Reset visible counts when product is deselected\r\n                return;\r\n            }\r\n            setLoadingResults(true);\r\n            setError(null);\r\n            try {\r\n                // The API endpoint is /api/results/by-product/{product_image_id}\r\n                // It expects 'limit' as a query parameter.\r\n                const params = { limit: INITIAL_SUGGESTIONS_LIMIT }; // Use the initial fetch limit\r\n                const response = await getResultsByProduct(selectedProduct.id, params);\r\n                // Backend response structure:\r\n                // {\r\n                //   \"product_image\": { \"id\": \"uuid\", \"filename\": \"str\", \"ip_category\": \"str\" },\r\n                //   \"ground_truth_ips\": [ { \"id\": \"uuid\", \"filename\": \"str\", \"ip_owner\": \"str\" } ],\r\n                //   \"results_by_model\": {\r\n                //     \"model_name_or_id_1\": [ { \"ip_image_id\": \"uuid\", \"ip_filename\": \"str\", \"similarity_score\": \"float\", \"is_ground_truth\": \"bool\", \"ip_owner\": \"str\" } ],\r\n                //     \"model_name_or_id_2\": [ ... ]\r\n                //   }\r\n                // }\r\n                setResults(response.data);\r\n\r\n                // Initialize visible counts for each model\r\n                if (response.data && response.data.results_by_model) {\r\n                    const initialCounts = {};\r\n                    Object.keys(response.data.results_by_model).forEach(modelName => {\r\n                        initialCounts[modelName] = DEFAULT_VISIBLE_SUGGESTIONS;\r\n                    });\r\n                    setVisibleCounts(initialCounts);\r\n                } else {\r\n                    setVisibleCounts({}); // Reset if no models or data\r\n                }\r\n\r\n            } catch (err) {\r\n                console.error(`Error fetching results for product ${selectedProduct.id}:`, err);\r\n                setError(`Failed to load results for product ${selectedProduct.filename}. ${err.response?.data?.detail || err.response?.data?.error || err.message}`);\r\n                setResults(null);\r\n                setVisibleCounts({}); // Reset visible counts on error\r\n            } finally {\r\n                setLoadingResults(false);\r\n            }\r\n        };\r\n\r\n        fetchResultsForProduct();\r\n    }, [selectedProduct, INITIAL_SUGGESTIONS_LIMIT, DEFAULT_VISIBLE_SUGGESTIONS]); // Add constants to dependency array\r\n\r\n    const handleMarkAsGroundTruth = useCallback(async (suggestedIpImageId, isCurrentlyGroundTruth) => {\r\n        if (!selectedProduct || !selectedProduct.id) return;\r\n        setError(null);\r\n        try {\r\n            if (isCurrentlyGroundTruth) {\r\n                await removeGroundTruth(selectedProduct.id, suggestedIpImageId);\r\n            } else {\r\n                await addGroundTruth(selectedProduct.id, suggestedIpImageId);\r\n            }\r\n            // Refetch results with the initial limit to get updated ground truth status\r\n            const params = { limit: INITIAL_SUGGESTIONS_LIMIT }; // Use the initial fetch limit\r\n            const response = await getResultsByProduct(selectedProduct.id, params);\r\n            setResults(response.data);\r\n            // Note: visibleCounts state is intentionally NOT reset here,\r\n            // so the \"show more\" state is preserved after marking ground truth.\r\n        } catch (err) {\r\n            console.error('Error updating ground truth:', err);\r\n            setError(`Failed to update ground truth. ${err.response?.data?.detail || err.response?.data?.error || err.message}`);\r\n        }\r\n    }, [selectedProduct, INITIAL_SUGGESTIONS_LIMIT]); // Add constant to dependency array\r\n\r\n    const handleImageClick = (imageUrl) => {\r\n        setImageToEnlarge(imageUrl);\r\n    };\r\n\r\n    const handleCloseDialog = () => {\r\n        setImageToEnlarge(null);\r\n    };\r\n\r\n    // suggestion object: { ip_image_id, ip_filename, similarity_score, is_ground_truth, ip_owner }\r\n    const renderSuggestion = (suggestion) => (\r\n        <Card sx={{ display: 'flex', mb: 1, alignItems: 'center', position: 'relative', width: '100%' }}>\r\n            {suggestion.is_ground_truth && (\r\n                <Tooltip title=\"Known Correct Match\">\r\n                    <CheckCircleIcon color=\"success\" sx={{ position: 'absolute', top: 4, left: 4, zIndex: 1, backgroundColor: 'white', borderRadius: '50%' }} />\r\n                </Tooltip>\r\n            )}\r\n            <CardMedia\r\n                component=\"img\"\r\n                sx={{ width: 80, height: 80, objectFit: 'contain', cursor: 'pointer', p: 0.5 }}\r\n                image={getImageUrl(suggestion.ip_image_id)} // Use ip_image_id\r\n                alt={suggestion.ip_filename || 'IP Image'} // Use ip_filename\r\n                onClick={() => handleImageClick(getImageUrl(suggestion.ip_image_id))}\r\n            />\r\n            <Box sx={{ display: 'flex', flexDirection: 'column', flexGrow: 1, justifyContent: 'center', pl: 1 }}>\r\n                <Typography variant=\"caption\" sx={{ wordBreak: 'break-all' }}>\r\n                    {suggestion.ip_filename || 'N/A'} {/* Use ip_filename */}\r\n                </Typography>\r\n                <Typography variant=\"caption\">\r\n                    Score: {suggestion.similarity_score?.toFixed(4) ?? 'N/A'} {/* Use similarity_score */}\r\n                </Typography>\r\n                 <Typography variant=\"caption\">\r\n                    Owner: {suggestion.ip_owner || 'N/A'}\r\n                </Typography>\r\n            </Box>\r\n             <Tooltip title={suggestion.is_ground_truth ? \"Remove from Ground Truth\" : \"Mark as Ground Truth\"}>\r\n                <IconButton\r\n                    size=\"small\"\r\n                    onClick={() => handleMarkAsGroundTruth(suggestion.ip_image_id, suggestion.is_ground_truth)}\r\n                    color={suggestion.is_ground_truth ? \"error\" : \"success\"}\r\n                    sx={{ alignSelf: 'center', mr: 1 }}\r\n                >\r\n                    {suggestion.is_ground_truth ? <RemoveCircleOutlineIcon /> : <AddTaskIcon />}\r\n                </IconButton>\r\n            </Tooltip>\r\n        </Card>\r\n    );\r\n\r\n    return (\r\n        <Box sx={{ p: 2 }}>\r\n            <Typography variant=\"h6\" gutterBottom>Results by Product Picture</Typography>\r\n\r\n            {loadingProducts && <CircularProgress size={24} />}\r\n            {error && <Alert severity=\"error\" sx={{ mb: 2 }}>{error}</Alert>}\r\n\r\n            <Autocomplete\r\n                options={productImagesForAutocomplete} // Use memoized and transformed images\r\n                getOptionLabel={(option) => option.label || \"\"}\r\n                value={selectedProduct}\r\n                onChange={(event, newValue) => {\r\n                    setSelectedProduct(newValue);\r\n                }}\r\n                isOptionEqualToValue={(option, value) => option.id === value.id}\r\n                renderInput={(params) => (\r\n                    <TextField\r\n                        {...params}\r\n                        label={`Select Product Image (${ipCategory})`}\r\n                        variant=\"outlined\"\r\n                        size=\"small\"\r\n                        disabled={loadingProducts || productImagesForAutocomplete.length === 0} // Use memoized images for disabled check\r\n                    />\r\n                )}\r\n                renderOption={(htmlProps, option) => {\r\n                    // Destructure 'key' from htmlProps to prevent spreading it if it exists,\r\n                    // as this can cause React warnings. The actual React key for the list item\r\n                    // should be option.id.\r\n                    const { key: muiProvidedKey, ...otherHtmlProps } = htmlProps;\r\n                    return (\r\n                        <Box component=\"li\" sx={{ '& > img': { mr: 2, flexShrink: 0 } }} {...otherHtmlProps} key={option.id}>\r\n                            <img\r\n                                loading=\"lazy\"\r\n                                width=\"40\"\r\n                                // option.imageUrl should be valid due to filtering,\r\n                                // but pass null if it's somehow empty to avoid empty src warning.\r\n                                src={option.imageUrl || null}\r\n                                alt={option.filename || 'Product thumbnail'} // Fallback for alt text\r\n                            />\r\n                            {option.label || 'Unnamed Product'} ({option.id ? option.id.substring(0, 8) : 'N/A'}...)\r\n                        </Box>\r\n                    );\r\n                }}\r\n                sx={{ mb: 2, maxWidth: 500 }}\r\n                disabled={loadingProducts}\r\n            />\r\n\r\n            {loadingResults && <CircularProgress />}\r\n\r\n            {selectedProduct && results && !loadingResults && (\r\n                <Grid container spacing={3}>\r\n                    {/* Selected Product Image - uses results.product_image */}\r\n                    <Grid item xs={12} md={3}>\r\n                        <Typography variant=\"subtitle1\" gutterBottom>Selected Product</Typography>\r\n                        <Card>\r\n                            <CardMedia\r\n                                component=\"img\"\r\n                                sx={{ height: 200, objectFit: 'contain', cursor: 'pointer' }}\r\n                                image={getImageUrl(results.product_image?.id)}\r\n                                alt={`${results.product_image?.filename || 'N/A'} - ${results.product_image?.ip_category || 'N/A'}`}\r\n                                onClick={() => results.product_image?.id && handleImageClick(getImageUrl(results.product_image.id))}\r\n                            />\r\n                            <CardContent>\r\n                                <Typography variant=\"body2\" sx={{ wordBreak: 'break-all' }}>{results.product_image?.filename || 'N/A'}</Typography>\r\n                                <Typography variant=\"caption\" display=\"block\" sx={{ fontStyle: 'italic', mt: 0.5 }}>\r\n                                    Category: {results.product_image?.ip_category || 'N/A'}\r\n                                </Typography>\r\n                            </CardContent>\r\n                         {/* Known Correct Matches Section - uses results.ground_truth_ips */}\r\n                         {results.ground_truth_ips && results.ground_truth_ips.length > 0 && (\r\n                            <Box mt={2}>\r\n                                <Typography variant=\"subtitle1\" gutterBottom>Known Correct Matches</Typography>\r\n                                <Grid container spacing={1}>\r\n                                    {/* Each item in ground_truth_ips is an IPImageSchema */}\r\n                                    {results.ground_truth_ips.map((gtImage) => (\r\n                                        <Grid item key={gtImage.id} xs={6} sm={4} md={6}>\r\n                                            <Card sx={{ position: 'relative', textAlign: 'center', p: 1 }}>\r\n                                                <Tooltip title={gtImage.filename || 'Ground Truth Image'}>\r\n                                                    <CardMedia\r\n                                                        component=\"img\"\r\n                                                        sx={{ height: 80, objectFit: 'contain', cursor: 'pointer', margin: '0 auto' }}\r\n                                                        image={getImageUrl(gtImage.id)}\r\n                                                        alt={gtImage.filename || 'Ground Truth'}\r\n                                                        onClick={() => handleImageClick(getImageUrl(gtImage.id))}\r\n                                                    />\r\n                                                </Tooltip>\r\n                                                <Typography variant=\"body2\" sx={{ mt: 1, wordBreak: 'break-all' }}>\r\n                                                    {gtImage.filename || 'N/A'}\r\n                                                </Typography>\r\n                                                <IconButton\r\n                                                    onClick={() => handleMarkAsGroundTruth(gtImage.id, true)}\r\n                                                    sx={{\r\n                                                        mt: 1,\r\n                                                        color: 'error.main',\r\n                                                        border: '1px solid',\r\n                                                        borderColor: 'error.main',\r\n                                                        borderRadius: '50%',\r\n                                                        width: 36,\r\n                                                        height: 36,\r\n                                                        display: 'inline-flex',\r\n                                                        justifyContent: 'center',\r\n                                                        alignItems: 'center',\r\n                                                    }}\r\n                                                >\r\n                                                    <ClearIcon />\r\n                                                </IconButton>\r\n                                            </Card>\r\n                                        </Grid>\r\n                                    ))}\r\n                                </Grid>\r\n                            </Box>\r\n                        )}\r\n                        </Card>\r\n                    </Grid>\r\n\r\n                    {/* Results Area - uses results.results_by_model */}\r\n                    <Grid item xs={12} md={9}>\r\n                        <Typography variant=\"subtitle1\" gutterBottom>Model Suggestions</Typography>\r\n                        {results.results_by_model && Object.entries(results.results_by_model).length > 0 ? (\r\n                            Object.entries(results.results_by_model).map(([modelName, suggestions]) => (\r\n                                <Box key={modelName} mb={3}>\r\n                                    <Typography variant=\"h6\" component=\"div\" gutterBottom>{modelName}</Typography>\r\n                                    {suggestions && suggestions.length > 0 ? (\r\n                                        <>\r\n                                            <Grid container spacing={1}>\r\n                                                {/* Determine how many suggestions to show for this model */}\r\n                                                {suggestions.slice(0, visibleCounts[modelName] || DEFAULT_VISIBLE_SUGGESTIONS).map((suggestion) => (\r\n                                                    <Grid item xs={12} sm={6} md={4} key={`${modelName}-${suggestion.ip_image_id}`}>\r\n                                                        {renderSuggestion(suggestion)}\r\n                                                    </Grid>\r\n                                                ))}\r\n                                            </Grid>\r\n                                            {/* Show \"Show More\" button if there are more suggestions to display */}\r\n                                            {(visibleCounts[modelName] || DEFAULT_VISIBLE_SUGGESTIONS) < suggestions.length && (\r\n                                                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2, width: '100%' }}>\r\n                                                    <Button\r\n                                                        variant=\"outlined\"\r\n                                                        onClick={() => {\r\n                                                            setVisibleCounts(prevCounts => ({\r\n                                                                ...prevCounts,\r\n                                                                [modelName]: (prevCounts[modelName] || DEFAULT_VISIBLE_SUGGESTIONS) + DEFAULT_VISIBLE_SUGGESTIONS\r\n                                                            }));\r\n                                                        }}\r\n                                                    >\r\n                                                        Show {DEFAULT_VISIBLE_SUGGESTIONS} More Suggestions\r\n                                                    </Button>\r\n                                                </Box>\r\n                                            )}\r\n                                        </>\r\n                                    ) : (\r\n                                        <Typography variant=\"body2\">No suggestions found for this model.</Typography>\r\n                                    )}\r\n                                </Box>\r\n                            ))\r\n                        ) : (\r\n                             <Alert severity=\"info\">No model results available for this product image.</Alert>\r\n                        )}\r\n                    </Grid>\r\n                </Grid>\r\n            )}\r\n\r\n            {/* Image Enlarge Dialog */}\r\n            <Dialog open={Boolean(imageToEnlarge)} onClose={handleCloseDialog} maxWidth=\"md\">\r\n                <DialogTitle>Enlarged Image</DialogTitle>\r\n                <DialogContent>\r\n                    <img src={imageToEnlarge} alt=\"Enlarged view\" style={{ width: '100%', height: 'auto' }} />\r\n                </DialogContent>\r\n                <Button onClick={handleCloseDialog} sx={{ m: 1 }}>Close</Button>\r\n            </Dialog>\r\n        </Box>\r\n    );\r\n};\r\n\r\nexport default ByProductView;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,EAAAC,aAAA,IAAAC,cAAA,QAAQ,OAAO;AACxE,SACIC,GAAG,EACHC,UAAU,EACVC,gBAAgB,EAChBC,KAAK,EACLC,IAAI,EACJC,YAAY,EACZC,SAAS,EACTC,IAAI,EACJC,WAAW,EACXC,SAAS,EACTC,UAAU,EACVC,OAAO,EACPC,MAAM,EACNC,aAAa,EACbC,WAAW,EACXC,MAAM,QACH,eAAe;AACtB,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,OAAOC,WAAW,MAAM,6BAA6B,CAAC,CAAC;AACvD,OAAOC,uBAAuB,MAAM,yCAAyC,CAAC,CAAC;AAC/E,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,SAAS,MAAM,2BAA2B;AACjD;AACA,SAASC,UAAU,EAAEC,mBAAmB,EAAEC,cAAc,EAAEC,iBAAiB,QAAQ,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExH,MAAMC,yBAAyB,GAAG,EAAE,CAAC,CAAC;AACtC,MAAMC,2BAA2B,GAAG,CAAC,CAAC,CAAC;;AAEvC,MAAMC,aAAa,GAAGA,CAAC;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EACtCC,OAAO,CAACC,GAAG,CAAC,sCAAsCR,UAAU,EAAE,CAAC,CAAC,CAAC;EACjE,MAAM,CAACS,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAC9D,MAAM,CAACiD,eAAe,EAAEC,kBAAkB,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC9D,MAAM,CAACmD,OAAO,EAAEC,UAAU,CAAC,GAAGpD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACqD,eAAe,EAAEC,kBAAkB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACuD,cAAc,EAAEC,iBAAiB,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACyD,KAAK,EAAEC,QAAQ,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC2D,cAAc,EAAEC,iBAAiB,CAAC,GAAG5D,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC6D,aAAa,EAAEC,gBAAgB,CAAC,GAAG9D,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAExD,MAAM+D,YAAY,GAAG,kBAAkB,CAAC,CAAC;;EAEzC;EACA,MAAMC,4BAA4B,GAAG7D,OAAO,CAAC,MAAM;IAC/C0C,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEC,gBAAgB,CAAC,CAAC,CAAC;;IAEjE,MAAMkB,WAAW,GAAGlB,gBAAgB,IAAI,EAAE;IAC1CF,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEmB,WAAW,CAAC;IAC3EpB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEmB,WAAW,CAACC,MAAM,CAAC;IAEhE,MAAMC,YAAY,GAAGF,WAAW,CAACG,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KAAK;MACnD,IAAIA,KAAK,GAAG,CAAC,EAAE;QAAE;QACbzB,OAAO,CAACC,GAAG,CAAC,+BAA+BwB,KAAK,GAAG,EAAEC,IAAI,CAACC,SAAS,CAACH,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QACpFxB,OAAO,CAACC,GAAG,CAAC,uBAAuBwB,KAAK,cAAc,EAAED,KAAK,CAACI,QAAQ,EAAE,oBAAoB,EAAEJ,KAAK,CAACK,iBAAiB,CAAC;MAC1H;MACA,OAAO;QACHC,EAAE,EAAEN,KAAK,CAACI,QAAQ;QAClBG,KAAK,EAAEP,KAAK,CAACK,iBAAiB,IAAI,eAAe;QACjDG,QAAQ,EAAER,KAAK,CAACI,QAAQ,GAAG,GAAGV,YAAY,SAASM,KAAK,CAACI,QAAQ,EAAE,GAAG,IAAI;QAAE;QAC5EK,WAAW,EAAET,KAAK,CAACS;MACvB,CAAC;IACL,CAAC,CAAC;IACFjC,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEqB,YAAY,CAAC;IACnEtB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEqB,YAAY,CAACD,MAAM,CAAC;IAElE,MAAMa,cAAc,GAAGZ,YAAY,CAACa,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACN,EAAE,IAAIO,MAAM,CAACD,MAAM,CAACN,EAAE,CAAC,CAACQ,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;IAClGtC,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEiC,cAAc,CAAC,CAAC,CAAC;;IAE5E,OAAOA,cAAc;EACzB,CAAC,EAAE,CAAChC,gBAAgB,EAAEgB,YAAY,CAAC,CAAC;;EAEpC;EACA,MAAMqB,WAAW,GAAIC,OAAO,IAAKA,OAAO,GAAG,yBAAyBA,OAAO,EAAE,GAAG,EAAE;;EAElF;EACApF,SAAS,CAAC,MAAM;IACZ,MAAMqF,kBAAkB,GAAG,MAAAA,CAAA,KAAY;MACnChC,kBAAkB,CAAC,IAAI,CAAC;MACxBI,QAAQ,CAAC,IAAI,CAAC;MACdV,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC;MACzBE,kBAAkB,CAAC,IAAI,CAAC;MACxBE,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACA,MAAMmC,MAAM,GAAG;UACXC,UAAU,EAAE,SAAS;UACrBV,WAAW,EAAExC;QACjB,CAAC;QACDO,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEyC,MAAM,CAAC,CAAC,CAAC;QACnD,MAAME,QAAQ,GAAG,MAAM9D,UAAU,CAAC4D,MAAM,CAAC;QACzC;QACA1C,OAAO,CAACC,GAAG,CAAC,uDAAuD,EAAE2C,QAAQ,CAACC,IAAI,CAACC,MAAM,GAAGF,QAAQ,CAACC,IAAI,CAACC,MAAM,CAACzB,MAAM,GAAG,CAAC,CAAC;QAC5H,IAAIuB,QAAQ,CAACC,IAAI,CAACC,MAAM,IAAIF,QAAQ,CAACC,IAAI,CAACC,MAAM,CAACzB,MAAM,GAAG,CAAC,EAAE;UACzDrB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEyB,IAAI,CAACC,SAAS,CAACiB,QAAQ,CAACC,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;UAC7F9C,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE8C,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACC,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7F;QACA;;QAEA;QACA3C,mBAAmB,CAACyC,QAAQ,CAACC,IAAI,CAACC,MAAM,IAAI,EAAE,CAAC;MACnD,CAAC,CAAC,OAAOG,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,kBAAA,EAAAC,cAAA,EAAAC,mBAAA;QACVrD,OAAO,CAACY,KAAK,CAAC,gCAAgC,EAAEqC,GAAG,CAAC;QACpDpC,QAAQ,CAAC,qCAAqCpB,UAAU,KAAK,EAAAyD,aAAA,GAAAD,GAAG,CAACL,QAAQ,cAAAM,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcL,IAAI,cAAAM,kBAAA,uBAAlBA,kBAAA,CAAoBG,MAAM,OAAAF,cAAA,GAAIH,GAAG,CAACL,QAAQ,cAAAQ,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcP,IAAI,cAAAQ,mBAAA,uBAAlBA,mBAAA,CAAoBzC,KAAK,KAAIqC,GAAG,CAACM,OAAO,EAAE,CAAC;MAC1I,CAAC,SAAS;QACN9C,kBAAkB,CAAC,KAAK,CAAC;MAC7B;IACJ,CAAC;IAED,IAAIhB,UAAU,EAAE;MACZgD,kBAAkB,CAAC,CAAC;IACxB;EACJ,CAAC,EAAE,CAAChD,UAAU,CAAC,CAAC;;EAEhB;EACArC,SAAS,CAAC,MAAM;IACZ,MAAMoG,sBAAsB,GAAG,MAAAA,CAAA,KAAY;MACvC,IAAI,CAACpD,eAAe,EAAE;QAClBG,UAAU,CAAC,IAAI,CAAC;QAChBU,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB;MACJ;MACAN,iBAAiB,CAAC,IAAI,CAAC;MACvBE,QAAQ,CAAC,IAAI,CAAC;MACd,IAAI;QACA;QACA;QACA,MAAM6B,MAAM,GAAG;UAAEe,KAAK,EAAEnE;QAA0B,CAAC,CAAC,CAAC;QACrD,MAAMsD,QAAQ,GAAG,MAAM7D,mBAAmB,CAACqB,eAAe,CAAC0B,EAAE,EAAEY,MAAM,CAAC;QACtE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAnC,UAAU,CAACqC,QAAQ,CAACC,IAAI,CAAC;;QAEzB;QACA,IAAID,QAAQ,CAACC,IAAI,IAAID,QAAQ,CAACC,IAAI,CAACa,gBAAgB,EAAE;UACjD,MAAMC,aAAa,GAAG,CAAC,CAAC;UACxBZ,MAAM,CAACC,IAAI,CAACJ,QAAQ,CAACC,IAAI,CAACa,gBAAgB,CAAC,CAACE,OAAO,CAACC,SAAS,IAAI;YAC7DF,aAAa,CAACE,SAAS,CAAC,GAAGtE,2BAA2B;UAC1D,CAAC,CAAC;UACF0B,gBAAgB,CAAC0C,aAAa,CAAC;QACnC,CAAC,MAAM;UACH1C,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B;MAEJ,CAAC,CAAC,OAAOgC,GAAG,EAAE;QAAA,IAAAa,cAAA,EAAAC,mBAAA,EAAAC,cAAA,EAAAC,mBAAA;QACVjE,OAAO,CAACY,KAAK,CAAC,sCAAsCR,eAAe,CAAC0B,EAAE,GAAG,EAAEmB,GAAG,CAAC;QAC/EpC,QAAQ,CAAC,sCAAsCT,eAAe,CAAC8D,QAAQ,KAAK,EAAAJ,cAAA,GAAAb,GAAG,CAACL,QAAQ,cAAAkB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcjB,IAAI,cAAAkB,mBAAA,uBAAlBA,mBAAA,CAAoBT,MAAM,OAAAU,cAAA,GAAIf,GAAG,CAACL,QAAQ,cAAAoB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcnB,IAAI,cAAAoB,mBAAA,uBAAlBA,mBAAA,CAAoBrD,KAAK,KAAIqC,GAAG,CAACM,OAAO,EAAE,CAAC;QACrJhD,UAAU,CAAC,IAAI,CAAC;QAChBU,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1B,CAAC,SAAS;QACNN,iBAAiB,CAAC,KAAK,CAAC;MAC5B;IACJ,CAAC;IAED6C,sBAAsB,CAAC,CAAC;EAC5B,CAAC,EAAE,CAACpD,eAAe,EAAEd,yBAAyB,EAAEC,2BAA2B,CAAC,CAAC,CAAC,CAAC;;EAE/E,MAAM4E,uBAAuB,GAAG9G,WAAW,CAAC,OAAO+G,kBAAkB,EAAEC,sBAAsB,KAAK;IAC9F,IAAI,CAACjE,eAAe,IAAI,CAACA,eAAe,CAAC0B,EAAE,EAAE;IAC7CjB,QAAQ,CAAC,IAAI,CAAC;IACd,IAAI;MACA,IAAIwD,sBAAsB,EAAE;QACxB,MAAMpF,iBAAiB,CAACmB,eAAe,CAAC0B,EAAE,EAAEsC,kBAAkB,CAAC;MACnE,CAAC,MAAM;QACH,MAAMpF,cAAc,CAACoB,eAAe,CAAC0B,EAAE,EAAEsC,kBAAkB,CAAC;MAChE;MACA;MACA,MAAM1B,MAAM,GAAG;QAAEe,KAAK,EAAEnE;MAA0B,CAAC,CAAC,CAAC;MACrD,MAAMsD,QAAQ,GAAG,MAAM7D,mBAAmB,CAACqB,eAAe,CAAC0B,EAAE,EAAEY,MAAM,CAAC;MACtEnC,UAAU,CAACqC,QAAQ,CAACC,IAAI,CAAC;MACzB;MACA;IACJ,CAAC,CAAC,OAAOI,GAAG,EAAE;MAAA,IAAAqB,cAAA,EAAAC,mBAAA,EAAAC,cAAA,EAAAC,mBAAA;MACVzE,OAAO,CAACY,KAAK,CAAC,8BAA8B,EAAEqC,GAAG,CAAC;MAClDpC,QAAQ,CAAC,kCAAkC,EAAAyD,cAAA,GAAArB,GAAG,CAACL,QAAQ,cAAA0B,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAczB,IAAI,cAAA0B,mBAAA,uBAAlBA,mBAAA,CAAoBjB,MAAM,OAAAkB,cAAA,GAAIvB,GAAG,CAACL,QAAQ,cAAA4B,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc3B,IAAI,cAAA4B,mBAAA,uBAAlBA,mBAAA,CAAoB7D,KAAK,KAAIqC,GAAG,CAACM,OAAO,EAAE,CAAC;IACxH;EACJ,CAAC,EAAE,CAACnD,eAAe,EAAEd,yBAAyB,CAAC,CAAC,CAAC,CAAC;;EAElD,MAAMoF,gBAAgB,GAAI1C,QAAQ,IAAK;IACnCjB,iBAAiB,CAACiB,QAAQ,CAAC;EAC/B,CAAC;EAED,MAAM2C,iBAAiB,GAAGA,CAAA,KAAM;IAC5B5D,iBAAiB,CAAC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,MAAM6D,gBAAgB,GAAIC,UAAU;IAAA,IAAAC,qBAAA,EAAAC,sBAAA;IAAA,oBAChC5F,OAAA,CAACnB,IAAI;MAACgH,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,EAAE,EAAE,CAAC;QAAEC,UAAU,EAAE,QAAQ;QAAEC,QAAQ,EAAE,UAAU;QAAEC,KAAK,EAAE;MAAO,CAAE;MAAAC,QAAA,GAC3FT,UAAU,CAACU,eAAe,iBACvBpG,OAAA,CAACf,OAAO;QAACoH,KAAK,EAAC,qBAAqB;QAAAF,QAAA,eAChCnG,OAAA,CAACV,eAAe;UAACgH,KAAK,EAAC,SAAS;UAACT,EAAE,EAAE;YAAEI,QAAQ,EAAE,UAAU;YAAEM,GAAG,EAAE,CAAC;YAAEC,IAAI,EAAE,CAAC;YAAEC,MAAM,EAAE,CAAC;YAAEC,eAAe,EAAE,OAAO;YAAEC,YAAY,EAAE;UAAM;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvI,CACZ,eACD/G,OAAA,CAACjB,SAAS;QACNiI,SAAS,EAAC,KAAK;QACfnB,EAAE,EAAE;UAAEK,KAAK,EAAE,EAAE;UAAEe,MAAM,EAAE,EAAE;UAAEC,SAAS,EAAE,SAAS;UAAEC,MAAM,EAAE,SAAS;UAAEC,CAAC,EAAE;QAAI,CAAE;QAC/E/E,KAAK,EAAEe,WAAW,CAACsC,UAAU,CAAC2B,WAAW,CAAE,CAAC;QAAA;QAC5CC,GAAG,EAAE5B,UAAU,CAAC6B,WAAW,IAAI,UAAW,CAAC;QAAA;QAC3CC,OAAO,EAAEA,CAAA,KAAMjC,gBAAgB,CAACnC,WAAW,CAACsC,UAAU,CAAC2B,WAAW,CAAC;MAAE;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE,CAAC,eACF/G,OAAA,CAAC1B,GAAG;QAACuH,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAE2B,aAAa,EAAE,QAAQ;UAAEC,QAAQ,EAAE,CAAC;UAAEC,cAAc,EAAE,QAAQ;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAzB,QAAA,gBAChGnG,OAAA,CAACzB,UAAU;UAACsJ,OAAO,EAAC,SAAS;UAAChC,EAAE,EAAE;YAAEiC,SAAS,EAAE;UAAY,CAAE;UAAA3B,QAAA,GACxDT,UAAU,CAAC6B,WAAW,IAAI,KAAK,EAAC,GAAC;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACb/G,OAAA,CAACzB,UAAU;UAACsJ,OAAO,EAAC,SAAS;UAAA1B,QAAA,GAAC,SACnB,GAAAR,qBAAA,IAAAC,sBAAA,GAACF,UAAU,CAACqC,gBAAgB,cAAAnC,sBAAA,uBAA3BA,sBAAA,CAA6BoC,OAAO,CAAC,CAAC,CAAC,cAAArC,qBAAA,cAAAA,qBAAA,GAAI,KAAK,EAAC,GAAC;QAAA;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACZ/G,OAAA,CAACzB,UAAU;UAACsJ,OAAO,EAAC,SAAS;UAAA1B,QAAA,GAAC,SACpB,EAACT,UAAU,CAACuC,QAAQ,IAAI,KAAK;QAAA;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eACL/G,OAAA,CAACf,OAAO;QAACoH,KAAK,EAAEX,UAAU,CAACU,eAAe,GAAG,0BAA0B,GAAG,sBAAuB;QAAAD,QAAA,eAC9FnG,OAAA,CAAChB,UAAU;UACPkJ,IAAI,EAAC,OAAO;UACZV,OAAO,EAAEA,CAAA,KAAMxC,uBAAuB,CAACU,UAAU,CAAC2B,WAAW,EAAE3B,UAAU,CAACU,eAAe,CAAE;UAC3FE,KAAK,EAAEZ,UAAU,CAACU,eAAe,GAAG,OAAO,GAAG,SAAU;UACxDP,EAAE,EAAE;YAAEsC,SAAS,EAAE,QAAQ;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAjC,QAAA,EAElCT,UAAU,CAACU,eAAe,gBAAGpG,OAAA,CAACR,uBAAuB;YAAAoH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG/G,OAAA,CAACT,WAAW;YAAAqH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAAA,CACV;EAED,oBACI/G,OAAA,CAAC1B,GAAG;IAACuH,EAAE,EAAE;MAAEuB,CAAC,EAAE;IAAE,CAAE;IAAAjB,QAAA,gBACdnG,OAAA,CAACzB,UAAU;MAACsJ,OAAO,EAAC,IAAI;MAACQ,YAAY;MAAAlC,QAAA,EAAC;IAA0B;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,EAE5E1F,eAAe,iBAAIrB,OAAA,CAACxB,gBAAgB;MAAC0J,IAAI,EAAE;IAAG;MAAAtB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACjDtF,KAAK,iBAAIzB,OAAA,CAACvB,KAAK;MAAC6J,QAAQ,EAAC,OAAO;MAACzC,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAI,QAAA,EAAE1E;IAAK;MAAAmF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAEhE/G,OAAA,CAACrB,YAAY;MACT4J,OAAO,EAAEvG,4BAA6B,CAAC;MAAA;MACvCwG,cAAc,EAAGvF,MAAM,IAAKA,MAAM,CAACL,KAAK,IAAI,EAAG;MAC/C6F,KAAK,EAAExH,eAAgB;MACvByH,QAAQ,EAAEA,CAACC,KAAK,EAAEC,QAAQ,KAAK;QAC3B1H,kBAAkB,CAAC0H,QAAQ,CAAC;MAChC,CAAE;MACFC,oBAAoB,EAAEA,CAAC5F,MAAM,EAAEwF,KAAK,KAAKxF,MAAM,CAACN,EAAE,KAAK8F,KAAK,CAAC9F,EAAG;MAChEmG,WAAW,EAAGvF,MAAM,iBAChBvD,OAAA,CAACpB,SAAS;QAAA,GACF2E,MAAM;QACVX,KAAK,EAAE,yBAAyBtC,UAAU,GAAI;QAC9CuH,OAAO,EAAC,UAAU;QAClBK,IAAI,EAAC,OAAO;QACZa,QAAQ,EAAE1H,eAAe,IAAIW,4BAA4B,CAACE,MAAM,KAAK,CAAE,CAAC;MAAA;QAAA0E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3E,CACH;MACFiC,YAAY,EAAEA,CAACC,SAAS,EAAEhG,MAAM,KAAK;QACjC;QACA;QACA;QACA,MAAM;UAAEiG,GAAG,EAAEC,cAAc;UAAE,GAAGC;QAAe,CAAC,GAAGH,SAAS;QAC5D,oBACI5K,cAAA,CAACC,GAAG;UAAC0I,SAAS,EAAC,IAAI;UAACnB,EAAE,EAAE;YAAE,SAAS,EAAE;cAAEuC,EAAE,EAAE,CAAC;cAAEiB,UAAU,EAAE;YAAE;UAAE,CAAE;UAAA,GAAKD,cAAc;UAAEF,GAAG,EAAEjG,MAAM,CAACN,EAAG;UAAA2G,MAAA;UAAAC,QAAA;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,gBAChG/G,OAAA;UACIwJ,OAAO,EAAC,MAAM;UACdtD,KAAK,EAAC;UACN;UACA;UAAA;UACAuD,GAAG,EAAExG,MAAM,CAACJ,QAAQ,IAAI,IAAK;UAC7ByE,GAAG,EAAErE,MAAM,CAAC8B,QAAQ,IAAI,mBAAoB,CAAC;QAAA;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,EACD9D,MAAM,CAACL,KAAK,IAAI,iBAAiB,EAAC,IAAE,EAACK,MAAM,CAACN,EAAE,GAAGM,MAAM,CAACN,EAAE,CAAC+G,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,EAAC,MACnF,CAAC;MAEd,CAAE;MACF7D,EAAE,EAAE;QAAEE,EAAE,EAAE,CAAC;QAAE4D,QAAQ,EAAE;MAAI,CAAE;MAC7BZ,QAAQ,EAAE1H;IAAgB;MAAAuF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC,EAEDxF,cAAc,iBAAIvB,OAAA,CAACxB,gBAAgB;MAAAoI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAEtC9F,eAAe,IAAIE,OAAO,IAAI,CAACI,cAAc,iBAC1CvB,OAAA,CAACtB,IAAI;MAACkL,SAAS;MAACC,OAAO,EAAE,CAAE;MAAA1D,QAAA,gBAEvBnG,OAAA,CAACtB,IAAI;QAACoL,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA7D,QAAA,gBACrBnG,OAAA,CAACzB,UAAU;UAACsJ,OAAO,EAAC,WAAW;UAACQ,YAAY;UAAAlC,QAAA,EAAC;QAAgB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAC1E/G,OAAA,CAACnB,IAAI;UAAAsH,QAAA,gBACDnG,OAAA,CAACjB,SAAS;YACNiI,SAAS,EAAC,KAAK;YACfnB,EAAE,EAAE;cAAEoB,MAAM,EAAE,GAAG;cAAEC,SAAS,EAAE,SAAS;cAAEC,MAAM,EAAE;YAAU,CAAE;YAC7D9E,KAAK,EAAEe,WAAW,EAAA5C,qBAAA,GAACW,OAAO,CAAC8I,aAAa,cAAAzJ,qBAAA,uBAArBA,qBAAA,CAAuBmC,EAAE,CAAE;YAC9C2E,GAAG,EAAE,GAAG,EAAA7G,sBAAA,GAAAU,OAAO,CAAC8I,aAAa,cAAAxJ,sBAAA,uBAArBA,sBAAA,CAAuBsE,QAAQ,KAAI,KAAK,MAAM,EAAArE,sBAAA,GAAAS,OAAO,CAAC8I,aAAa,cAAAvJ,sBAAA,uBAArBA,sBAAA,CAAuBoC,WAAW,KAAI,KAAK,EAAG;YACpG0E,OAAO,EAAEA,CAAA;cAAA,IAAA0C,sBAAA;cAAA,OAAM,EAAAA,sBAAA,GAAA/I,OAAO,CAAC8I,aAAa,cAAAC,sBAAA,uBAArBA,sBAAA,CAAuBvH,EAAE,KAAI4C,gBAAgB,CAACnC,WAAW,CAACjC,OAAO,CAAC8I,aAAa,CAACtH,EAAE,CAAC,CAAC;YAAA;UAAC;YAAAiE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvG,CAAC,eACF/G,OAAA,CAAClB,WAAW;YAAAqH,QAAA,gBACRnG,OAAA,CAACzB,UAAU;cAACsJ,OAAO,EAAC,OAAO;cAAChC,EAAE,EAAE;gBAAEiC,SAAS,EAAE;cAAY,CAAE;cAAA3B,QAAA,EAAE,EAAAxF,sBAAA,GAAAQ,OAAO,CAAC8I,aAAa,cAAAtJ,sBAAA,uBAArBA,sBAAA,CAAuBoE,QAAQ,KAAI;YAAK;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eACnH/G,OAAA,CAACzB,UAAU;cAACsJ,OAAO,EAAC,SAAS;cAAC/B,OAAO,EAAC,OAAO;cAACD,EAAE,EAAE;gBAAEsE,SAAS,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAI,CAAE;cAAAjE,QAAA,GAAC,YACtE,EAAC,EAAAvF,sBAAA,GAAAO,OAAO,CAAC8I,aAAa,cAAArJ,sBAAA,uBAArBA,sBAAA,CAAuBkC,WAAW,KAAI,KAAK;YAAA;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,EAEhB5F,OAAO,CAACkJ,gBAAgB,IAAIlJ,OAAO,CAACkJ,gBAAgB,CAACnI,MAAM,GAAG,CAAC,iBAC7DlC,OAAA,CAAC1B,GAAG;YAAC8L,EAAE,EAAE,CAAE;YAAAjE,QAAA,gBACPnG,OAAA,CAACzB,UAAU;cAACsJ,OAAO,EAAC,WAAW;cAACQ,YAAY;cAAAlC,QAAA,EAAC;YAAqB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC/E/G,OAAA,CAACtB,IAAI;cAACkL,SAAS;cAACC,OAAO,EAAE,CAAE;cAAA1D,QAAA,EAEtBhF,OAAO,CAACkJ,gBAAgB,CAACjI,GAAG,CAAEkI,OAAO,iBAClCtK,OAAA,CAACtB,IAAI;gBAACoL,IAAI;gBAAkBC,EAAE,EAAE,CAAE;gBAACQ,EAAE,EAAE,CAAE;gBAACP,EAAE,EAAE,CAAE;gBAAA7D,QAAA,eAC5CnG,OAAA,CAACnB,IAAI;kBAACgH,EAAE,EAAE;oBAAEI,QAAQ,EAAE,UAAU;oBAAEuE,SAAS,EAAE,QAAQ;oBAAEpD,CAAC,EAAE;kBAAE,CAAE;kBAAAjB,QAAA,gBAC1DnG,OAAA,CAACf,OAAO;oBAACoH,KAAK,EAAEiE,OAAO,CAACvF,QAAQ,IAAI,oBAAqB;oBAAAoB,QAAA,eACrDnG,OAAA,CAACjB,SAAS;sBACNiI,SAAS,EAAC,KAAK;sBACfnB,EAAE,EAAE;wBAAEoB,MAAM,EAAE,EAAE;wBAAEC,SAAS,EAAE,SAAS;wBAAEC,MAAM,EAAE,SAAS;wBAAEsD,MAAM,EAAE;sBAAS,CAAE;sBAC9EpI,KAAK,EAAEe,WAAW,CAACkH,OAAO,CAAC3H,EAAE,CAAE;sBAC/B2E,GAAG,EAAEgD,OAAO,CAACvF,QAAQ,IAAI,cAAe;sBACxCyC,OAAO,EAAEA,CAAA,KAAMjC,gBAAgB,CAACnC,WAAW,CAACkH,OAAO,CAAC3H,EAAE,CAAC;oBAAE;sBAAAiE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5D;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG,CAAC,eACV/G,OAAA,CAACzB,UAAU;oBAACsJ,OAAO,EAAC,OAAO;oBAAChC,EAAE,EAAE;sBAAEuE,EAAE,EAAE,CAAC;sBAAEtC,SAAS,EAAE;oBAAY,CAAE;oBAAA3B,QAAA,EAC7DmE,OAAO,CAACvF,QAAQ,IAAI;kBAAK;oBAAA6B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,eACb/G,OAAA,CAAChB,UAAU;oBACPwI,OAAO,EAAEA,CAAA,KAAMxC,uBAAuB,CAACsF,OAAO,CAAC3H,EAAE,EAAE,IAAI,CAAE;oBACzDkD,EAAE,EAAE;sBACAuE,EAAE,EAAE,CAAC;sBACL9D,KAAK,EAAE,YAAY;sBACnBoE,MAAM,EAAE,WAAW;sBACnBC,WAAW,EAAE,YAAY;sBACzBhE,YAAY,EAAE,KAAK;sBACnBT,KAAK,EAAE,EAAE;sBACTe,MAAM,EAAE,EAAE;sBACVnB,OAAO,EAAE,aAAa;sBACtB6B,cAAc,EAAE,QAAQ;sBACxB3B,UAAU,EAAE;oBAChB,CAAE;oBAAAG,QAAA,eAEFnG,OAAA,CAACN,SAAS;sBAAAkH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX;cAAC,GA/BKuD,OAAO,CAAC3H,EAAE;gBAAAiE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgCpB,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACR;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGP/G,OAAA,CAACtB,IAAI;QAACoL,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAA7D,QAAA,gBACrBnG,OAAA,CAACzB,UAAU;UAACsJ,OAAO,EAAC,WAAW;UAACQ,YAAY;UAAAlC,QAAA,EAAC;QAAiB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAC1E5F,OAAO,CAACoD,gBAAgB,IAAIX,MAAM,CAACgH,OAAO,CAACzJ,OAAO,CAACoD,gBAAgB,CAAC,CAACrC,MAAM,GAAG,CAAC,GAC5E0B,MAAM,CAACgH,OAAO,CAACzJ,OAAO,CAACoD,gBAAgB,CAAC,CAACnC,GAAG,CAAC,CAAC,CAACsC,SAAS,EAAEmG,WAAW,CAAC,kBAClE7K,OAAA,CAAC1B,GAAG;UAAiByH,EAAE,EAAE,CAAE;UAAAI,QAAA,gBACvBnG,OAAA,CAACzB,UAAU;YAACsJ,OAAO,EAAC,IAAI;YAACb,SAAS,EAAC,KAAK;YAACqB,YAAY;YAAAlC,QAAA,EAAEzB;UAAS;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,EAC7E8D,WAAW,IAAIA,WAAW,CAAC3I,MAAM,GAAG,CAAC,gBAClClC,OAAA,CAAAE,SAAA;YAAAiG,QAAA,gBACInG,OAAA,CAACtB,IAAI;cAACkL,SAAS;cAACC,OAAO,EAAE,CAAE;cAAA1D,QAAA,EAEtB0E,WAAW,CAACC,KAAK,CAAC,CAAC,EAAEjJ,aAAa,CAAC6C,SAAS,CAAC,IAAItE,2BAA2B,CAAC,CAACgC,GAAG,CAAEsD,UAAU,iBAC1F1F,OAAA,CAACtB,IAAI;gBAACoL,IAAI;gBAACC,EAAE,EAAE,EAAG;gBAACQ,EAAE,EAAE,CAAE;gBAACP,EAAE,EAAE,CAAE;gBAAA7D,QAAA,EAC3BV,gBAAgB,CAACC,UAAU;cAAC,GADK,GAAGhB,SAAS,IAAIgB,UAAU,CAAC2B,WAAW,EAAE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAExE,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,EAEN,CAAClF,aAAa,CAAC6C,SAAS,CAAC,IAAItE,2BAA2B,IAAIyK,WAAW,CAAC3I,MAAM,iBAC3ElC,OAAA,CAAC1B,GAAG;cAACuH,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAE6B,cAAc,EAAE,QAAQ;gBAAEyC,EAAE,EAAE,CAAC;gBAAElE,KAAK,EAAE;cAAO,CAAE;cAAAC,QAAA,eACzEnG,OAAA,CAACX,MAAM;gBACHwI,OAAO,EAAC,UAAU;gBAClBL,OAAO,EAAEA,CAAA,KAAM;kBACX1F,gBAAgB,CAACiJ,UAAU,KAAK;oBAC5B,GAAGA,UAAU;oBACb,CAACrG,SAAS,GAAG,CAACqG,UAAU,CAACrG,SAAS,CAAC,IAAItE,2BAA2B,IAAIA;kBAC1E,CAAC,CAAC,CAAC;gBACP,CAAE;gBAAA+F,QAAA,GACL,OACQ,EAAC/F,2BAA2B,EAAC,mBACtC;cAAA;gBAAAwG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CACR;UAAA,eACH,CAAC,gBAEH/G,OAAA,CAACzB,UAAU;YAACsJ,OAAO,EAAC,OAAO;YAAA1B,QAAA,EAAC;UAAoC;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAC/E;QAAA,GA/BKrC,SAAS;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgCd,CACR,CAAC,gBAED/G,OAAA,CAACvB,KAAK;UAAC6J,QAAQ,EAAC,MAAM;UAAAnC,QAAA,EAAC;QAAkD;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACpF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACT,eAGD/G,OAAA,CAACd,MAAM;MAAC8L,IAAI,EAAEC,OAAO,CAACtJ,cAAc,CAAE;MAACuJ,OAAO,EAAE1F,iBAAkB;MAACmE,QAAQ,EAAC,IAAI;MAAAxD,QAAA,gBAC5EnG,OAAA,CAACZ,WAAW;QAAA+G,QAAA,EAAC;MAAc;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACzC/G,OAAA,CAACb,aAAa;QAAAgH,QAAA,eACVnG,OAAA;UAAKyJ,GAAG,EAAE9H,cAAe;UAAC2F,GAAG,EAAC,eAAe;UAAC6D,KAAK,EAAE;YAAEjF,KAAK,EAAE,MAAM;YAAEe,MAAM,EAAE;UAAO;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/E,CAAC,eAChB/G,OAAA,CAACX,MAAM;QAACmI,OAAO,EAAEhC,iBAAkB;QAACK,EAAE,EAAE;UAAEuF,CAAC,EAAE;QAAE,CAAE;QAAAjF,QAAA,EAAC;MAAK;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5D,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEd,CAAC;AAACxG,EAAA,CApXIF,aAAa;AAAAgL,EAAA,GAAbhL,aAAa;AAsXnB,eAAeA,aAAa;AAAC,IAAAgL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}