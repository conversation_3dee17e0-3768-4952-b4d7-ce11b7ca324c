{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents\\\\Programing\\\\TRO\\\\ModelTestsWorkbench\\\\frontend\\\\src\\\\components\\\\model-test-workbench\\\\ByModelView.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useRef } from 'react';\nimport { Box, Typography, Select, MenuItem, FormControl, InputLabel, Grid, Card, CardMedia, CardContent, CircularProgress, Alert, Pagination, IconButton, Tooltip, Modal, Paper, Button // Added Button\n} from '@mui/material';\nimport ThumbUpIcon from '@mui/icons-material/ThumbUp';\nimport ThumbUpOutlinedIcon from '@mui/icons-material/ThumbUpOutlined';\nimport ZoomInIcon from '@mui/icons-material/ZoomIn';\nimport ClearIcon from '@mui/icons-material/Clear'; // Added ClearIcon\n// Use named imports for API functions\nimport { listModels, getCombinedScores, getResultsByModel, addGroundTruth, removeGroundTruth } from '../../services/api_model_workbench'; // Added getCombinedScores\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RESULTS_PER_PAGE = 10; // Or make this configurable\n\nconst ByModelView = ({\n  ipCategory\n}) => {\n  _s();\n  const [models, setModels] = useState([]);\n  const [selectedModelId, setSelectedModelId] = useState('');\n  const [results, setResults] = useState([]);\n  const [currentPage, setCurrentPage] = useState(1); // Step 1: Dedicated current page state\n  const [paginationMeta, setPaginationMeta] = useState({\n    totalPages: 1\n  }); // Step 2: Separate pagination meta\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [imageModalOpen, setImageModalOpen] = useState(false);\n  const [selectedImage, setSelectedImage] = useState('');\n  const INITIAL_SUGGESTIONS_COUNT = 5; // Number of suggestions to show initially\n  // Removed suggestionsApiLimit, will manage per product and a max for API\n  const [lastApiLimit, setLastApiLimit] = useState(INITIAL_SUGGESTIONS_COUNT);\n  const handleOpenImageModal = imageUrl => {\n    setSelectedImage(imageUrl);\n    setImageModalOpen(true);\n  };\n  const handleCloseImageModal = () => {\n    setImageModalOpen(false);\n    setSelectedImage('');\n  };\n\n  // Fetch models and combined scores\n  useEffect(() => {\n    const fetchModels = async () => {\n      setLoading(true);\n      setError(null);\n      try {\n        // Fetch active models for the category\n        // TODO: The backend listModels doesn't filter by category yet, filter client-side for now\n        const modelsResponse = await listModels(); // Fetch all models\n        const combinedScoresResponse = await getCombinedScores({\n          ip_category: ipCategory,\n          is_active: true\n        }); // Fetch active combined scores for category\n\n        // Combine and filter models applicable to this category\n        const applicableModels = (modelsResponse.data || []).filter(m => m.is_active && (m.applicable_ip_category.includes(ipCategory) || m.applicable_ip_category.includes('all'))).map(m => ({\n          id: m.model_id,\n          name: m.model_name,\n          type: 'model'\n        }));\n        const applicableCombined = (combinedScoresResponse.data || []).map(c => ({\n          id: c.config_id,\n          name: c.config_name,\n          type: 'combined'\n        }));\n        const allApplicable = [...applicableModels, ...applicableCombined];\n        setModels(allApplicable);\n        if (allApplicable.length > 0) {\n          // Optionally pre-select the first model\n          // setSelectedModelId(allApplicable[0].id);\n        } else {\n          setError(`No active models or combined scores found for ${ipCategory}.`);\n        }\n      } catch (err) {\n        console.error(\"Error fetching models:\", err);\n        setError(`Failed to fetch models for ${ipCategory}. Please try again later.`);\n        setModels([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (ipCategory) {\n      fetchModels();\n    }\n  }, [ipCategory]);\n\n  // Step 4: Refactor useEffect for Resetting Page on Filter Change\n  useEffect(() => {\n    // Reset to page 1 when selectedModelId or ipCategory changes\n    // This will trigger the data fetching effect for the new page 1\n    setCurrentPage(1);\n    setResults([]); // Clear results, so displayedSuggestionsCount are effectively reset for new model/category\n    setLastApiLimit(INITIAL_SUGGESTIONS_COUNT); // Reset last API limit as well\n  }, [selectedModelId, ipCategory]);\n\n  // Step 5: Review fetchResults (or equivalent data fetching function)\n  // Step 3: Refactor useEffect for Fetching Results\n  const fetchResults = useCallback(async (pageToFetch, currentResultsSnapshot) => {\n    if (!selectedModelId) {\n      setResults([]);\n      setPaginationMeta({\n        totalPages: 1\n      });\n      return;\n    }\n    setLoading(true);\n    setError(null);\n    try {\n      var _response$data$pagina;\n      let limitForApi = INITIAL_SUGGESTIONS_COUNT;\n      if (currentResultsSnapshot && currentResultsSnapshot.length > 0) {\n        limitForApi = Math.max(INITIAL_SUGGESTIONS_COUNT, ...currentResultsSnapshot.map(r => r.displayedSuggestionsCount || INITIAL_SUGGESTIONS_COUNT));\n      }\n      setLastApiLimit(limitForApi);\n      const params = {\n        model_id: selectedModelId,\n        ip_category: ipCategory,\n        page: pageToFetch,\n        per_page: RESULTS_PER_PAGE,\n        limit: limitForApi\n      };\n      const response = await getResultsByModel(params);\n      const newApiResultsForPage = response.data.results || [];\n      const processedResults = newApiResultsForPage.map(apiProd => {\n        const existingProductState = currentResultsSnapshot.find(r => {\n          var _r$product_image, _apiProd$product_imag;\n          return ((_r$product_image = r.product_image) === null || _r$product_image === void 0 ? void 0 : _r$product_image.id) === ((_apiProd$product_imag = apiProd.product_image) === null || _apiProd$product_imag === void 0 ? void 0 : _apiProd$product_imag.id);\n        });\n        const displayCount = existingProductState ? existingProductState.displayedSuggestionsCount : INITIAL_SUGGESTIONS_COUNT;\n        return {\n          ...apiProd,\n          displayedSuggestionsCount: displayCount,\n          model_suggestions: apiProd.model_suggestions || [] // Ensure it's an array\n        };\n      });\n      setResults(processedResults);\n      setPaginationMeta({\n        totalPages: ((_response$data$pagina = response.data.pagination) === null || _response$data$pagina === void 0 ? void 0 : _response$data$pagina.total_pages) || 1\n      });\n    } catch (err) {\n      var _err$response, _err$response$data, _err$response2, _err$response2$data;\n      console.error(\"Error fetching results:\", err);\n      setError(`Failed to fetch results for model ${selectedModelId}. ${((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.detail) || ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.error) || err.message}`);\n      setResults([]); // Clear results on error\n      setPaginationMeta({\n        totalPages: 1\n      });\n    } finally {\n      setLoading(false);\n    }\n  }, [selectedModelId, ipCategory]); // Removed suggestionsApiLimit, fetchResults itself doesn't depend on 'results' state directly in definition\n\n  // Effect for fetching results based on currentPage, selectedModelId, or ipCategory\n  useEffect(() => {\n    if (selectedModelId && ipCategory) {\n      // Pass the current 'results' state to fetchResults for limit calculation\n      fetchResults(currentPage, results);\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [selectedModelId, ipCategory, currentPage, fetchResults]); // REMOVED 'results' to prevent loop\n\n  const handleModelChange = event => {\n    setSelectedModelId(event.target.value);\n    // setCurrentPage(1) is handled by the useEffect hook dependent on selectedModelId\n  };\n  const handlePageChange = (event, value) => {\n    setCurrentPage(value); // Step 1: Pagination onChange calls setCurrentPage\n  };\n  const handleGroundTruthToggle = async (productImageId, ipImageId, currentIsGroundTruth) => {\n    setError(null);\n    try {\n      if (currentIsGroundTruth) {\n        await removeGroundTruth(productImageId, ipImageId);\n      } else {\n        await addGroundTruth(productImageId, ipImageId);\n      }\n      fetchResults(currentPage, results); // Refresh results for the current page\n    } catch (err) {\n      var _err$response3, _err$response3$data, _err$response4, _err$response4$data;\n      console.error(\"Error updating ground truth:\", err);\n      setError(`Failed to update ground truth. ${((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.detail) || ((_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : (_err$response4$data = _err$response4.data) === null || _err$response4$data === void 0 ? void 0 : _err$response4$data.error) || err.message}`);\n    }\n  };\n  const handleRemoveDirectGroundTruth = async (productId, ipId) => {\n    if (!productId) {\n      setError(\"Cannot remove ground truth: Product ID is missing.\");\n      console.error(\"Product ID is missing for removeGroundTruth call.\");\n      return;\n    }\n    setError(null);\n    try {\n      await removeGroundTruth(productId, ipId);\n      fetchResults(currentPage, results); // Refresh results for the current page\n    } catch (err) {\n      var _err$response5, _err$response5$data, _err$response6, _err$response6$data;\n      console.error(\"Error removing ground truth directly:\", err);\n      setError(`Failed to remove ground truth. ${((_err$response5 = err.response) === null || _err$response5 === void 0 ? void 0 : (_err$response5$data = _err$response5.data) === null || _err$response5$data === void 0 ? void 0 : _err$response5$data.detail) || ((_err$response6 = err.response) === null || _err$response6 === void 0 ? void 0 : (_err$response6$data = _err$response6.data) === null || _err$response6$data === void 0 ? void 0 : _err$response6$data.error) || err.message}`);\n    }\n  };\n  const handleShowMoreSuggestions = productId => {\n    // Create the next state for 'results' by updating the specific product's display count\n    const updatedResults = results.map(p => {\n      var _p$product_image;\n      if (((_p$product_image = p.product_image) === null || _p$product_image === void 0 ? void 0 : _p$product_image.id) === productId) {\n        return {\n          ...p,\n          displayedSuggestionsCount: (p.displayedSuggestionsCount || INITIAL_SUGGESTIONS_COUNT) + INITIAL_SUGGESTIONS_COUNT\n        };\n      }\n      return p;\n    });\n    setResults(updatedResults); // Apply the UI change immediately\n\n    // Now, determine if an API call is needed because the overall max displayed suggestions increased\n    const overallMaxDisplayed = Math.max(INITIAL_SUGGESTIONS_COUNT, ...updatedResults.map(r => r.displayedSuggestionsCount || INITIAL_SUGGESTIONS_COUNT));\n\n    // If the new max count required for display exceeds the limit used for the last API call,\n    // then we need to fetch results again with an updated limit.\n    if (overallMaxDisplayed > lastApiLimit) {\n      // Pass currentPage and the *newly updated* results state (updatedResults)\n      // so fetchResults can calculate the correct new limitForApi.\n      fetchResults(currentPage, updatedResults);\n    }\n  };\n\n  // Consistent image URL construction\n  const getImageUrl = imageId => imageId ? `/api/data/images/file/${imageId}` : '';\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h5\",\n      gutterBottom: true,\n      children: [\"Results by Model (\", ipCategory.charAt(0).toUpperCase() + ipCategory.slice(1), \")\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n      fullWidth: true,\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n        id: \"model-select-label\",\n        children: \"Select Model\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(Select, {\n        labelId: \"model-select-label\",\n        id: \"model-select\",\n        value: selectedModelId,\n        label: \"Select Model\",\n        onChange: handleModelChange,\n        disabled: loading || models.length === 0,\n        children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n          value: \"\",\n          disabled: true,\n          children: /*#__PURE__*/_jsxDEV(\"em\", {\n            children: \"Select a model\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 21\n        }, this), models.map(model => /*#__PURE__*/_jsxDEV(MenuItem, {\n          value: model.id,\n          children: model.name\n        }, model.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 25\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 13\n    }, this), loading && /*#__PURE__*/_jsxDEV(CircularProgress, {\n      sx: {\n        display: 'block',\n        margin: 'auto',\n        my: 2\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 25\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 276,\n      columnNumber: 23\n    }, this), !loading && !error && !selectedModelId && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      children: \"Please select a model to view results.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 17\n    }, this), !loading && !error && selectedModelId && results.length === 0 && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      children: \"No results found for the selected model and category.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 18\n    }, this), results.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: results.map(productResult => {\n          var _productResult$produc, _productResult$produc2, _productResult$produc3, _productResult$produc5, _productResult$produc6, _productResult$produc7;\n          return (\n            /*#__PURE__*/\n            // Use product_image.id as key for the outer Grid item\n            _jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                variant: \"outlined\",\n                children: /*#__PURE__*/_jsxDEV(CardContent, {\n                  children: /*#__PURE__*/_jsxDEV(Grid, {\n                    container: true,\n                    spacing: 2,\n                    alignItems: \"flex-start\",\n                    children: [/*#__PURE__*/_jsxDEV(Grid, {\n                      item: true,\n                      xs: 12,\n                      sm: 2,\n                      md: 1.5,\n                      sx: {\n                        textAlign: 'center'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"subtitle2\",\n                        gutterBottom: true,\n                        children: \"Product\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 298,\n                        columnNumber: 49\n                      }, this), /*#__PURE__*/_jsxDEV(CardMedia, {\n                        component: \"img\",\n                        sx: {\n                          width: 100,\n                          height: 100,\n                          objectFit: 'contain',\n                          margin: 'auto',\n                          mb: 1,\n                          cursor: 'pointer',\n                          border: '1px solid lightgray',\n                          borderRadius: 1\n                        },\n                        image: getImageUrl((_productResult$produc2 = productResult.product_image) === null || _productResult$produc2 === void 0 ? void 0 : _productResult$produc2.id),\n                        alt: `Product ${((_productResult$produc3 = productResult.product_image) === null || _productResult$produc3 === void 0 ? void 0 : _productResult$produc3.filename) || 'N/A'}`,\n                        onClick: () => {\n                          var _productResult$produc4;\n                          return ((_productResult$produc4 = productResult.product_image) === null || _productResult$produc4 === void 0 ? void 0 : _productResult$produc4.id) && handleOpenImageModal(getImageUrl(productResult.product_image.id));\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 299,\n                        columnNumber: 49\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        display: \"block\",\n                        children: [\"ID: \", ((_productResult$produc5 = productResult.product_image) === null || _productResult$produc5 === void 0 ? void 0 : _productResult$produc5.id) || 'N/A']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 306,\n                        columnNumber: 49\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        display: \"block\",\n                        children: [\"File: \", ((_productResult$produc6 = productResult.product_image) === null || _productResult$produc6 === void 0 ? void 0 : _productResult$produc6.filename) || 'N/A']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 307,\n                        columnNumber: 49\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        display: \"block\",\n                        sx: {\n                          fontStyle: 'italic'\n                        },\n                        children: [\"Category: \", ((_productResult$produc7 = productResult.product_image) === null || _productResult$produc7 === void 0 ? void 0 : _productResult$produc7.ip_category) || 'N/A']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 309,\n                        columnNumber: 49\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 297,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                      item: true,\n                      xs: 12,\n                      sm: 10,\n                      md: 3.5,\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"subtitle2\",\n                        gutterBottom: true,\n                        children: \"Ground Truth IPs\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 316,\n                        columnNumber: 49\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          display: 'flex',\n                          flexWrap: 'wrap',\n                          gap: 1\n                        },\n                        children: productResult.ground_truth_ips && productResult.ground_truth_ips.length > 0 ? productResult.ground_truth_ips.map(gt_item =>\n                        /*#__PURE__*/\n                        // gt_item is an IPImageSchema object\n                        _jsxDEV(Tooltip, {\n                          title: `IP ID: ${gt_item.id} | File: ${gt_item.filename || 'N/A'} | Owner: ${gt_item.ip_owner || 'N/A'}`,\n                          children: /*#__PURE__*/_jsxDEV(Paper, {\n                            variant: \"outlined\",\n                            sx: {\n                              p: 0.5,\n                              textAlign: 'center',\n                              width: 'auto',\n                              minWidth: 90,\n                              display: 'flex',\n                              flexDirection: 'column',\n                              alignItems: 'center',\n                              gap: 0.5\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(CardMedia, {\n                              component: \"img\",\n                              sx: {\n                                width: 70,\n                                height: 70,\n                                objectFit: 'contain',\n                                cursor: 'pointer',\n                                border: '1px solid lightgray',\n                                borderRadius: 1\n                              },\n                              image: getImageUrl(gt_item.id),\n                              alt: `Ground Truth ${gt_item.filename || gt_item.id}`,\n                              onClick: () => handleOpenImageModal(getImageUrl(gt_item.id))\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 323,\n                              columnNumber: 69\n                            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                              variant: \"caption\",\n                              display: \"block\",\n                              sx: {\n                                maxWidth: 80,\n                                overflow: 'hidden',\n                                textOverflow: 'ellipsis',\n                                whiteSpace: 'nowrap'\n                              },\n                              children: gt_item.filename || gt_item.id.substring(0, 8)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 330,\n                              columnNumber: 69\n                            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                              title: \"Remove from Ground Truth\",\n                              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                                size: \"small\",\n                                onClick: () => {\n                                  if (productResult.product_image && productResult.product_image.id) {\n                                    handleRemoveDirectGroundTruth(productResult.product_image.id, gt_item.id);\n                                  } else {\n                                    console.error(\"Product image ID is not available for removing ground truth.\");\n                                    setError(\"Cannot remove ground truth: Product information missing.\");\n                                  }\n                                },\n                                \"aria-label\": \"Remove from Ground Truth\",\n                                children: /*#__PURE__*/_jsxDEV(ClearIcon, {\n                                  fontSize: \"small\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 346,\n                                  columnNumber: 77\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 334,\n                                columnNumber: 73\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 333,\n                              columnNumber: 69\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 322,\n                            columnNumber: 65\n                          }, this)\n                        }, gt_item.id, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 321,\n                          columnNumber: 61\n                        }, this)) : /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          children: \"None specified.\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 353,\n                          columnNumber: 57\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 317,\n                        columnNumber: 49\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 315,\n                      columnNumber: 45\n                    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n                      item: true,\n                      xs: 12,\n                      md: 7,\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"subtitle2\",\n                        gutterBottom: true,\n                        children: [\"Model Suggestions (Displaying up to \", productResult.displayedSuggestionsCount || INITIAL_SUGGESTIONS_COUNT, \")\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 361,\n                        columnNumber: 49\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          display: 'flex',\n                          flexWrap: 'wrap',\n                          gap: 2\n                        },\n                        children: productResult.model_suggestions && productResult.model_suggestions.length > 0 ? productResult.model_suggestions.slice(0, productResult.displayedSuggestionsCount || INITIAL_SUGGESTIONS_COUNT).map(suggestion => {\n                          var _productResult$produc8, _suggestion$similarit, _suggestion$similarit2;\n                          return (\n                            /*#__PURE__*/\n                            // suggestion has ip_image_id, similarity_score, is_ground_truth, ip_filename, ip_owner\n                            _jsxDEV(Paper, {\n                              variant: \"outlined\",\n                              sx: {\n                                p: 1,\n                                textAlign: 'center',\n                                border: suggestion.is_ground_truth ? '2px solid green' : '1px solid lightgray',\n                                width: 120\n                              },\n                              children: [/*#__PURE__*/_jsxDEV(CardMedia, {\n                                component: \"img\",\n                                sx: {\n                                  width: 80,\n                                  height: 80,\n                                  objectFit: 'contain',\n                                  margin: 'auto',\n                                  mb: 1,\n                                  cursor: 'pointer'\n                                },\n                                image: getImageUrl(suggestion.ip_image_id),\n                                alt: `Suggestion ${suggestion.ip_filename || suggestion.ip_image_id.substring(0, 8)}`,\n                                onClick: () => handleOpenImageModal(getImageUrl(suggestion.ip_image_id))\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 374,\n                                columnNumber: 69\n                              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                                title: suggestion.ip_filename || `IP ID: ${suggestion.ip_image_id}`,\n                                children: /*#__PURE__*/_jsxDEV(Typography, {\n                                  variant: \"caption\",\n                                  display: \"block\",\n                                  sx: {\n                                    overflow: 'hidden',\n                                    textOverflow: 'ellipsis',\n                                    whiteSpace: 'nowrap'\n                                  },\n                                  children: suggestion.ip_filename || `ID: ${suggestion.ip_image_id.substring(0, 8)}`\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 382,\n                                  columnNumber: 73\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 381,\n                                columnNumber: 69\n                              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                                variant: \"caption\",\n                                display: \"block\",\n                                children: [\"Score: \", (_suggestion$similarit = (_suggestion$similarit2 = suggestion.similarity_score) === null || _suggestion$similarit2 === void 0 ? void 0 : _suggestion$similarit2.toFixed(4)) !== null && _suggestion$similarit !== void 0 ? _suggestion$similarit : 'N/A']\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 386,\n                                columnNumber: 69\n                              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                                title: suggestion.ip_owner || 'No Owner',\n                                children: /*#__PURE__*/_jsxDEV(Typography, {\n                                  variant: \"caption\",\n                                  display: \"block\",\n                                  sx: {\n                                    overflow: 'hidden',\n                                    textOverflow: 'ellipsis',\n                                    whiteSpace: 'nowrap'\n                                  },\n                                  children: [\"Owner: \", suggestion.ip_owner || 'N/A']\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 388,\n                                  columnNumber: 73\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 387,\n                                columnNumber: 69\n                              }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                                title: suggestion.is_ground_truth ? \"Marked as Ground Truth\" : \"Mark as Ground Truth\",\n                                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                                    size: \"small\",\n                                    color: suggestion.is_ground_truth ? \"success\" : \"default\",\n                                    onClick: () => handleGroundTruthToggle(productResult.product_image.id, suggestion.ip_image_id, suggestion.is_ground_truth),\n                                    children: suggestion.is_ground_truth ? /*#__PURE__*/_jsxDEV(ThumbUpIcon, {\n                                      fontSize: \"small\"\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 399,\n                                      columnNumber: 111\n                                    }, this) : /*#__PURE__*/_jsxDEV(ThumbUpOutlinedIcon, {\n                                      fontSize: \"small\"\n                                    }, void 0, false, {\n                                      fileName: _jsxFileName,\n                                      lineNumber: 399,\n                                      columnNumber: 146\n                                    }, this)\n                                  }, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 394,\n                                    columnNumber: 77\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 393,\n                                  columnNumber: 73\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 392,\n                                columnNumber: 69\n                              }, this)]\n                            }, `${(_productResult$produc8 = productResult.product_image) === null || _productResult$produc8 === void 0 ? void 0 : _productResult$produc8.id}-${suggestion.ip_image_id}`, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 369,\n                              columnNumber: 65\n                            }, this)\n                          );\n                        }) : /*#__PURE__*/_jsxDEV(Typography, {\n                          variant: \"caption\",\n                          children: \"No suggestions from this model.\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 406,\n                          columnNumber: 57\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 364,\n                        columnNumber: 49\n                      }, this), (_productResult$produc9 => {\n                        const productSuggestions = productResult.model_suggestions || [];\n                        const displayCount = productResult.displayedSuggestionsCount || INITIAL_SUGGESTIONS_COUNT;\n                        const hasFetchedSuggestions = productSuggestions.length > 0;\n                        const canDisplayMoreOfFetched = hasFetchedSuggestions && displayCount < productSuggestions.length;\n                        const hitApiLimitAndDisplayedAllFetched = hasFetchedSuggestions && displayCount === productSuggestions.length && productSuggestions.length === lastApiLimit;\n                        const showMoreButton = canDisplayMoreOfFetched || hitApiLimitAndDisplayedAllFetched;\n                        if ((_productResult$produc9 = productResult.product_image) !== null && _productResult$produc9 !== void 0 && _productResult$produc9.id && showMoreButton) {\n                          return /*#__PURE__*/_jsxDEV(Button, {\n                            onClick: () => handleShowMoreSuggestions(productResult.product_image.id),\n                            variant: \"outlined\",\n                            size: \"small\",\n                            sx: {\n                              mt: 2,\n                              display: 'block',\n                              mx: 'auto'\n                            },\n                            children: \"Show 5 More Suggestions\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 421,\n                            columnNumber: 61\n                          }, this);\n                        }\n                        return null;\n                      })()]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 359,\n                      columnNumber: 45\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 33\n              }, this)\n            }, ((_productResult$produc = productResult.product_image) === null || _productResult$produc === void 0 ? void 0 : _productResult$produc.id) || Math.random(), false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 29\n            }, this)\n          );\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 21\n      }, this), paginationMeta.totalPages > 1 && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          mt: 3\n        },\n        children: /*#__PURE__*/_jsxDEV(Pagination, {\n          count: paginationMeta.totalPages // Step 2: Use paginationMeta\n          ,\n          page: currentPage // Step 1: Bind page to currentPage\n          ,\n          onChange: handlePageChange // Step 1: onChange calls setCurrentPage (via handlePageChange)\n          ,\n          color: \"primary\",\n          disabled: loading\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 444,\n          columnNumber: 29\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 443,\n        columnNumber: 25\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      open: imageModalOpen,\n      onClose: handleCloseImageModal,\n      \"aria-labelledby\": \"enlarge-image-modal-title\",\n      \"aria-describedby\": \"enlarge-image-modal-description\",\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'absolute',\n          top: '50%',\n          left: '50%',\n          transform: 'translate(-50%, -50%)',\n          bgcolor: 'background.paper',\n          boxShadow: 24,\n          p: 2,\n          // Padding around the image\n          outline: 'none',\n          maxWidth: '90vw',\n          // Max width relative to viewport width\n          maxHeight: '90vh',\n          // Max height relative to viewport height\n          display: 'flex',\n          // Use flexbox for centering\n          justifyContent: 'center',\n          // Center horizontally\n          alignItems: 'center' // Center vertically\n        },\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: selectedImage,\n          alt: \"Enlarged view\",\n          style: {\n            maxWidth: '100%',\n            maxHeight: '100%',\n            objectFit: 'contain'\n          } // Image scales within the box\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 463,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 457,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 249,\n    columnNumber: 9\n  }, this);\n};\n_s(ByModelView, \"cOe0MWU6JvQ4Fdhs8isafQaHdY4=\");\n_c = ByModelView;\nexport default ByModelView;\nvar _c;\n$RefreshReg$(_c, \"ByModelView\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "Box", "Typography", "Select", "MenuItem", "FormControl", "InputLabel", "Grid", "Card", "CardMedia", "<PERSON><PERSON><PERSON><PERSON>", "CircularProgress", "<PERSON><PERSON>", "Pagination", "IconButton", "<PERSON><PERSON><PERSON>", "Modal", "Paper", "<PERSON><PERSON>", "ThumbUpIcon", "ThumbUpOutlinedIcon", "ZoomInIcon", "ClearIcon", "listModels", "getCombinedScores", "getResultsByModel", "addGroundTruth", "removeGroundTruth", "jsxDEV", "_jsxDEV", "RESULTS_PER_PAGE", "ByModelView", "ipCategory", "_s", "models", "setModels", "selectedModelId", "setSelectedModelId", "results", "setResults", "currentPage", "setCurrentPage", "paginationMeta", "setPaginationMeta", "totalPages", "loading", "setLoading", "error", "setError", "imageModalOpen", "setImageModalOpen", "selectedImage", "setSelectedImage", "INITIAL_SUGGESTIONS_COUNT", "lastApiLimit", "setLastApiLimit", "handleOpenImageModal", "imageUrl", "handleCloseImageModal", "fetchModels", "modelsResponse", "combinedScoresResponse", "ip_category", "is_active", "applicableModels", "data", "filter", "m", "applicable_ip_category", "includes", "map", "id", "model_id", "name", "model_name", "type", "applicableCombined", "c", "config_id", "config_name", "allApplicable", "length", "err", "console", "fetchResults", "pageToFetch", "currentResultsSnapshot", "_response$data$pagina", "limitForApi", "Math", "max", "r", "displayedSuggestionsCount", "params", "page", "per_page", "limit", "response", "newApiResultsForPage", "processedResults", "apiProd", "existingProductState", "find", "_r$product_image", "_apiProd$product_imag", "product_image", "displayCount", "model_suggestions", "pagination", "total_pages", "_err$response", "_err$response$data", "_err$response2", "_err$response2$data", "detail", "message", "handleModelChange", "event", "target", "value", "handlePageChange", "handleGroundTruthToggle", "productImageId", "ipImageId", "currentIsGroundTruth", "_err$response3", "_err$response3$data", "_err$response4", "_err$response4$data", "handleRemoveDirectGroundTruth", "productId", "ipId", "_err$response5", "_err$response5$data", "_err$response6", "_err$response6$data", "handleShowMoreSuggestions", "updatedResults", "p", "_p$product_image", "overallMaxDisplayed", "getImageUrl", "imageId", "sx", "children", "variant", "gutterBottom", "char<PERSON>t", "toUpperCase", "slice", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fullWidth", "mb", "labelId", "label", "onChange", "disabled", "model", "display", "margin", "my", "severity", "container", "spacing", "productResult", "_productResult$produc", "_productResult$produc2", "_productResult$produc3", "_productResult$produc5", "_productResult$produc6", "_productResult$produc7", "item", "xs", "alignItems", "sm", "md", "textAlign", "component", "width", "height", "objectFit", "cursor", "border", "borderRadius", "image", "alt", "filename", "onClick", "_productResult$produc4", "fontStyle", "flexWrap", "gap", "ground_truth_ips", "gt_item", "title", "ip_owner", "min<PERSON><PERSON><PERSON>", "flexDirection", "max<PERSON><PERSON><PERSON>", "overflow", "textOverflow", "whiteSpace", "substring", "size", "fontSize", "suggestion", "_productResult$produc8", "_suggestion$similarit", "_suggestion$similarit2", "is_ground_truth", "ip_image_id", "ip_filename", "similarity_score", "toFixed", "color", "_productResult$produc9", "productSuggestions", "hasFetchedSuggestions", "canDisplayMoreOfFetched", "hitApiLimitAndDisplayedAllFetched", "showMoreButton", "mt", "mx", "random", "justifyContent", "count", "open", "onClose", "position", "top", "left", "transform", "bgcolor", "boxShadow", "outline", "maxHeight", "src", "style", "_c", "$RefreshReg$"], "sources": ["D:/Documents/Programing/TRO/ModelTestsWorkbench/frontend/src/components/model-test-workbench/ByModelView.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef } from 'react';\r\nimport {\r\n    Box,\r\n    Typography,\r\n    Select,\r\n    MenuItem,\r\n    FormControl,\r\n    InputLabel,\r\n    Grid,\r\n    Card,\r\n    CardMedia,\r\n    CardContent,\r\n    CircularProgress,\r\n    Alert,\r\n    Pagination,\r\n    IconButton,\r\n    Tooltip,\r\n    Modal,\r\n    Paper,\r\n    Button, // Added Button\r\n} from '@mui/material';\r\nimport ThumbUpIcon from '@mui/icons-material/ThumbUp';\r\nimport ThumbUpOutlinedIcon from '@mui/icons-material/ThumbUpOutlined';\r\nimport ZoomInIcon from '@mui/icons-material/ZoomIn';\r\nimport ClearIcon from '@mui/icons-material/Clear'; // Added ClearIcon\r\n// Use named imports for API functions\r\nimport { listModels, getCombinedScores, getResultsByModel, addGroundTruth, removeGroundTruth } from '../../services/api_model_workbench'; // Added getCombinedScores\r\n\r\nconst RESULTS_PER_PAGE = 10; // Or make this configurable\r\n\r\nconst ByModelView = ({ ipCategory }) => {\r\n    const [models, setModels] = useState([]);\r\n    const [selectedModelId, setSelectedModelId] = useState('');\r\n    const [results, setResults] = useState([]);\r\n    const [currentPage, setCurrentPage] = useState(1); // Step 1: Dedicated current page state\r\n    const [paginationMeta, setPaginationMeta] = useState({ totalPages: 1 }); // Step 2: Separate pagination meta\r\n    const [loading, setLoading] = useState(false);\r\n    const [error, setError] = useState(null);\r\n    const [imageModalOpen, setImageModalOpen] = useState(false);\r\n    const [selectedImage, setSelectedImage] = useState('');\r\n    const INITIAL_SUGGESTIONS_COUNT = 5; // Number of suggestions to show initially\r\n    // Removed suggestionsApiLimit, will manage per product and a max for API\r\n    const [lastApiLimit, setLastApiLimit] = useState(INITIAL_SUGGESTIONS_COUNT);\r\n\r\n    const handleOpenImageModal = (imageUrl) => {\r\n        setSelectedImage(imageUrl);\r\n        setImageModalOpen(true);\r\n    };\r\n\r\n    const handleCloseImageModal = () => {\r\n        setImageModalOpen(false);\r\n        setSelectedImage('');\r\n    };\r\n\r\n    // Fetch models and combined scores\r\n    useEffect(() => {\r\n        const fetchModels = async () => {\r\n            setLoading(true);\r\n            setError(null);\r\n            try {\r\n                // Fetch active models for the category\r\n                // TODO: The backend listModels doesn't filter by category yet, filter client-side for now\r\n                const modelsResponse = await listModels(); // Fetch all models\r\n                const combinedScoresResponse = await getCombinedScores({ ip_category: ipCategory, is_active: true }); // Fetch active combined scores for category\r\n\r\n                // Combine and filter models applicable to this category\r\n                const applicableModels = (modelsResponse.data || [])\r\n                    .filter(m => m.is_active && (m.applicable_ip_category.includes(ipCategory) || m.applicable_ip_category.includes('all')))\r\n                    .map(m => ({ id: m.model_id, name: m.model_name, type: 'model' }));\r\n\r\n                const applicableCombined = (combinedScoresResponse.data || [])\r\n                    .map(c => ({ id: c.config_id, name: c.config_name, type: 'combined' }));\r\n\r\n                const allApplicable = [...applicableModels, ...applicableCombined];\r\n\r\n                setModels(allApplicable);\r\n                if (allApplicable.length > 0) {\r\n                    // Optionally pre-select the first model\r\n                    // setSelectedModelId(allApplicable[0].id);\r\n                } else {\r\n                    setError(`No active models or combined scores found for ${ipCategory}.`);\r\n                }\r\n            } catch (err) {\r\n                console.error(\"Error fetching models:\", err);\r\n                setError(`Failed to fetch models for ${ipCategory}. Please try again later.`);\r\n                setModels([]);\r\n            } finally {\r\n                setLoading(false);\r\n            }\r\n        };\r\n\r\n        if (ipCategory) {\r\n            fetchModels();\r\n        }\r\n    }, [ipCategory]);\r\n\r\n    // Step 4: Refactor useEffect for Resetting Page on Filter Change\r\n    useEffect(() => {\r\n        // Reset to page 1 when selectedModelId or ipCategory changes\r\n        // This will trigger the data fetching effect for the new page 1\r\n        setCurrentPage(1);\r\n        setResults([]); // Clear results, so displayedSuggestionsCount are effectively reset for new model/category\r\n        setLastApiLimit(INITIAL_SUGGESTIONS_COUNT); // Reset last API limit as well\r\n    }, [selectedModelId, ipCategory]);\r\n\r\n    // Step 5: Review fetchResults (or equivalent data fetching function)\r\n    // Step 3: Refactor useEffect for Fetching Results\r\n    const fetchResults = useCallback(async (pageToFetch, currentResultsSnapshot) => {\r\n        if (!selectedModelId) {\r\n            setResults([]);\r\n            setPaginationMeta({ totalPages: 1 });\r\n            return;\r\n        }\r\n\r\n        setLoading(true);\r\n        setError(null);\r\n        try {\r\n            let limitForApi = INITIAL_SUGGESTIONS_COUNT;\r\n            if (currentResultsSnapshot && currentResultsSnapshot.length > 0) {\r\n                limitForApi = Math.max(\r\n                    INITIAL_SUGGESTIONS_COUNT,\r\n                    ...currentResultsSnapshot.map(r => r.displayedSuggestionsCount || INITIAL_SUGGESTIONS_COUNT)\r\n                );\r\n            }\r\n            setLastApiLimit(limitForApi);\r\n\r\n            const params = {\r\n                model_id: selectedModelId,\r\n                ip_category: ipCategory,\r\n                page: pageToFetch,\r\n                per_page: RESULTS_PER_PAGE,\r\n                limit: limitForApi,\r\n            };\r\n            const response = await getResultsByModel(params);\r\n            const newApiResultsForPage = response.data.results || [];\r\n\r\n            const processedResults = newApiResultsForPage.map(apiProd => {\r\n                const existingProductState = currentResultsSnapshot.find(\r\n                    r => r.product_image?.id === apiProd.product_image?.id\r\n                );\r\n                const displayCount = existingProductState\r\n                    ? existingProductState.displayedSuggestionsCount\r\n                    : INITIAL_SUGGESTIONS_COUNT;\r\n                return {\r\n                    ...apiProd,\r\n                    displayedSuggestionsCount: displayCount,\r\n                    model_suggestions: apiProd.model_suggestions || [], // Ensure it's an array\r\n                };\r\n            });\r\n\r\n            setResults(processedResults);\r\n            setPaginationMeta({\r\n                totalPages: response.data.pagination?.total_pages || 1,\r\n            });\r\n        } catch (err) {\r\n            console.error(\"Error fetching results:\", err);\r\n            setError(`Failed to fetch results for model ${selectedModelId}. ${err.response?.data?.detail || err.response?.data?.error || err.message}`);\r\n            setResults([]); // Clear results on error\r\n            setPaginationMeta({ totalPages: 1 });\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    }, [selectedModelId, ipCategory]); // Removed suggestionsApiLimit, fetchResults itself doesn't depend on 'results' state directly in definition\r\n\r\n    // Effect for fetching results based on currentPage, selectedModelId, or ipCategory\r\n    useEffect(() => {\r\n        if (selectedModelId && ipCategory) {\r\n            // Pass the current 'results' state to fetchResults for limit calculation\r\n            fetchResults(currentPage, results);\r\n        }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n    }, [selectedModelId, ipCategory, currentPage, fetchResults]); // REMOVED 'results' to prevent loop\r\n\r\n\r\n    const handleModelChange = (event) => {\r\n        setSelectedModelId(event.target.value);\r\n        // setCurrentPage(1) is handled by the useEffect hook dependent on selectedModelId\r\n    };\r\n\r\n    const handlePageChange = (event, value) => {\r\n        setCurrentPage(value); // Step 1: Pagination onChange calls setCurrentPage\r\n    };\r\n\r\n    const handleGroundTruthToggle = async (productImageId, ipImageId, currentIsGroundTruth) => {\r\n        setError(null);\r\n        try {\r\n            if (currentIsGroundTruth) {\r\n                await removeGroundTruth(productImageId, ipImageId);\r\n            } else {\r\n                await addGroundTruth(productImageId, ipImageId);\r\n            }\r\n            fetchResults(currentPage, results); // Refresh results for the current page\r\n        } catch (err) {\r\n            console.error(\"Error updating ground truth:\", err);\r\n            setError(`Failed to update ground truth. ${err.response?.data?.detail || err.response?.data?.error || err.message}`);\r\n        }\r\n    };\r\n\r\n    const handleRemoveDirectGroundTruth = async (productId, ipId) => {\r\n        if (!productId) {\r\n            setError(\"Cannot remove ground truth: Product ID is missing.\");\r\n            console.error(\"Product ID is missing for removeGroundTruth call.\");\r\n            return;\r\n        }\r\n        setError(null);\r\n        try {\r\n            await removeGroundTruth(productId, ipId);\r\n            fetchResults(currentPage, results); // Refresh results for the current page\r\n        } catch (err) {\r\n            console.error(\"Error removing ground truth directly:\", err);\r\n            setError(`Failed to remove ground truth. ${err.response?.data?.detail || err.response?.data?.error || err.message}`);\r\n        }\r\n    };\r\n\r\n    const handleShowMoreSuggestions = (productId) => {\r\n        // Create the next state for 'results' by updating the specific product's display count\r\n        const updatedResults = results.map(p => {\r\n            if (p.product_image?.id === productId) {\r\n                return {\r\n                    ...p,\r\n                    displayedSuggestionsCount: (p.displayedSuggestionsCount || INITIAL_SUGGESTIONS_COUNT) + INITIAL_SUGGESTIONS_COUNT,\r\n                };\r\n            }\r\n            return p;\r\n        });\r\n\r\n        setResults(updatedResults); // Apply the UI change immediately\r\n\r\n        // Now, determine if an API call is needed because the overall max displayed suggestions increased\r\n        const overallMaxDisplayed = Math.max(\r\n            INITIAL_SUGGESTIONS_COUNT,\r\n            ...updatedResults.map(r => r.displayedSuggestionsCount || INITIAL_SUGGESTIONS_COUNT)\r\n        );\r\n\r\n        // If the new max count required for display exceeds the limit used for the last API call,\r\n        // then we need to fetch results again with an updated limit.\r\n        if (overallMaxDisplayed > lastApiLimit) {\r\n            // Pass currentPage and the *newly updated* results state (updatedResults)\r\n            // so fetchResults can calculate the correct new limitForApi.\r\n            fetchResults(currentPage, updatedResults);\r\n        }\r\n    };\r\n \r\n    // Consistent image URL construction\r\n    const getImageUrl = (imageId) => imageId ? `/api/data/images/file/${imageId}` : '';\r\n\r\n\r\n    return (\r\n        <Box sx={{ p: 3 }}>\r\n            <Typography variant=\"h5\" gutterBottom>\r\n                Results by Model ({ipCategory.charAt(0).toUpperCase() + ipCategory.slice(1)})\r\n            </Typography>\r\n\r\n            <FormControl fullWidth sx={{ mb: 3 }}>\r\n                <InputLabel id=\"model-select-label\">Select Model</InputLabel>\r\n                <Select\r\n                    labelId=\"model-select-label\"\r\n                    id=\"model-select\"\r\n                    value={selectedModelId}\r\n                    label=\"Select Model\"\r\n                    onChange={handleModelChange}\r\n                    disabled={loading || models.length === 0}\r\n                >\r\n                    <MenuItem value=\"\" disabled>\r\n                        <em>Select a model</em>\r\n                    </MenuItem>\r\n                    {models.map((model) => (\r\n                        <MenuItem key={model.id} value={model.id}>\r\n                            {model.name}\r\n                        </MenuItem>\r\n                    ))}\r\n                </Select>\r\n            </FormControl>\r\n\r\n            {loading && <CircularProgress sx={{ display: 'block', margin: 'auto', my: 2 }} />}\r\n            {error && <Alert severity=\"error\" sx={{ mb: 2 }}>{error}</Alert>}\r\n\r\n            {!loading && !error && !selectedModelId && (\r\n                <Alert severity=\"info\">Please select a model to view results.</Alert>\r\n            )}\r\n\r\n            {!loading && !error && selectedModelId && results.length === 0 && (\r\n                 <Alert severity=\"info\">No results found for the selected model and category.</Alert>\r\n            )}\r\n\r\n            {results.length > 0 && (\r\n                <Box>\r\n                    <Grid container spacing={3}>\r\n                        {/* Each 'productResult' is an item from the 'results' array */}\r\n                        {results.map((productResult) => (\r\n                            // Use product_image.id as key for the outer Grid item\r\n                            <Grid item xs={12} key={productResult.product_image?.id || Math.random()}>\r\n                                <Card variant=\"outlined\">\r\n                                    <CardContent>\r\n                                        <Grid container spacing={2} alignItems=\"flex-start\">\r\n                                            {/* Product Image Section */}\r\n                                            <Grid item xs={12} sm={2} md={1.5} sx={{ textAlign: 'center' }}>\r\n                                                <Typography variant=\"subtitle2\" gutterBottom>Product</Typography>\r\n                                                <CardMedia\r\n                                                    component=\"img\"\r\n                                                    sx={{ width: 100, height: 100, objectFit: 'contain', margin: 'auto', mb: 1, cursor: 'pointer', border: '1px solid lightgray', borderRadius: 1 }}\r\n                                                    image={getImageUrl(productResult.product_image?.id)}\r\n                                                    alt={`Product ${productResult.product_image?.filename || 'N/A'}`}\r\n                                                    onClick={() => productResult.product_image?.id && handleOpenImageModal(getImageUrl(productResult.product_image.id))}\r\n                                                />\r\n                                                <Typography variant=\"caption\" display=\"block\">ID: {productResult.product_image?.id || 'N/A'}</Typography>\r\n                                                <Typography variant=\"caption\" display=\"block\">File: {productResult.product_image?.filename || 'N/A'}</Typography>\r\n                                                {/* Gracefully handle missing product_image.ip_category */}\r\n                                                <Typography variant=\"caption\" display=\"block\" sx={{ fontStyle: 'italic' }}>\r\n                                                    Category: {productResult.product_image?.ip_category || 'N/A'}\r\n                                                </Typography>\r\n                                            </Grid>\r\n\r\n                                            {/* Ground Truth IPs Section */}\r\n                                            <Grid item xs={12} sm={10} md={3.5}>\r\n                                                <Typography variant=\"subtitle2\" gutterBottom>Ground Truth IPs</Typography>\r\n                                                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>\r\n                                                    {/* Use 'ground_truth_ips' list name and 'item.id' for IP image identifiers */}\r\n                                                    {productResult.ground_truth_ips && productResult.ground_truth_ips.length > 0 ? (\r\n                                                        productResult.ground_truth_ips.map(gt_item => ( // gt_item is an IPImageSchema object\r\n                                                            <Tooltip key={gt_item.id} title={`IP ID: ${gt_item.id} | File: ${gt_item.filename || 'N/A'} | Owner: ${gt_item.ip_owner || 'N/A'}`}>\r\n                                                                <Paper variant=\"outlined\" sx={{ p: 0.5, textAlign: 'center', width: 'auto', minWidth: 90, display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 0.5 }}>\r\n                                                                    <CardMedia\r\n                                                                        component=\"img\"\r\n                                                                        sx={{ width: 70, height: 70, objectFit: 'contain', cursor: 'pointer', border: '1px solid lightgray', borderRadius: 1 }}\r\n                                                                        image={getImageUrl(gt_item.id)}\r\n                                                                        alt={`Ground Truth ${gt_item.filename || gt_item.id}`}\r\n                                                                        onClick={() => handleOpenImageModal(getImageUrl(gt_item.id))}\r\n                                                                    />\r\n                                                                    <Typography variant=\"caption\" display=\"block\" sx={{ maxWidth: 80, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>\r\n                                                                        {gt_item.filename || gt_item.id.substring(0,8)}\r\n                                                                    </Typography>\r\n                                                                    <Tooltip title=\"Remove from Ground Truth\">\r\n                                                                        <IconButton\r\n                                                                            size=\"small\"\r\n                                                                            onClick={() => {\r\n                                                                                if (productResult.product_image && productResult.product_image.id) {\r\n                                                                                    handleRemoveDirectGroundTruth(productResult.product_image.id, gt_item.id);\r\n                                                                                } else {\r\n                                                                                    console.error(\"Product image ID is not available for removing ground truth.\");\r\n                                                                                    setError(\"Cannot remove ground truth: Product information missing.\");\r\n                                                                                }\r\n                                                                            }}\r\n                                                                            aria-label=\"Remove from Ground Truth\"\r\n                                                                        >\r\n                                                                            <ClearIcon fontSize=\"small\" />\r\n                                                                        </IconButton>\r\n                                                                    </Tooltip>\r\n                                                                </Paper>\r\n                                                            </Tooltip>\r\n                                                        ))\r\n                                                    ) : (\r\n                                                        <Typography variant=\"caption\">None specified.</Typography>\r\n                                                    )}\r\n                                                </Box>\r\n                                            </Grid>\r\n\r\n                                            {/* Model Suggestions Section */}\r\n                                            <Grid item xs={12} md={7}>\r\n                                                {/* Use 'model_suggestions' list name */}\r\n                                                <Typography variant=\"subtitle2\" gutterBottom>\r\n                                                    Model Suggestions (Displaying up to {productResult.displayedSuggestionsCount || INITIAL_SUGGESTIONS_COUNT})\r\n                                                </Typography>\r\n                                                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>\r\n                                                    {(productResult.model_suggestions && productResult.model_suggestions.length > 0) ? (\r\n                                                        productResult.model_suggestions\r\n                                                            .slice(0, productResult.displayedSuggestionsCount || INITIAL_SUGGESTIONS_COUNT)\r\n                                                            .map((suggestion) => ( // suggestion has ip_image_id, similarity_score, is_ground_truth, ip_filename, ip_owner\r\n                                                                <Paper\r\n                                                                    key={`${productResult.product_image?.id}-${suggestion.ip_image_id}`}\r\n                                                                    variant=\"outlined\"\r\n                                                                    sx={{ p: 1, textAlign: 'center', border: suggestion.is_ground_truth ? '2px solid green' : '1px solid lightgray', width: 120 }}\r\n                                                                >\r\n                                                                    <CardMedia\r\n                                                                        component=\"img\"\r\n                                                                        sx={{ width: 80, height: 80, objectFit: 'contain', margin: 'auto', mb: 1, cursor: 'pointer' }}\r\n                                                                        image={getImageUrl(suggestion.ip_image_id)}\r\n                                                                        alt={`Suggestion ${suggestion.ip_filename || suggestion.ip_image_id.substring(0,8)}`}\r\n                                                                        onClick={() => handleOpenImageModal(getImageUrl(suggestion.ip_image_id))}\r\n                                                                    />\r\n                                                                    <Tooltip title={suggestion.ip_filename || `IP ID: ${suggestion.ip_image_id}`}>\r\n                                                                        <Typography variant=\"caption\" display=\"block\" sx={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>\r\n                                                                            {suggestion.ip_filename || `ID: ${suggestion.ip_image_id.substring(0,8)}`}\r\n                                                                        </Typography>\r\n                                                                    </Tooltip>\r\n                                                                    <Typography variant=\"caption\" display=\"block\">Score: {suggestion.similarity_score?.toFixed(4) ?? 'N/A'}</Typography>\r\n                                                                    <Tooltip title={suggestion.ip_owner || 'No Owner'}>\r\n                                                                        <Typography variant=\"caption\" display=\"block\" sx={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>\r\n                                                                            Owner: {suggestion.ip_owner || 'N/A'}\r\n                                                                        </Typography>\r\n                                                                    </Tooltip>\r\n                                                                    <Tooltip title={suggestion.is_ground_truth ? \"Marked as Ground Truth\" : \"Mark as Ground Truth\"}>\r\n                                                                        <span>\r\n                                                                            <IconButton\r\n                                                                                size=\"small\"\r\n                                                                                color={suggestion.is_ground_truth ? \"success\" : \"default\"}\r\n                                                                                onClick={() => handleGroundTruthToggle(productResult.product_image.id, suggestion.ip_image_id, suggestion.is_ground_truth)}\r\n                                                                            >\r\n                                                                                {suggestion.is_ground_truth ? <ThumbUpIcon fontSize=\"small\" /> : <ThumbUpOutlinedIcon fontSize=\"small\" />}\r\n                                                                            </IconButton>\r\n                                                                        </span>\r\n                                                                    </Tooltip>\r\n                                                                </Paper>\r\n                                                            ))\r\n                                                    ) : (\r\n                                                        <Typography variant=\"caption\">No suggestions from this model.</Typography>\r\n                                                    )}\r\n                                                </Box>\r\n                                                {(() => {\r\n                                                    const productSuggestions = productResult.model_suggestions || [];\r\n                                                    const displayCount = productResult.displayedSuggestionsCount || INITIAL_SUGGESTIONS_COUNT;\r\n                                                    const hasFetchedSuggestions = productSuggestions.length > 0;\r\n                                                    \r\n                                                    const canDisplayMoreOfFetched = hasFetchedSuggestions && displayCount < productSuggestions.length;\r\n                                                    const hitApiLimitAndDisplayedAllFetched = hasFetchedSuggestions && displayCount === productSuggestions.length && productSuggestions.length === lastApiLimit;\r\n                                                    \r\n                                                    const showMoreButton = canDisplayMoreOfFetched || hitApiLimitAndDisplayedAllFetched;\r\n\r\n                                                    if (productResult.product_image?.id && showMoreButton) {\r\n                                                        return (\r\n                                                            <Button\r\n                                                                onClick={() => handleShowMoreSuggestions(productResult.product_image.id)}\r\n                                                                variant=\"outlined\"\r\n                                                                size=\"small\"\r\n                                                                sx={{ mt: 2, display: 'block', mx: 'auto' }}\r\n                                                            >\r\n                                                                Show 5 More Suggestions\r\n                                                            </Button>\r\n                                                        );\r\n                                                    }\r\n                                                    return null;\r\n                                                })()}\r\n                                            </Grid>\r\n                                        </Grid>\r\n                                    </CardContent>\r\n                                </Card>\r\n                            </Grid>\r\n                        ))}\r\n                    </Grid>\r\n\r\n                    {/* Pagination - uses paginationMeta.totalPages and currentPage */}\r\n                    {paginationMeta.totalPages > 1 && (\r\n                        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>\r\n                            <Pagination\r\n                                count={paginationMeta.totalPages} // Step 2: Use paginationMeta\r\n                                page={currentPage} // Step 1: Bind page to currentPage\r\n                                onChange={handlePageChange} // Step 1: onChange calls setCurrentPage (via handlePageChange)\r\n                                color=\"primary\"\r\n                                disabled={loading}\r\n                            />\r\n                        </Box>\r\n                    )}\r\n                </Box>\r\n            )}\r\n\r\n            {/* Image Enlarge Modal */}\r\n            <Modal\r\n                open={imageModalOpen}\r\n                onClose={handleCloseImageModal}\r\n                aria-labelledby=\"enlarge-image-modal-title\"\r\n                aria-describedby=\"enlarge-image-modal-description\"\r\n            >\r\n                <Box sx={{\r\n                    position: 'absolute',\r\n                    top: '50%',\r\n                    left: '50%',\r\n                    transform: 'translate(-50%, -50%)',\r\n                    bgcolor: 'background.paper',\r\n                    boxShadow: 24,\r\n                    p: 2, // Padding around the image\r\n                    outline: 'none',\r\n                    maxWidth: '90vw', // Max width relative to viewport width\r\n                    maxHeight: '90vh', // Max height relative to viewport height\r\n                    display: 'flex', // Use flexbox for centering\r\n                    justifyContent: 'center', // Center horizontally\r\n                    alignItems: 'center', // Center vertically\r\n                }}>\r\n                    <img\r\n                        src={selectedImage}\r\n                        alt=\"Enlarged view\"\r\n                        style={{ maxWidth: '100%', maxHeight: '100%', objectFit: 'contain' }} // Image scales within the box\r\n                    />\r\n                </Box>\r\n            </Modal>\r\n        </Box>\r\n    );\r\n};\r\n\r\nexport default ByModelView;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,QAAQ,OAAO;AACvE,SACIC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,UAAU,EACVC,IAAI,EACJC,IAAI,EACJC,SAAS,EACTC,WAAW,EACXC,gBAAgB,EAChBC,KAAK,EACLC,UAAU,EACVC,UAAU,EACVC,OAAO,EACPC,KAAK,EACLC,KAAK,EACLC,MAAM,CAAE;AAAA,OACL,eAAe;AACtB,OAAOC,WAAW,MAAM,6BAA6B;AACrD,OAAOC,mBAAmB,MAAM,qCAAqC;AACrE,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,SAAS,MAAM,2BAA2B,CAAC,CAAC;AACnD;AACA,SAASC,UAAU,EAAEC,iBAAiB,EAAEC,iBAAiB,EAAEC,cAAc,EAAEC,iBAAiB,QAAQ,oCAAoC,CAAC,CAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAE1I,MAAMC,gBAAgB,GAAG,EAAE,CAAC,CAAC;;AAE7B,MAAMC,WAAW,GAAGA,CAAC;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EACpC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACuC,eAAe,EAAEC,kBAAkB,CAAC,GAAGxC,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACyC,OAAO,EAAEC,UAAU,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC2C,WAAW,EAAEC,cAAc,CAAC,GAAG5C,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACnD,MAAM,CAAC6C,cAAc,EAAEC,iBAAiB,CAAC,GAAG9C,QAAQ,CAAC;IAAE+C,UAAU,EAAE;EAAE,CAAC,CAAC,CAAC,CAAC;EACzE,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkD,KAAK,EAAEC,QAAQ,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACoD,cAAc,EAAEC,iBAAiB,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACsD,aAAa,EAAEC,gBAAgB,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAMwD,yBAAyB,GAAG,CAAC,CAAC,CAAC;EACrC;EACA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG1D,QAAQ,CAACwD,yBAAyB,CAAC;EAE3E,MAAMG,oBAAoB,GAAIC,QAAQ,IAAK;IACvCL,gBAAgB,CAACK,QAAQ,CAAC;IAC1BP,iBAAiB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMQ,qBAAqB,GAAGA,CAAA,KAAM;IAChCR,iBAAiB,CAAC,KAAK,CAAC;IACxBE,gBAAgB,CAAC,EAAE,CAAC;EACxB,CAAC;;EAED;EACAtD,SAAS,CAAC,MAAM;IACZ,MAAM6D,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC5Bb,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MACd,IAAI;QACA;QACA;QACA,MAAMY,cAAc,GAAG,MAAMrC,UAAU,CAAC,CAAC,CAAC,CAAC;QAC3C,MAAMsC,sBAAsB,GAAG,MAAMrC,iBAAiB,CAAC;UAAEsC,WAAW,EAAE9B,UAAU;UAAE+B,SAAS,EAAE;QAAK,CAAC,CAAC,CAAC,CAAC;;QAEtG;QACA,MAAMC,gBAAgB,GAAG,CAACJ,cAAc,CAACK,IAAI,IAAI,EAAE,EAC9CC,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACJ,SAAS,KAAKI,CAAC,CAACC,sBAAsB,CAACC,QAAQ,CAACrC,UAAU,CAAC,IAAImC,CAAC,CAACC,sBAAsB,CAACC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CACvHC,GAAG,CAACH,CAAC,KAAK;UAAEI,EAAE,EAAEJ,CAAC,CAACK,QAAQ;UAAEC,IAAI,EAAEN,CAAC,CAACO,UAAU;UAAEC,IAAI,EAAE;QAAQ,CAAC,CAAC,CAAC;QAEtE,MAAMC,kBAAkB,GAAG,CAACf,sBAAsB,CAACI,IAAI,IAAI,EAAE,EACxDK,GAAG,CAACO,CAAC,KAAK;UAAEN,EAAE,EAAEM,CAAC,CAACC,SAAS;UAAEL,IAAI,EAAEI,CAAC,CAACE,WAAW;UAAEJ,IAAI,EAAE;QAAW,CAAC,CAAC,CAAC;QAE3E,MAAMK,aAAa,GAAG,CAAC,GAAGhB,gBAAgB,EAAE,GAAGY,kBAAkB,CAAC;QAElEzC,SAAS,CAAC6C,aAAa,CAAC;QACxB,IAAIA,aAAa,CAACC,MAAM,GAAG,CAAC,EAAE;UAC1B;UACA;QAAA,CACH,MAAM;UACHjC,QAAQ,CAAC,iDAAiDhB,UAAU,GAAG,CAAC;QAC5E;MACJ,CAAC,CAAC,OAAOkD,GAAG,EAAE;QACVC,OAAO,CAACpC,KAAK,CAAC,wBAAwB,EAAEmC,GAAG,CAAC;QAC5ClC,QAAQ,CAAC,8BAA8BhB,UAAU,2BAA2B,CAAC;QAC7EG,SAAS,CAAC,EAAE,CAAC;MACjB,CAAC,SAAS;QACNW,UAAU,CAAC,KAAK,CAAC;MACrB;IACJ,CAAC;IAED,IAAId,UAAU,EAAE;MACZ2B,WAAW,CAAC,CAAC;IACjB;EACJ,CAAC,EAAE,CAAC3B,UAAU,CAAC,CAAC;;EAEhB;EACAlC,SAAS,CAAC,MAAM;IACZ;IACA;IACA2C,cAAc,CAAC,CAAC,CAAC;IACjBF,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;IAChBgB,eAAe,CAACF,yBAAyB,CAAC,CAAC,CAAC;EAChD,CAAC,EAAE,CAACjB,eAAe,EAAEJ,UAAU,CAAC,CAAC;;EAEjC;EACA;EACA,MAAMoD,YAAY,GAAGrF,WAAW,CAAC,OAAOsF,WAAW,EAAEC,sBAAsB,KAAK;IAC5E,IAAI,CAAClD,eAAe,EAAE;MAClBG,UAAU,CAAC,EAAE,CAAC;MACdI,iBAAiB,CAAC;QAAEC,UAAU,EAAE;MAAE,CAAC,CAAC;MACpC;IACJ;IAEAE,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IACd,IAAI;MAAA,IAAAuC,qBAAA;MACA,IAAIC,WAAW,GAAGnC,yBAAyB;MAC3C,IAAIiC,sBAAsB,IAAIA,sBAAsB,CAACL,MAAM,GAAG,CAAC,EAAE;QAC7DO,WAAW,GAAGC,IAAI,CAACC,GAAG,CAClBrC,yBAAyB,EACzB,GAAGiC,sBAAsB,CAAChB,GAAG,CAACqB,CAAC,IAAIA,CAAC,CAACC,yBAAyB,IAAIvC,yBAAyB,CAC/F,CAAC;MACL;MACAE,eAAe,CAACiC,WAAW,CAAC;MAE5B,MAAMK,MAAM,GAAG;QACXrB,QAAQ,EAAEpC,eAAe;QACzB0B,WAAW,EAAE9B,UAAU;QACvB8D,IAAI,EAAET,WAAW;QACjBU,QAAQ,EAAEjE,gBAAgB;QAC1BkE,KAAK,EAAER;MACX,CAAC;MACD,MAAMS,QAAQ,GAAG,MAAMxE,iBAAiB,CAACoE,MAAM,CAAC;MAChD,MAAMK,oBAAoB,GAAGD,QAAQ,CAAChC,IAAI,CAAC3B,OAAO,IAAI,EAAE;MAExD,MAAM6D,gBAAgB,GAAGD,oBAAoB,CAAC5B,GAAG,CAAC8B,OAAO,IAAI;QACzD,MAAMC,oBAAoB,GAAGf,sBAAsB,CAACgB,IAAI,CACpDX,CAAC;UAAA,IAAAY,gBAAA,EAAAC,qBAAA;UAAA,OAAI,EAAAD,gBAAA,GAAAZ,CAAC,CAACc,aAAa,cAAAF,gBAAA,uBAAfA,gBAAA,CAAiBhC,EAAE,QAAAiC,qBAAA,GAAKJ,OAAO,CAACK,aAAa,cAAAD,qBAAA,uBAArBA,qBAAA,CAAuBjC,EAAE;QAAA,CAC1D,CAAC;QACD,MAAMmC,YAAY,GAAGL,oBAAoB,GACnCA,oBAAoB,CAACT,yBAAyB,GAC9CvC,yBAAyB;QAC/B,OAAO;UACH,GAAG+C,OAAO;UACVR,yBAAyB,EAAEc,YAAY;UACvCC,iBAAiB,EAAEP,OAAO,CAACO,iBAAiB,IAAI,EAAE,CAAE;QACxD,CAAC;MACL,CAAC,CAAC;MAEFpE,UAAU,CAAC4D,gBAAgB,CAAC;MAC5BxD,iBAAiB,CAAC;QACdC,UAAU,EAAE,EAAA2C,qBAAA,GAAAU,QAAQ,CAAChC,IAAI,CAAC2C,UAAU,cAAArB,qBAAA,uBAAxBA,qBAAA,CAA0BsB,WAAW,KAAI;MACzD,CAAC,CAAC;IACN,CAAC,CAAC,OAAO3B,GAAG,EAAE;MAAA,IAAA4B,aAAA,EAAAC,kBAAA,EAAAC,cAAA,EAAAC,mBAAA;MACV9B,OAAO,CAACpC,KAAK,CAAC,yBAAyB,EAAEmC,GAAG,CAAC;MAC7ClC,QAAQ,CAAC,qCAAqCZ,eAAe,KAAK,EAAA0E,aAAA,GAAA5B,GAAG,CAACe,QAAQ,cAAAa,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAc7C,IAAI,cAAA8C,kBAAA,uBAAlBA,kBAAA,CAAoBG,MAAM,OAAAF,cAAA,GAAI9B,GAAG,CAACe,QAAQ,cAAAe,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc/C,IAAI,cAAAgD,mBAAA,uBAAlBA,mBAAA,CAAoBlE,KAAK,KAAImC,GAAG,CAACiC,OAAO,EAAE,CAAC;MAC3I5E,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;MAChBI,iBAAiB,CAAC;QAAEC,UAAU,EAAE;MAAE,CAAC,CAAC;IACxC,CAAC,SAAS;MACNE,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC,EAAE,CAACV,eAAe,EAAEJ,UAAU,CAAC,CAAC,CAAC,CAAC;;EAEnC;EACAlC,SAAS,CAAC,MAAM;IACZ,IAAIsC,eAAe,IAAIJ,UAAU,EAAE;MAC/B;MACAoD,YAAY,CAAC5C,WAAW,EAAEF,OAAO,CAAC;IACtC;IACJ;EACA,CAAC,EAAE,CAACF,eAAe,EAAEJ,UAAU,EAAEQ,WAAW,EAAE4C,YAAY,CAAC,CAAC,CAAC,CAAC;;EAG9D,MAAMgC,iBAAiB,GAAIC,KAAK,IAAK;IACjChF,kBAAkB,CAACgF,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;IACtC;EACJ,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAACH,KAAK,EAAEE,KAAK,KAAK;IACvC9E,cAAc,CAAC8E,KAAK,CAAC,CAAC,CAAC;EAC3B,CAAC;EAED,MAAME,uBAAuB,GAAG,MAAAA,CAAOC,cAAc,EAAEC,SAAS,EAAEC,oBAAoB,KAAK;IACvF5E,QAAQ,CAAC,IAAI,CAAC;IACd,IAAI;MACA,IAAI4E,oBAAoB,EAAE;QACtB,MAAMjG,iBAAiB,CAAC+F,cAAc,EAAEC,SAAS,CAAC;MACtD,CAAC,MAAM;QACH,MAAMjG,cAAc,CAACgG,cAAc,EAAEC,SAAS,CAAC;MACnD;MACAvC,YAAY,CAAC5C,WAAW,EAAEF,OAAO,CAAC,CAAC,CAAC;IACxC,CAAC,CAAC,OAAO4C,GAAG,EAAE;MAAA,IAAA2C,cAAA,EAAAC,mBAAA,EAAAC,cAAA,EAAAC,mBAAA;MACV7C,OAAO,CAACpC,KAAK,CAAC,8BAA8B,EAAEmC,GAAG,CAAC;MAClDlC,QAAQ,CAAC,kCAAkC,EAAA6E,cAAA,GAAA3C,GAAG,CAACe,QAAQ,cAAA4B,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc5D,IAAI,cAAA6D,mBAAA,uBAAlBA,mBAAA,CAAoBZ,MAAM,OAAAa,cAAA,GAAI7C,GAAG,CAACe,QAAQ,cAAA8B,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc9D,IAAI,cAAA+D,mBAAA,uBAAlBA,mBAAA,CAAoBjF,KAAK,KAAImC,GAAG,CAACiC,OAAO,EAAE,CAAC;IACxH;EACJ,CAAC;EAED,MAAMc,6BAA6B,GAAG,MAAAA,CAAOC,SAAS,EAAEC,IAAI,KAAK;IAC7D,IAAI,CAACD,SAAS,EAAE;MACZlF,QAAQ,CAAC,oDAAoD,CAAC;MAC9DmC,OAAO,CAACpC,KAAK,CAAC,mDAAmD,CAAC;MAClE;IACJ;IACAC,QAAQ,CAAC,IAAI,CAAC;IACd,IAAI;MACA,MAAMrB,iBAAiB,CAACuG,SAAS,EAAEC,IAAI,CAAC;MACxC/C,YAAY,CAAC5C,WAAW,EAAEF,OAAO,CAAC,CAAC,CAAC;IACxC,CAAC,CAAC,OAAO4C,GAAG,EAAE;MAAA,IAAAkD,cAAA,EAAAC,mBAAA,EAAAC,cAAA,EAAAC,mBAAA;MACVpD,OAAO,CAACpC,KAAK,CAAC,uCAAuC,EAAEmC,GAAG,CAAC;MAC3DlC,QAAQ,CAAC,kCAAkC,EAAAoF,cAAA,GAAAlD,GAAG,CAACe,QAAQ,cAAAmC,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcnE,IAAI,cAAAoE,mBAAA,uBAAlBA,mBAAA,CAAoBnB,MAAM,OAAAoB,cAAA,GAAIpD,GAAG,CAACe,QAAQ,cAAAqC,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcrE,IAAI,cAAAsE,mBAAA,uBAAlBA,mBAAA,CAAoBxF,KAAK,KAAImC,GAAG,CAACiC,OAAO,EAAE,CAAC;IACxH;EACJ,CAAC;EAED,MAAMqB,yBAAyB,GAAIN,SAAS,IAAK;IAC7C;IACA,MAAMO,cAAc,GAAGnG,OAAO,CAACgC,GAAG,CAACoE,CAAC,IAAI;MAAA,IAAAC,gBAAA;MACpC,IAAI,EAAAA,gBAAA,GAAAD,CAAC,CAACjC,aAAa,cAAAkC,gBAAA,uBAAfA,gBAAA,CAAiBpE,EAAE,MAAK2D,SAAS,EAAE;QACnC,OAAO;UACH,GAAGQ,CAAC;UACJ9C,yBAAyB,EAAE,CAAC8C,CAAC,CAAC9C,yBAAyB,IAAIvC,yBAAyB,IAAIA;QAC5F,CAAC;MACL;MACA,OAAOqF,CAAC;IACZ,CAAC,CAAC;IAEFnG,UAAU,CAACkG,cAAc,CAAC,CAAC,CAAC;;IAE5B;IACA,MAAMG,mBAAmB,GAAGnD,IAAI,CAACC,GAAG,CAChCrC,yBAAyB,EACzB,GAAGoF,cAAc,CAACnE,GAAG,CAACqB,CAAC,IAAIA,CAAC,CAACC,yBAAyB,IAAIvC,yBAAyB,CACvF,CAAC;;IAED;IACA;IACA,IAAIuF,mBAAmB,GAAGtF,YAAY,EAAE;MACpC;MACA;MACA8B,YAAY,CAAC5C,WAAW,EAAEiG,cAAc,CAAC;IAC7C;EACJ,CAAC;;EAED;EACA,MAAMI,WAAW,GAAIC,OAAO,IAAKA,OAAO,GAAG,yBAAyBA,OAAO,EAAE,GAAG,EAAE;EAGlF,oBACIjH,OAAA,CAAC5B,GAAG;IAAC8I,EAAE,EAAE;MAAEL,CAAC,EAAE;IAAE,CAAE;IAAAM,QAAA,gBACdnH,OAAA,CAAC3B,UAAU;MAAC+I,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,GAAC,oBAChB,EAAChH,UAAU,CAACmH,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGpH,UAAU,CAACqH,KAAK,CAAC,CAAC,CAAC,EAAC,GAChF;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEb5H,OAAA,CAACxB,WAAW;MAACqJ,SAAS;MAACX,EAAE,EAAE;QAAEY,EAAE,EAAE;MAAE,CAAE;MAAAX,QAAA,gBACjCnH,OAAA,CAACvB,UAAU;QAACiE,EAAE,EAAC,oBAAoB;QAAAyE,QAAA,EAAC;MAAY;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC7D5H,OAAA,CAAC1B,MAAM;QACHyJ,OAAO,EAAC,oBAAoB;QAC5BrF,EAAE,EAAC,cAAc;QACjBgD,KAAK,EAAEnF,eAAgB;QACvByH,KAAK,EAAC,cAAc;QACpBC,QAAQ,EAAE1C,iBAAkB;QAC5B2C,QAAQ,EAAElH,OAAO,IAAIX,MAAM,CAAC+C,MAAM,KAAK,CAAE;QAAA+D,QAAA,gBAEzCnH,OAAA,CAACzB,QAAQ;UAACmH,KAAK,EAAC,EAAE;UAACwC,QAAQ;UAAAf,QAAA,eACvBnH,OAAA;YAAAmH,QAAA,EAAI;UAAc;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,EACVvH,MAAM,CAACoC,GAAG,CAAE0F,KAAK,iBACdnI,OAAA,CAACzB,QAAQ;UAAgBmH,KAAK,EAAEyC,KAAK,CAACzF,EAAG;UAAAyE,QAAA,EACpCgB,KAAK,CAACvF;QAAI,GADAuF,KAAK,CAACzF,EAAE;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEb,CACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAEb5G,OAAO,iBAAIhB,OAAA,CAAClB,gBAAgB;MAACoI,EAAE,EAAE;QAAEkB,OAAO,EAAE,OAAO;QAAEC,MAAM,EAAE,MAAM;QAAEC,EAAE,EAAE;MAAE;IAAE;MAAAb,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAChF1G,KAAK,iBAAIlB,OAAA,CAACjB,KAAK;MAACwJ,QAAQ,EAAC,OAAO;MAACrB,EAAE,EAAE;QAAEY,EAAE,EAAE;MAAE,CAAE;MAAAX,QAAA,EAAEjG;IAAK;MAAAuG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,EAE/D,CAAC5G,OAAO,IAAI,CAACE,KAAK,IAAI,CAACX,eAAe,iBACnCP,OAAA,CAACjB,KAAK;MAACwJ,QAAQ,EAAC,MAAM;MAAApB,QAAA,EAAC;IAAsC;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACvE,EAEA,CAAC5G,OAAO,IAAI,CAACE,KAAK,IAAIX,eAAe,IAAIE,OAAO,CAAC2C,MAAM,KAAK,CAAC,iBACzDpD,OAAA,CAACjB,KAAK;MAACwJ,QAAQ,EAAC,MAAM;MAAApB,QAAA,EAAC;IAAqD;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACvF,EAEAnH,OAAO,CAAC2C,MAAM,GAAG,CAAC,iBACfpD,OAAA,CAAC5B,GAAG;MAAA+I,QAAA,gBACAnH,OAAA,CAACtB,IAAI;QAAC8J,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAtB,QAAA,EAEtB1G,OAAO,CAACgC,GAAG,CAAEiG,aAAa;UAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;UAAA;YAAA;YACvB;YACAhJ,OAAA,CAACtB,IAAI;cAACuK,IAAI;cAACC,EAAE,EAAE,EAAG;cAAA/B,QAAA,eACdnH,OAAA,CAACrB,IAAI;gBAACyI,OAAO,EAAC,UAAU;gBAAAD,QAAA,eACpBnH,OAAA,CAACnB,WAAW;kBAAAsI,QAAA,eACRnH,OAAA,CAACtB,IAAI;oBAAC8J,SAAS;oBAACC,OAAO,EAAE,CAAE;oBAACU,UAAU,EAAC,YAAY;oBAAAhC,QAAA,gBAE/CnH,OAAA,CAACtB,IAAI;sBAACuK,IAAI;sBAACC,EAAE,EAAE,EAAG;sBAACE,EAAE,EAAE,CAAE;sBAACC,EAAE,EAAE,GAAI;sBAACnC,EAAE,EAAE;wBAAEoC,SAAS,EAAE;sBAAS,CAAE;sBAAAnC,QAAA,gBAC3DnH,OAAA,CAAC3B,UAAU;wBAAC+I,OAAO,EAAC,WAAW;wBAACC,YAAY;wBAAAF,QAAA,EAAC;sBAAO;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACjE5H,OAAA,CAACpB,SAAS;wBACN2K,SAAS,EAAC,KAAK;wBACfrC,EAAE,EAAE;0BAAEsC,KAAK,EAAE,GAAG;0BAAEC,MAAM,EAAE,GAAG;0BAAEC,SAAS,EAAE,SAAS;0BAAErB,MAAM,EAAE,MAAM;0BAAEP,EAAE,EAAE,CAAC;0BAAE6B,MAAM,EAAE,SAAS;0BAAEC,MAAM,EAAE,qBAAqB;0BAAEC,YAAY,EAAE;wBAAE,CAAE;wBAChJC,KAAK,EAAE9C,WAAW,EAAA4B,sBAAA,GAACF,aAAa,CAAC9D,aAAa,cAAAgE,sBAAA,uBAA3BA,sBAAA,CAA6BlG,EAAE,CAAE;wBACpDqH,GAAG,EAAE,WAAW,EAAAlB,sBAAA,GAAAH,aAAa,CAAC9D,aAAa,cAAAiE,sBAAA,uBAA3BA,sBAAA,CAA6BmB,QAAQ,KAAI,KAAK,EAAG;wBACjEC,OAAO,EAAEA,CAAA;0BAAA,IAAAC,sBAAA;0BAAA,OAAM,EAAAA,sBAAA,GAAAxB,aAAa,CAAC9D,aAAa,cAAAsF,sBAAA,uBAA3BA,sBAAA,CAA6BxH,EAAE,KAAIf,oBAAoB,CAACqF,WAAW,CAAC0B,aAAa,CAAC9D,aAAa,CAAClC,EAAE,CAAC,CAAC;wBAAA;sBAAC;wBAAA+E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvH,CAAC,eACF5H,OAAA,CAAC3B,UAAU;wBAAC+I,OAAO,EAAC,SAAS;wBAACgB,OAAO,EAAC,OAAO;wBAAAjB,QAAA,GAAC,MAAI,EAAC,EAAA2B,sBAAA,GAAAJ,aAAa,CAAC9D,aAAa,cAAAkE,sBAAA,uBAA3BA,sBAAA,CAA6BpG,EAAE,KAAI,KAAK;sBAAA;wBAAA+E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAa,CAAC,eACzG5H,OAAA,CAAC3B,UAAU;wBAAC+I,OAAO,EAAC,SAAS;wBAACgB,OAAO,EAAC,OAAO;wBAAAjB,QAAA,GAAC,QAAM,EAAC,EAAA4B,sBAAA,GAAAL,aAAa,CAAC9D,aAAa,cAAAmE,sBAAA,uBAA3BA,sBAAA,CAA6BiB,QAAQ,KAAI,KAAK;sBAAA;wBAAAvC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAa,CAAC,eAEjH5H,OAAA,CAAC3B,UAAU;wBAAC+I,OAAO,EAAC,SAAS;wBAACgB,OAAO,EAAC,OAAO;wBAAClB,EAAE,EAAE;0BAAEiD,SAAS,EAAE;wBAAS,CAAE;wBAAAhD,QAAA,GAAC,YAC7D,EAAC,EAAA6B,sBAAA,GAAAN,aAAa,CAAC9D,aAAa,cAAAoE,sBAAA,uBAA3BA,sBAAA,CAA6B/G,WAAW,KAAI,KAAK;sBAAA;wBAAAwF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC,eAGP5H,OAAA,CAACtB,IAAI;sBAACuK,IAAI;sBAACC,EAAE,EAAE,EAAG;sBAACE,EAAE,EAAE,EAAG;sBAACC,EAAE,EAAE,GAAI;sBAAAlC,QAAA,gBAC/BnH,OAAA,CAAC3B,UAAU;wBAAC+I,OAAO,EAAC,WAAW;wBAACC,YAAY;wBAAAF,QAAA,EAAC;sBAAgB;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eAC1E5H,OAAA,CAAC5B,GAAG;wBAAC8I,EAAE,EAAE;0BAAEkB,OAAO,EAAE,MAAM;0BAAEgC,QAAQ,EAAE,MAAM;0BAAEC,GAAG,EAAE;wBAAE,CAAE;wBAAAlD,QAAA,EAElDuB,aAAa,CAAC4B,gBAAgB,IAAI5B,aAAa,CAAC4B,gBAAgB,CAAClH,MAAM,GAAG,CAAC,GACxEsF,aAAa,CAAC4B,gBAAgB,CAAC7H,GAAG,CAAC8H,OAAO;wBAAA;wBAAM;wBAC5CvK,OAAA,CAACd,OAAO;0BAAkBsL,KAAK,EAAE,UAAUD,OAAO,CAAC7H,EAAE,YAAY6H,OAAO,CAACP,QAAQ,IAAI,KAAK,aAAaO,OAAO,CAACE,QAAQ,IAAI,KAAK,EAAG;0BAAAtD,QAAA,eAC/HnH,OAAA,CAACZ,KAAK;4BAACgI,OAAO,EAAC,UAAU;4BAACF,EAAE,EAAE;8BAAEL,CAAC,EAAE,GAAG;8BAAEyC,SAAS,EAAE,QAAQ;8BAAEE,KAAK,EAAE,MAAM;8BAAEkB,QAAQ,EAAE,EAAE;8BAAEtC,OAAO,EAAE,MAAM;8BAAEuC,aAAa,EAAE,QAAQ;8BAAExB,UAAU,EAAE,QAAQ;8BAAEkB,GAAG,EAAE;4BAAI,CAAE;4BAAAlD,QAAA,gBACjKnH,OAAA,CAACpB,SAAS;8BACN2K,SAAS,EAAC,KAAK;8BACfrC,EAAE,EAAE;gCAAEsC,KAAK,EAAE,EAAE;gCAAEC,MAAM,EAAE,EAAE;gCAAEC,SAAS,EAAE,SAAS;gCAAEC,MAAM,EAAE,SAAS;gCAAEC,MAAM,EAAE,qBAAqB;gCAAEC,YAAY,EAAE;8BAAE,CAAE;8BACvHC,KAAK,EAAE9C,WAAW,CAACuD,OAAO,CAAC7H,EAAE,CAAE;8BAC/BqH,GAAG,EAAE,gBAAgBQ,OAAO,CAACP,QAAQ,IAAIO,OAAO,CAAC7H,EAAE,EAAG;8BACtDuH,OAAO,EAAEA,CAAA,KAAMtI,oBAAoB,CAACqF,WAAW,CAACuD,OAAO,CAAC7H,EAAE,CAAC;4BAAE;8BAAA+E,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAChE,CAAC,eACF5H,OAAA,CAAC3B,UAAU;8BAAC+I,OAAO,EAAC,SAAS;8BAACgB,OAAO,EAAC,OAAO;8BAAClB,EAAE,EAAE;gCAAE0D,QAAQ,EAAE,EAAE;gCAAEC,QAAQ,EAAE,QAAQ;gCAAEC,YAAY,EAAE,UAAU;gCAAEC,UAAU,EAAE;8BAAS,CAAE;8BAAA5D,QAAA,EAClIoD,OAAO,CAACP,QAAQ,IAAIO,OAAO,CAAC7H,EAAE,CAACsI,SAAS,CAAC,CAAC,EAAC,CAAC;4BAAC;8BAAAvD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACtC,CAAC,eACb5H,OAAA,CAACd,OAAO;8BAACsL,KAAK,EAAC,0BAA0B;8BAAArD,QAAA,eACrCnH,OAAA,CAACf,UAAU;gCACPgM,IAAI,EAAC,OAAO;gCACZhB,OAAO,EAAEA,CAAA,KAAM;kCACX,IAAIvB,aAAa,CAAC9D,aAAa,IAAI8D,aAAa,CAAC9D,aAAa,CAAClC,EAAE,EAAE;oCAC/D0D,6BAA6B,CAACsC,aAAa,CAAC9D,aAAa,CAAClC,EAAE,EAAE6H,OAAO,CAAC7H,EAAE,CAAC;kCAC7E,CAAC,MAAM;oCACHY,OAAO,CAACpC,KAAK,CAAC,8DAA8D,CAAC;oCAC7EC,QAAQ,CAAC,0DAA0D,CAAC;kCACxE;gCACJ,CAAE;gCACF,cAAW,0BAA0B;gCAAAgG,QAAA,eAErCnH,OAAA,CAACP,SAAS;kCAACyL,QAAQ,EAAC;gCAAO;kCAAAzD,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACtB;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACR,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACP;wBAAC,GA5BE2C,OAAO,CAAC7H,EAAE;0BAAA+E,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OA6Bf,CACZ,CAAC,gBAEF5H,OAAA,CAAC3B,UAAU;0BAAC+I,OAAO,EAAC,SAAS;0BAAAD,QAAA,EAAC;wBAAe;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY;sBAC5D;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eAGP5H,OAAA,CAACtB,IAAI;sBAACuK,IAAI;sBAACC,EAAE,EAAE,EAAG;sBAACG,EAAE,EAAE,CAAE;sBAAAlC,QAAA,gBAErBnH,OAAA,CAAC3B,UAAU;wBAAC+I,OAAO,EAAC,WAAW;wBAACC,YAAY;wBAAAF,QAAA,GAAC,sCACL,EAACuB,aAAa,CAAC3E,yBAAyB,IAAIvC,yBAAyB,EAAC,GAC9G;sBAAA;wBAAAiG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC,eACb5H,OAAA,CAAC5B,GAAG;wBAAC8I,EAAE,EAAE;0BAAEkB,OAAO,EAAE,MAAM;0BAAEgC,QAAQ,EAAE,MAAM;0BAAEC,GAAG,EAAE;wBAAE,CAAE;wBAAAlD,QAAA,EACjDuB,aAAa,CAAC5D,iBAAiB,IAAI4D,aAAa,CAAC5D,iBAAiB,CAAC1B,MAAM,GAAG,CAAC,GAC3EsF,aAAa,CAAC5D,iBAAiB,CAC1B0C,KAAK,CAAC,CAAC,EAAEkB,aAAa,CAAC3E,yBAAyB,IAAIvC,yBAAyB,CAAC,CAC9EiB,GAAG,CAAE0I,UAAU;0BAAA,IAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA;0BAAA;4BAAA;4BAAO;4BACnBtL,OAAA,CAACZ,KAAK;8BAEFgI,OAAO,EAAC,UAAU;8BAClBF,EAAE,EAAE;gCAAEL,CAAC,EAAE,CAAC;gCAAEyC,SAAS,EAAE,QAAQ;gCAAEM,MAAM,EAAEuB,UAAU,CAACI,eAAe,GAAG,iBAAiB,GAAG,qBAAqB;gCAAE/B,KAAK,EAAE;8BAAI,CAAE;8BAAArC,QAAA,gBAE9HnH,OAAA,CAACpB,SAAS;gCACN2K,SAAS,EAAC,KAAK;gCACfrC,EAAE,EAAE;kCAAEsC,KAAK,EAAE,EAAE;kCAAEC,MAAM,EAAE,EAAE;kCAAEC,SAAS,EAAE,SAAS;kCAAErB,MAAM,EAAE,MAAM;kCAAEP,EAAE,EAAE,CAAC;kCAAE6B,MAAM,EAAE;gCAAU,CAAE;gCAC9FG,KAAK,EAAE9C,WAAW,CAACmE,UAAU,CAACK,WAAW,CAAE;gCAC3CzB,GAAG,EAAE,cAAcoB,UAAU,CAACM,WAAW,IAAIN,UAAU,CAACK,WAAW,CAACR,SAAS,CAAC,CAAC,EAAC,CAAC,CAAC,EAAG;gCACrFf,OAAO,EAAEA,CAAA,KAAMtI,oBAAoB,CAACqF,WAAW,CAACmE,UAAU,CAACK,WAAW,CAAC;8BAAE;gCAAA/D,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC5E,CAAC,eACF5H,OAAA,CAACd,OAAO;gCAACsL,KAAK,EAAEW,UAAU,CAACM,WAAW,IAAI,UAAUN,UAAU,CAACK,WAAW,EAAG;gCAAArE,QAAA,eACzEnH,OAAA,CAAC3B,UAAU;kCAAC+I,OAAO,EAAC,SAAS;kCAACgB,OAAO,EAAC,OAAO;kCAAClB,EAAE,EAAE;oCAAE2D,QAAQ,EAAE,QAAQ;oCAAEC,YAAY,EAAE,UAAU;oCAAEC,UAAU,EAAE;kCAAS,CAAE;kCAAA5D,QAAA,EACpHgE,UAAU,CAACM,WAAW,IAAI,OAAON,UAAU,CAACK,WAAW,CAACR,SAAS,CAAC,CAAC,EAAC,CAAC,CAAC;gCAAE;kCAAAvD,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACjE;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACR,CAAC,eACV5H,OAAA,CAAC3B,UAAU;gCAAC+I,OAAO,EAAC,SAAS;gCAACgB,OAAO,EAAC,OAAO;gCAAAjB,QAAA,GAAC,SAAO,GAAAkE,qBAAA,IAAAC,sBAAA,GAACH,UAAU,CAACO,gBAAgB,cAAAJ,sBAAA,uBAA3BA,sBAAA,CAA6BK,OAAO,CAAC,CAAC,CAAC,cAAAN,qBAAA,cAAAA,qBAAA,GAAI,KAAK;8BAAA;gCAAA5D,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAa,CAAC,eACpH5H,OAAA,CAACd,OAAO;gCAACsL,KAAK,EAAEW,UAAU,CAACV,QAAQ,IAAI,UAAW;gCAAAtD,QAAA,eAC9CnH,OAAA,CAAC3B,UAAU;kCAAC+I,OAAO,EAAC,SAAS;kCAACgB,OAAO,EAAC,OAAO;kCAAClB,EAAE,EAAE;oCAAE2D,QAAQ,EAAE,QAAQ;oCAAEC,YAAY,EAAE,UAAU;oCAAEC,UAAU,EAAE;kCAAS,CAAE;kCAAA5D,QAAA,GAAC,SAC/G,EAACgE,UAAU,CAACV,QAAQ,IAAI,KAAK;gCAAA;kCAAAhD,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAC5B;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACR,CAAC,eACV5H,OAAA,CAACd,OAAO;gCAACsL,KAAK,EAAEW,UAAU,CAACI,eAAe,GAAG,wBAAwB,GAAG,sBAAuB;gCAAApE,QAAA,eAC3FnH,OAAA;kCAAAmH,QAAA,eACInH,OAAA,CAACf,UAAU;oCACPgM,IAAI,EAAC,OAAO;oCACZW,KAAK,EAAET,UAAU,CAACI,eAAe,GAAG,SAAS,GAAG,SAAU;oCAC1DtB,OAAO,EAAEA,CAAA,KAAMrE,uBAAuB,CAAC8C,aAAa,CAAC9D,aAAa,CAAClC,EAAE,EAAEyI,UAAU,CAACK,WAAW,EAAEL,UAAU,CAACI,eAAe,CAAE;oCAAApE,QAAA,EAE1HgE,UAAU,CAACI,eAAe,gBAAGvL,OAAA,CAACV,WAAW;sCAAC4L,QAAQ,EAAC;oCAAO;sCAAAzD,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAE,CAAC,gBAAG5H,OAAA,CAACT,mBAAmB;sCAAC2L,QAAQ,EAAC;oCAAO;sCAAAzD,QAAA,EAAAC,YAAA;sCAAAC,UAAA;sCAAAC,YAAA;oCAAA,OAAE;kCAAC;oCAAAH,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OACjG;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACX;8BAAC;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACF,CAAC;4BAAA,GAhCL,IAAAwD,sBAAA,GAAG1C,aAAa,CAAC9D,aAAa,cAAAwG,sBAAA,uBAA3BA,sBAAA,CAA6B1I,EAAE,IAAIyI,UAAU,CAACK,WAAW,EAAE;8BAAA/D,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAiChE;0BAAC;wBAAA,CACX,CAAC,gBAEN5H,OAAA,CAAC3B,UAAU;0BAAC+I,OAAO,EAAC,SAAS;0BAAAD,QAAA,EAAC;wBAA+B;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY;sBAC5E;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,EACL,CAACiE,sBAAA,IAAM;wBACJ,MAAMC,kBAAkB,GAAGpD,aAAa,CAAC5D,iBAAiB,IAAI,EAAE;wBAChE,MAAMD,YAAY,GAAG6D,aAAa,CAAC3E,yBAAyB,IAAIvC,yBAAyB;wBACzF,MAAMuK,qBAAqB,GAAGD,kBAAkB,CAAC1I,MAAM,GAAG,CAAC;wBAE3D,MAAM4I,uBAAuB,GAAGD,qBAAqB,IAAIlH,YAAY,GAAGiH,kBAAkB,CAAC1I,MAAM;wBACjG,MAAM6I,iCAAiC,GAAGF,qBAAqB,IAAIlH,YAAY,KAAKiH,kBAAkB,CAAC1I,MAAM,IAAI0I,kBAAkB,CAAC1I,MAAM,KAAK3B,YAAY;wBAE3J,MAAMyK,cAAc,GAAGF,uBAAuB,IAAIC,iCAAiC;wBAEnF,IAAI,CAAAJ,sBAAA,GAAAnD,aAAa,CAAC9D,aAAa,cAAAiH,sBAAA,eAA3BA,sBAAA,CAA6BnJ,EAAE,IAAIwJ,cAAc,EAAE;0BACnD,oBACIlM,OAAA,CAACX,MAAM;4BACH4K,OAAO,EAAEA,CAAA,KAAMtD,yBAAyB,CAAC+B,aAAa,CAAC9D,aAAa,CAAClC,EAAE,CAAE;4BACzE0E,OAAO,EAAC,UAAU;4BAClB6D,IAAI,EAAC,OAAO;4BACZ/D,EAAE,EAAE;8BAAEiF,EAAE,EAAE,CAAC;8BAAE/D,OAAO,EAAE,OAAO;8BAAEgE,EAAE,EAAE;4BAAO,CAAE;4BAAAjF,QAAA,EAC/C;0BAED;4BAAAM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC;wBAEjB;wBACA,OAAO,IAAI;sBACf,CAAC,EAAE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ;YAAC,GAhJa,EAAAe,qBAAA,GAAAD,aAAa,CAAC9D,aAAa,cAAA+D,qBAAA,uBAA3BA,qBAAA,CAA6BjG,EAAE,KAAIkB,IAAI,CAACyI,MAAM,CAAC,CAAC;cAAA5E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiJlE;UAAC;QAAA,CACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,EAGN/G,cAAc,CAACE,UAAU,GAAG,CAAC,iBAC1Bf,OAAA,CAAC5B,GAAG;QAAC8I,EAAE,EAAE;UAAEkB,OAAO,EAAE,MAAM;UAAEkE,cAAc,EAAE,QAAQ;UAAEH,EAAE,EAAE;QAAE,CAAE;QAAAhF,QAAA,eAC1DnH,OAAA,CAAChB,UAAU;UACPuN,KAAK,EAAE1L,cAAc,CAACE,UAAW,CAAC;UAAA;UAClCkD,IAAI,EAAEtD,WAAY,CAAC;UAAA;UACnBsH,QAAQ,EAAEtC,gBAAiB,CAAC;UAAA;UAC5BiG,KAAK,EAAC,SAAS;UACf1D,QAAQ,EAAElH;QAAQ;UAAAyG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CACR,eAGD5H,OAAA,CAACb,KAAK;MACFqN,IAAI,EAAEpL,cAAe;MACrBqL,OAAO,EAAE5K,qBAAsB;MAC/B,mBAAgB,2BAA2B;MAC3C,oBAAiB,iCAAiC;MAAAsF,QAAA,eAElDnH,OAAA,CAAC5B,GAAG;QAAC8I,EAAE,EAAE;UACLwF,QAAQ,EAAE,UAAU;UACpBC,GAAG,EAAE,KAAK;UACVC,IAAI,EAAE,KAAK;UACXC,SAAS,EAAE,uBAAuB;UAClCC,OAAO,EAAE,kBAAkB;UAC3BC,SAAS,EAAE,EAAE;UACblG,CAAC,EAAE,CAAC;UAAE;UACNmG,OAAO,EAAE,MAAM;UACfpC,QAAQ,EAAE,MAAM;UAAE;UAClBqC,SAAS,EAAE,MAAM;UAAE;UACnB7E,OAAO,EAAE,MAAM;UAAE;UACjBkE,cAAc,EAAE,QAAQ;UAAE;UAC1BnD,UAAU,EAAE,QAAQ,CAAE;QAC1B,CAAE;QAAAhC,QAAA,eACEnH,OAAA;UACIkN,GAAG,EAAE5L,aAAc;UACnByI,GAAG,EAAC,eAAe;UACnBoD,KAAK,EAAE;YAAEvC,QAAQ,EAAE,MAAM;YAAEqC,SAAS,EAAE,MAAM;YAAEvD,SAAS,EAAE;UAAU,CAAE,CAAC;QAAA;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEd,CAAC;AAACxH,EAAA,CAxcIF,WAAW;AAAAkN,EAAA,GAAXlN,WAAW;AA0cjB,eAAeA,WAAW;AAAC,IAAAkN,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}