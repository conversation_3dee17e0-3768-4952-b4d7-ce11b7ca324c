import uuid
from datetime import datetime
from sqlalchemy import (
    Column, String, Text, Boolean, TIMESTAMP, Float, ForeignKey, CheckConstraint,
    UniqueConstraint, Index, func, inspect
)
from sqlalchemy.dialects.postgresql import UUID as PG_UUID, JSONB, ARRAY, BIGINT, BYTEA
from sqlalchemy.orm import relationship
from backend.extensions import db # Import db instance from extensions module

# Import new bounding box models so they are registered with db.Model.metadata
from .models_bounding_box import BoundingBoxModels, BoundingBoxPictures, BoundingBoxExperiments, BoundingBoxResults

class ModelTestsImage(db.Model):
    """Stores image metadata."""
    __tablename__ = 'modeltests_images'

    image_id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    original_filename = Column(Text, nullable=False)
    relative_path = Column(Text, nullable=False, unique=True) # e.g., 'trademark/product/image.jpg'
    image_type = Column(String(10), nullable=False) # 'product' or 'ip'
    ip_category = Column(String(50)) # 'trademark', 'copyright', 'patent', NULL if product
    ip_owner = Column(String(255), nullable=True)
    created_at = Column(TIMESTAMP(timezone=True), server_default=func.now())
    updated_at = Column(TIMESTAMP(timezone=True), server_default=func.now(), onupdate=func.now())
    file_last_modified = Column(TIMESTAMP(timezone=True))

    # Relationships with cascade delete for FR2.1.4
    comparison_results_as_product = relationship(
        "ModelTestsComparisonResult",
        foreign_keys="[ModelTestsComparisonResult.product_image_id]",
        back_populates="product_image",
        cascade="all, delete-orphan"
    )
    comparison_results_as_ip = relationship(
        "ModelTestsComparisonResult",
        foreign_keys="[ModelTestsComparisonResult.ip_image_id]",
        back_populates="ip_image",
        cascade="all, delete-orphan"
    )
    ground_truth_as_product = relationship(
        "ModelTestsGroundTruth",
        foreign_keys="[ModelTestsGroundTruth.product_image_id]",
        back_populates="product_image",
        cascade="all, delete-orphan"
    )
    ground_truth_as_ip = relationship(
        "ModelTestsGroundTruth",
        foreign_keys="[ModelTestsGroundTruth.correct_ip_image_id]",
        back_populates="correct_ip_image",
        cascade="all, delete-orphan"
    )
    feature_storage = relationship(
        "ModelTestsFeatureStorage",
        back_populates="image",
        cascade="all, delete-orphan"
    )
    feature_status = relationship(
        "ModelTestsFeatureStatus",
        back_populates="image",
        cascade="all, delete-orphan"
    )

    __table_args__ = (
        CheckConstraint(image_type.in_(['product', 'ip']), name='check_image_type'),
        CheckConstraint(ip_category.in_(['trademark', 'copyright', 'patent']), name='check_ip_category'),
        # Constraint to ensure ip_category is set if image_type is 'ip'
        CheckConstraint(
            "(image_type != 'ip') OR (ip_category IS NOT NULL)", # Allow ip_category for product, enforce for ip
            name='check_ip_category_consistency'
        ),
        Index('idx_image_type_category', image_type, ip_category),
    )

    def __repr__(self):
        return f"<ModelTestsImage(image_id='{self.image_id}', filename='{self.original_filename}', type='{self.image_type}', category='{self.ip_category}')>"


class ModelTestsModel(db.Model):
    """Stores metadata about registered models."""
    __tablename__ = 'modeltests_models'

    model_id = Column(PG_UUID(as_uuid=True), primary_key=True) # From config.json
    model_name = Column(String(255), unique=True, nullable=False)
    model_type = Column(String(20), nullable=False) # 'embedding', 'descriptor', 'hash'
    applicable_ip_category = Column(ARRAY(Text), nullable=False) # ['trademark', 'copyright', 'patent'] or ['all']
    description = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(TIMESTAMP(timezone=True), server_default=func.now())
    updated_at = Column(TIMESTAMP(timezone=True), server_default=func.now(), onupdate=func.now())

    # Relationships with cascade delete
    feature_storage = relationship("ModelTestsFeatureStorage", back_populates="model", cascade="all, delete-orphan")
    feature_status = relationship("ModelTestsFeatureStatus", back_populates="model", cascade="all, delete-orphan")
    # Note: No direct FK relationship to comparison_results as model_id there can also be a config_id

    __table_args__ = (
        CheckConstraint(model_type.in_(['embedding', 'descriptor', 'hash']), name='check_model_type'),
    )

    def __repr__(self):
        return f"<ModelTestsModel(model_id='{self.model_id}', name='{self.model_name}', type='{self.model_type}')>"


class ModelTestsComparisonResult(db.Model):
    """Stores computed similarity scores (Top N + GT)."""
    __tablename__ = 'modeltests_comparison_results'

    result_id = Column(BIGINT, primary_key=True)
    product_image_id = Column(PG_UUID(as_uuid=True), ForeignKey('modeltests_images.image_id', ondelete='CASCADE'), nullable=False)
    ip_image_id = Column(PG_UUID(as_uuid=True), ForeignKey('modeltests_images.image_id', ondelete='CASCADE'), nullable=False)
    model_id = Column(PG_UUID(as_uuid=True), nullable=False) # FK to models OR combined_scores_config
    similarity_score = Column(Float, nullable=False)
    computed_at = Column(TIMESTAMP(timezone=True), server_default=func.now())

    # Relationships
    product_image = relationship("ModelTestsImage", foreign_keys=[product_image_id], back_populates="comparison_results_as_product")
    ip_image = relationship("ModelTestsImage", foreign_keys=[ip_image_id], back_populates="comparison_results_as_ip")
    # Cannot directly link model_id via FK due to dual nature (model vs combined score config)

    __table_args__ = (
        CheckConstraint('similarity_score >= 0 AND similarity_score <= 1', name='check_similarity_score_range'),
        UniqueConstraint('product_image_id', 'ip_image_id', 'model_id', name='uq_comparison_result'),
        Index('idx_comparison_results_product_model_score', product_image_id, model_id, similarity_score.desc()),
        Index('idx_comparison_results_ip', ip_image_id),
        Index('idx_comparison_results_model', model_id),
    )

    def __repr__(self):
        return f"<ModelTestsComparisonResult(product='{self.product_image_id}', ip='{self.ip_image_id}', model='{self.model_id}', score={self.similarity_score:.4f})>"


class ModelTestsGroundTruth(db.Model):
    """Stores user-defined ground truth pairings."""
    __tablename__ = 'modeltests_ground_truth'

    ground_truth_id = Column(BIGINT, primary_key=True)
    product_image_id = Column(PG_UUID(as_uuid=True), ForeignKey('modeltests_images.image_id', ondelete='CASCADE'), nullable=False)
    correct_ip_image_id = Column(PG_UUID(as_uuid=True), ForeignKey('modeltests_images.image_id', ondelete='CASCADE'), nullable=False)
    created_at = Column(TIMESTAMP(timezone=True), server_default=func.now())

    # Relationships
    product_image = relationship("ModelTestsImage", foreign_keys=[product_image_id], back_populates="ground_truth_as_product")
    correct_ip_image = relationship("ModelTestsImage", foreign_keys=[correct_ip_image_id], back_populates="ground_truth_as_ip")

    __table_args__ = (
        UniqueConstraint('product_image_id', 'correct_ip_image_id', name='uq_ground_truth'),
        Index('idx_ground_truth_product', product_image_id),
    )

    def __repr__(self):
        return f"<ModelTestsGroundTruth(product='{self.product_image_id}', correct_ip='{self.correct_ip_image_id}')>"


class ModelTestsCombinedScoresConfig(db.Model):
    """Stores combined score configurations."""
    __tablename__ = 'modeltests_combined_scores_config'

    config_id = Column(PG_UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    config_name = Column(String(255), unique=True, nullable=False)
    ip_category = Column(String(50), nullable=False) # 'trademark', 'copyright', 'patent'
    model_weights = Column(JSONB, nullable=False) # { "model_id_UUID_1": weight1, ... }
    is_active = Column(Boolean, default=True)
    created_at = Column(TIMESTAMP(timezone=True), server_default=func.now())
    updated_at = Column(TIMESTAMP(timezone=True), server_default=func.now(), onupdate=func.now())

    __table_args__ = (
        CheckConstraint(ip_category.in_(['trademark', 'copyright', 'patent']), name='check_config_ip_category'),
    )

    def __repr__(self):
        return f"<ModelTestsCombinedScoresConfig(config_id='{self.config_id}', name='{self.config_name}', category='{self.ip_category}')>"


class ModelTestsFeatureStorage(db.Model):
    """Stores computed features for DESCRIPTOR and HASH models."""
    __tablename__ = 'modeltests_feature_storage'

    feature_id = Column(BIGINT, primary_key=True)
    image_id = Column(PG_UUID(as_uuid=True), ForeignKey('modeltests_images.image_id', ondelete='CASCADE'), nullable=False)
    model_id = Column(PG_UUID(as_uuid=True), ForeignKey('modeltests_models.model_id', ondelete='CASCADE'), nullable=False)
    features = Column(BYTEA, nullable=False) # Descriptors (binary blob) or hash (binary/hex string)
    computed_at = Column(TIMESTAMP(timezone=True), server_default=func.now())

    # Relationships
    image = relationship("ModelTestsImage", back_populates="feature_storage")
    model = relationship("ModelTestsModel", back_populates="feature_storage")

    __table_args__ = (
        UniqueConstraint('image_id', 'model_id', name='uq_feature_storage'),
        Index('idx_feature_storage_lookup', model_id, image_id),
    )

    def __repr__(self):
        return f"<ModelTestsFeatureStorage(image='{self.image_id}', model='{self.model_id}')>"


class ModelTestsFeatureStatus(db.Model):
    """Tracks the status of feature computation per image/model pair."""
    __tablename__ = 'modeltests_feature_status'

    status_id = Column(BIGINT, primary_key=True)
    image_id = Column(PG_UUID(as_uuid=True), ForeignKey('modeltests_images.image_id', ondelete='CASCADE'), nullable=False)
    model_id = Column(PG_UUID(as_uuid=True), ForeignKey('modeltests_models.model_id', ondelete='CASCADE'), nullable=False)
    last_computed_at = Column(TIMESTAMP(timezone=True), nullable=True) # Timestamp of last successful computation

    status = Column(String(20), nullable=True, default='pending') # e.g., pending, processing, completed, failed
    error_message = Column(Text, nullable=True) # Store error details if status is 'failed'

    # Relationships
    image = relationship("ModelTestsImage", back_populates="feature_status")
    model = relationship("ModelTestsModel", back_populates="feature_status")

    __table_args__ = (
        UniqueConstraint('image_id', 'model_id', name='uq_feature_status'),
         Index('idx_feature_status_model', model_id),
    )

    def __repr__(self):
        return f"<ModelTestsFeatureStatus(image='{self.image_id}', model='{self.model_id}', status='{self.status}', computed_at='{self.last_computed_at}')>"

# Helper function to automatically create tables (optional, often done via migrations)
def init_db(app):
    with app.app_context():
        # Create tables based on models
        inspector = inspect(db.engine)
        if not inspector.has_table(ModelTestsImage.__tablename__):
             app.logger.info("Creating database tables...")
             db.create_all()
             app.logger.info("Database tables created.")
        else:
             app.logger.info("Database tables already exist.")