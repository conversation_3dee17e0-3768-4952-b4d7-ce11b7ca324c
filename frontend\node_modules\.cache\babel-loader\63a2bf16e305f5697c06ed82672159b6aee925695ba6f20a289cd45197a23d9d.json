{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents\\\\Programing\\\\TRO\\\\ModelTestsWorkbench\\\\frontend\\\\src\\\\components\\\\model-test-workbench\\\\CombinedScoresConfig.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useMemo } from 'react';\nimport { Box, Typography, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, CircularProgress, Alert, Button, IconButton, Tooltip, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, TextField, Select, MenuItem, FormControl, InputLabel, List, ListItem, ListItemText, Snackbar, Chip, Switch, FormControlLabel } from '@mui/material';\nimport AddIcon from '@mui/icons-material/Add';\nimport EditIcon from '@mui/icons-material/Edit';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport RefreshIcon from '@mui/icons-material/Refresh';\nimport { getCombinedScores, createCombinedScoreConfig, updateCombinedScoreConfig, deleteCombinedScoreConfig, listModels, triggerComputeCombinedScores // Import the new function\n} from '../../services/api_model_workbench';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst IP_CATEGORIES = [\"trademark\", \"copyright\", \"patent\"]; // Consistent casing with backend\n\nconst CombinedScoresConfig = () => {\n  _s();\n  const [configs, setConfigs] = useState([]);\n  const [models, setModels] = useState([]); // Stores { id, name, type, applicable_categories, ... }\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [refreshTrigger, setRefreshTrigger] = useState(0);\n\n  // Dialog State\n  const [dialogOpen, setDialogOpen] = useState(false);\n  const [isEditing, setIsEditing] = useState(false);\n  const [currentConfig, setCurrentConfig] = useState(null); // For editing\n  const [editingConfigId, setEditingConfigId] = useState(null); // Store ID separately for update\n  const [configName, setConfigName] = useState('');\n  const [configCategory, setConfigCategory] = useState('');\n  const [modelWeights, setModelWeights] = useState({}); // { model_uuid: weight }\n  const [isActive, setIsActive] = useState(true); // For is_active status\n  const [dialogLoading, setDialogLoading] = useState(false);\n  const [dialogError, setDialogError] = useState('');\n\n  // Delete Dialog State\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\n  const [deletingConfigId, setDeletingConfigId] = useState(null);\n  const [deleteLoading, setDeleteLoading] = useState(false);\n  const [deleteError, setDeleteError] = useState('');\n\n  // Snackbar State\n  const [snackbarOpen, setSnackbarOpen] = useState(false);\n  const [snackbarMessage, setSnackbarMessage] = useState('');\n  const [snackbarSeverity, setSnackbarSeverity] = useState('success');\n  const fetchConfigs = useCallback(async () => {\n    setIsLoading(true);\n    setError('');\n    try {\n      const response = await getCombinedScores(); // Assuming GET /api/combined-scores\n      // Backend sends: { id, config_name, ip_category, model_weights: {uuid: weight}, is_active }\n      setConfigs(response.data || []);\n    } catch (err) {\n      handleApiError(err, 'Failed to fetch combined score configurations.');\n      setConfigs([]);\n    } finally {\n      setIsLoading(false);\n    }\n  }, []);\n  const fetchModels = useCallback(async () => {\n    try {\n      const response = await listModels();\n      // Backend sends: { id (uuid), name, type, applicable_categories, active }\n      setModels(response.data || []);\n    } catch (err) {\n      handleApiError(err, 'Failed to fetch models for configuration.');\n      setModels([]);\n    }\n  }, []);\n  useEffect(() => {\n    fetchConfigs();\n    fetchModels();\n  }, [fetchConfigs, fetchModels, refreshTrigger]);\n  const handleRefresh = () => {\n    setRefreshTrigger(prev => prev + 1);\n  };\n  const handleOpenCreateDialog = () => {\n    setIsEditing(false);\n    setCurrentConfig(null);\n    setConfigName('');\n    setConfigCategory('');\n    setModelWeights({});\n    setIsActive(true);\n    setDialogError('');\n    setDialogOpen(true);\n    if (models.length === 0) fetchModels();\n  };\n  const handleOpenEditDialog = config => {\n    setIsEditing(true);\n    setCurrentConfig(config);\n    setEditingConfigId(config.config_id); // Set the editing ID\n    setConfigName(config.config_name || config.name || ''); // Prefer config_name\n    setConfigCategory(config.ip_category);\n    setModelWeights(config.model_weights || {}); // Expects { uuid: weight }\n    setIsActive(typeof config.is_active === 'boolean' ? config.is_active : true);\n    setDialogError('');\n    setDialogOpen(true);\n    if (models.length === 0) fetchModels();\n  };\n  const handleCloseDialog = () => {\n    setDialogOpen(false);\n    setTimeout(() => {\n      setCurrentConfig(null);\n      setConfigName('');\n      setConfigCategory('');\n      setModelWeights({});\n      setIsActive(true);\n      setDialogError('');\n      setDialogLoading(false);\n      setEditingConfigId(null); // Reset editing ID\n    }, 200);\n  };\n  const handleWeightChange = (modelId, value) => {\n    // modelId is UUID\n    // Store the raw input value as a string\n    setModelWeights(prev => ({\n      ...prev,\n      [modelId]: value\n    }));\n  };\n  const handleIsActiveChange = event => {\n    setIsActive(event.target.checked);\n  };\n  const handleDialogSubmit = async () => {\n    setDialogLoading(true);\n    setDialogError('');\n    const processedModelWeights = Object.entries(modelWeights).reduce((obj, [uuid, weightStr]) => {\n      const weight = parseFloat(weightStr);\n      if (!isNaN(weight)) {\n        const clampedWeight = Math.max(0, Math.min(1, weight));\n        if (clampedWeight > 0) {\n          obj[uuid] = parseFloat(clampedWeight.toFixed(4)); // Store with fixed precision\n        }\n      }\n      return obj;\n    }, {});\n    if (Object.keys(processedModelWeights).length === 0) {\n      setDialogError('At least one model must have a weight greater than 0.');\n      setDialogLoading(false);\n      return;\n    }\n\n    // Optional: Validate sum of weights if backend requires it (e.g., sum to 1)\n    // const totalWeight = Object.values(processedModelWeights).reduce((sum, weight) => sum + weight, 0);\n    // if (Math.abs(totalWeight - 1.0) > 0.001) { /* error */ }\n\n    const configData = {\n      config_name: configName.trim(),\n      // Use config_name for payload\n      ip_category: configCategory,\n      model_weights: processedModelWeights,\n      // Object { uuid: weight }\n      is_active: isActive\n    };\n    try {\n      if (isEditing && currentConfig) {\n        // For PUT, ip_category is removed by api.js if present in configData\n        await updateCombinedScoreConfig(editingConfigId, configData); // Use editingConfigId\n        showSnackbar('Configuration updated successfully.', 'success');\n        // Trigger combined score computation after update\n        await triggerComputeCombinedScores(configCategory);\n        showSnackbar(`Combined score computation triggered for ${configCategory}.`, 'info');\n      } else {\n        await createCombinedScoreConfig(configData);\n        showSnackbar('Configuration created successfully.', 'success');\n        // Trigger combined score computation after creation\n        await triggerComputeCombinedScores(configCategory);\n        showSnackbar(`Combined score computation triggered for ${configCategory}.`, 'info');\n      }\n      handleCloseDialog();\n      handleRefresh();\n    } catch (err) {\n      handleApiError(err, isEditing ? 'Failed to update configuration.' : 'Failed to create configuration.', setDialogError);\n    } finally {\n      setDialogLoading(false);\n    }\n  };\n  const handleOpenDeleteDialog = configId => {\n    setDeletingConfigId(configId);\n    setDeleteError('');\n    setDeleteDialogOpen(true);\n  };\n  const handleCloseDeleteDialog = () => {\n    console.log('Closing delete dialog.'); // Added log\n    setDeleteDialogOpen(false);\n    setDeletingConfigId(null);\n    setDeleteError('');\n    setDeleteLoading(false);\n  };\n  const handleDeleteConfig = async configIdToDelete => {\n    setDeleteLoading(true);\n    setDeleteError('');\n    try {\n      await deleteCombinedScoreConfig(configIdToDelete);\n      showSnackbar('Configuration deleted successfully.', 'success');\n      handleCloseDeleteDialog();\n      handleRefresh();\n    } catch (err) {\n      handleApiError(err, 'Failed to delete configuration.', setDeleteError);\n    } finally {\n      setDeleteLoading(false);\n    }\n  };\n  const handleApiError = (err, defaultMessage, specificErrorSetter = setError) => {\n    var _err$response, _err$response$data, _err$response2, _err$response2$data;\n    console.error(defaultMessage, err);\n    const message = ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.error) || ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.detail) || err.message || defaultMessage;\n    specificErrorSetter(message);\n    if (specificErrorSetter === setError) {\n      showSnackbar(message, 'error');\n    }\n  };\n  const showSnackbar = (message, severity = 'success') => {\n    setSnackbarMessage(message);\n    setSnackbarSeverity(severity);\n    setSnackbarOpen(true);\n  };\n  const handleCloseSnackbar = (event, reason) => {\n    if (reason === 'clickaway') return;\n    setSnackbarOpen(false);\n  };\n  const applicableModels = models.filter(model => {\n    var _model$applicable_ip_, _model$applicable_ip_2;\n    return model.is_active && (((_model$applicable_ip_ = model.applicable_ip_category) === null || _model$applicable_ip_ === void 0 ? void 0 : _model$applicable_ip_.includes(configCategory.toLowerCase())) || ((_model$applicable_ip_2 = model.applicable_ip_category) === null || _model$applicable_ip_2 === void 0 ? void 0 : _model$applicable_ip_2.includes('all')));\n  });\n  const processedModelWeights = React.useMemo(() => {\n    return Object.entries(modelWeights).filter(([, weightStr]) => {\n      const weightNum = parseFloat(weightStr);\n      return !isNaN(weightNum) && weightNum > 0;\n    }).reduce((obj, [uuid, weightStr]) => {\n      obj[uuid] = parseFloat(weightStr);\n      return obj;\n    }, {});\n  }, [modelWeights]);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      mt: 2\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: \"Combined Score Configurations\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"Refresh List\",\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: handleRefresh,\n            disabled: isLoading || dialogOpen,\n            children: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 12\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 26\n          }, this),\n          onClick: handleOpenCreateDialog,\n          disabled: isLoading || dialogOpen,\n          sx: {\n            ml: 1\n          },\n          children: \"Create New\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 12\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 17\n    }, this), isLoading ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        p: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 71\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 286,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        size: \"small\",\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"IP Category\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 293,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Model Weights\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Actions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: [configs.length === 0 && !isLoading && /*#__PURE__*/_jsxDEV(TableRow, {\n            children: /*#__PURE__*/_jsxDEV(TableCell, {\n              colSpan: 5,\n              align: \"center\",\n              children: \"No configurations found.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 27\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 17\n          }, this), configs.map(config => {\n            console.log('Rendering config:', config); // Added log\n            const displayWeights = config.model_weights ? Object.entries(config.model_weights).map(([uuid, weight]) => {\n              const model = models.find(m => m.id === uuid);\n              return {\n                name: model ? model.name : `UUID: ${uuid.substring(0, 8)}...`,\n                weight: weight\n              };\n            }) : [];\n            return /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [\" \", /*#__PURE__*/_jsxDEV(TableCell, {\n                children: config.config_name || config.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                sx: {\n                  textTransform: 'capitalize'\n                },\n                children: config.ip_category\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  label: config.is_active ? \"Active\" : \"Inactive\",\n                  color: config.is_active ? \"success\" : \"default\",\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: displayWeights.length > 0 ? displayWeights.map(mw => /*#__PURE__*/_jsxDEV(Chip, {\n                  label: `${mw.name}: ${Number(mw.weight).toFixed(2)}`,\n                  size: \"small\",\n                  sx: {\n                    mr: 0.5,\n                    mb: 0.5\n                  }\n                }, mw.name, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 25\n                }, this)) : 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Edit Configuration\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => handleOpenEditDialog(config),\n                    disabled: dialogOpen,\n                    children: /*#__PURE__*/_jsxDEV(EditIcon, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 325,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 324,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Delete Configuration\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => handleOpenDeleteDialog(config.config_id),\n                    disabled: dialogOpen,\n                    sx: {\n                      pointerEvents: 'auto'\n                    },\n                    children: [\" \", /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                      fontSize: \"small\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 330,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 329,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 21\n              }, this)]\n            }, config.config_id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 19\n            }, this);\n          })]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 288,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: dialogOpen,\n      onClose: handleCloseDialog,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: isEditing ? 'Edit Configuration' : 'Create New Configuration'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [dialogError && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            mb: 2\n          },\n          children: dialogError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 27\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          id: \"config-name\",\n          label: \"Configuration Name\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"outlined\",\n          value: configName,\n          onChange: e => setConfigName(e.target.value),\n          disabled: dialogLoading,\n          sx: {\n            mb: 2\n          },\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n          fullWidth: true,\n          margin: \"dense\",\n          variant: \"outlined\",\n          disabled: dialogLoading || isEditing,\n          sx: {\n            mb: 2\n          },\n          required: true,\n          children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n            id: \"config-category-label\",\n            children: \"IP Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 14\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            labelId: \"config-category-label\",\n            id: \"config-category\",\n            value: configCategory,\n            label: \"IP Category\",\n            onChange: e => {\n              setConfigCategory(e.target.value);\n              setModelWeights({}); // Reset weights when category changes\n            },\n            children: IP_CATEGORIES.map(cat => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: cat,\n              sx: {\n                textTransform: 'capitalize'\n              },\n              children: cat\n            }, cat, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 42\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 14\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n          control: /*#__PURE__*/_jsxDEV(Switch, {\n            checked: isActive,\n            onChange: handleIsActiveChange,\n            disabled: dialogLoading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 22\n          }, this),\n          label: \"Active Configuration\",\n          sx: {\n            mb: 1,\n            mt: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 11\n        }, this), configCategory && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            sx: {\n              mt: 2,\n              mb: 1\n            },\n            children: \"Model Weights (0.0 - 1.0)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 15\n          }, this), applicableModels.length === 0 && models.length > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"warning\",\n            sx: {\n              mb: 2\n            },\n            children: [\"No active models found for the selected category '\", configCategory, \"'.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 18\n          }, this), applicableModels.length === 0 && models.length === 0 && !isLoading && /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"error\",\n            sx: {\n              mb: 2\n            },\n            children: \"Could not load models. Please try refreshing.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 18\n          }, this), /*#__PURE__*/_jsxDEV(List, {\n            dense: true,\n            sx: {\n              maxHeight: 300,\n              overflow: 'auto'\n            },\n            children: applicableModels.map(model =>\n            /*#__PURE__*/\n            // model.id is the UUID\n            _jsxDEV(ListItem, {\n              disablePadding: true,\n              sx: {\n                mb: 1.5,\n                display: 'flex',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: model.model_name,\n                secondary: model.model_type,\n                sx: {\n                  mr: 2,\n                  flexGrow: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 22\n              }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                label: \"Weight\",\n                id: `weight-${model.model_id}`,\n                value: modelWeights[model.model_id] || '',\n                onChange: e => handleWeightChange(model.model_id, e.target.value),\n                type: \"number\",\n                inputProps: {\n                  step: \"0.01\",\n                  min: \"0\",\n                  max: \"1\"\n                },\n                variant: \"outlined\",\n                size: \"small\",\n                sx: {\n                  width: '120px'\n                },\n                disabled: dialogLoading\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 395,\n                columnNumber: 22\n              }, this)]\n            }, model.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDialog,\n          disabled: dialogLoading,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 415,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleDialogSubmit,\n          disabled: dialogLoading || !configName || !configCategory || applicableModels.length > 0 && Object.keys(processedModelWeights).length === 0 || isEditing && !editingConfigId,\n          variant: \"contained\",\n          children: dialogLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 30\n          }, this) : isEditing ? 'Save Changes' : 'Create'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 414,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: deleteDialogOpen,\n      onClose: handleCloseDeleteDialog,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Confirm Deletion\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 427,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [deleteError && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            mb: 2\n          },\n          children: deleteError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 27\n        }, this), /*#__PURE__*/_jsxDEV(DialogContentText, {\n          children: \"Are you sure you want to delete this combined score configuration? This action cannot be undone.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 428,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDeleteDialog,\n          disabled: deleteLoading,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 435,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => handleDeleteConfig(deletingConfigId),\n          color: \"error\",\n          autoFocus: true,\n          disabled: deleteLoading,\n          children: [\" \", deleteLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 30\n          }, this) : 'Delete']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 434,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 426,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Snackbar, {\n      open: snackbarOpen,\n      autoHideDuration: 6000,\n      onClose: handleCloseSnackbar,\n      anchorOrigin: {\n        vertical: 'bottom',\n        horizontal: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        onClose: handleCloseSnackbar,\n        severity: snackbarSeverity,\n        sx: {\n          width: '100%'\n        },\n        children: snackbarMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 443,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 442,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 262,\n    columnNumber: 5\n  }, this);\n};\n_s(CombinedScoresConfig, \"ZblwmI0nRA8rqv77XCbVCT0dfbg=\");\n_c = CombinedScoresConfig;\nexport default CombinedScoresConfig;\nvar _c;\n$RefreshReg$(_c, \"CombinedScoresConfig\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "Box", "Typography", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "CircularProgress", "<PERSON><PERSON>", "<PERSON><PERSON>", "IconButton", "<PERSON><PERSON><PERSON>", "Dialog", "DialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogTitle", "TextField", "Select", "MenuItem", "FormControl", "InputLabel", "List", "ListItem", "ListItemText", "Snackbar", "Chip", "Switch", "FormControlLabel", "AddIcon", "EditIcon", "DeleteIcon", "RefreshIcon", "getCombinedScores", "createCombinedScoreConfig", "updateCombinedScoreConfig", "deleteCombinedScoreConfig", "listModels", "triggerComputeCombinedScores", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "IP_CATEGORIES", "CombinedScoresConfig", "_s", "configs", "setConfigs", "models", "setModels", "isLoading", "setIsLoading", "error", "setError", "refreshTrigger", "setRefreshTrigger", "dialogOpen", "setDialogOpen", "isEditing", "setIsEditing", "currentConfig", "setCurrentConfig", "editingConfigId", "setEditingConfigId", "config<PERSON><PERSON>", "setConfigName", "config<PERSON>ate<PERSON><PERSON>", "setConfigCategory", "modelWeights", "setModelWeights", "isActive", "setIsActive", "dialogLoading", "setDialogLoading", "dialogError", "setDialogError", "deleteDialogOpen", "setDeleteDialogOpen", "deletingConfigId", "setDeletingConfigId", "deleteLoading", "setDeleteLoading", "deleteError", "setDeleteError", "snackbarOpen", "setSnackbarOpen", "snackbarMessage", "setSnackbarMessage", "snackbarSeverity", "setSnackbarSeverity", "fetchConfigs", "response", "data", "err", "handleApiError", "fetchModels", "handleRefresh", "prev", "handleOpenCreateDialog", "length", "handleOpenEditDialog", "config", "config_id", "config_name", "name", "ip_category", "model_weights", "is_active", "handleCloseDialog", "setTimeout", "handleWeightChange", "modelId", "value", "handleIsActiveChange", "event", "target", "checked", "handleDialogSubmit", "processedModelWeights", "Object", "entries", "reduce", "obj", "uuid", "weightStr", "weight", "parseFloat", "isNaN", "clampedWeight", "Math", "max", "min", "toFixed", "keys", "configData", "trim", "showSnackbar", "handleOpenDeleteDialog", "configId", "handleCloseDeleteDialog", "console", "log", "handleDeleteConfig", "configIdToDelete", "defaultMessage", "specificErrorSetter", "_err$response", "_err$response$data", "_err$response2", "_err$response2$data", "message", "detail", "severity", "handleCloseSnackbar", "reason", "applicableModels", "filter", "model", "_model$applicable_ip_", "_model$applicable_ip_2", "applicable_ip_category", "includes", "toLowerCase", "weightNum", "sx", "mt", "children", "display", "justifyContent", "alignItems", "mb", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "onClick", "disabled", "startIcon", "ml", "p", "component", "size", "colSpan", "align", "map", "displayWeights", "find", "m", "id", "substring", "textTransform", "label", "color", "mw", "Number", "mr", "fontSize", "pointerEvents", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "autoFocus", "margin", "type", "onChange", "e", "required", "labelId", "cat", "control", "dense", "maxHeight", "overflow", "disablePadding", "primary", "model_name", "secondary", "model_type", "flexGrow", "model_id", "inputProps", "step", "width", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "_c", "$RefreshReg$"], "sources": ["D:/Documents/Programing/TRO/ModelTestsWorkbench/frontend/src/components/model-test-workbench/CombinedScoresConfig.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useMemo } from 'react';\r\nimport {\r\n  Box, Typography, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper,\r\n  CircularProgress, Alert, Button, IconButton, Tooltip, Dialog, DialogActions, DialogContent,\r\n  DialogContentText, DialogTitle, TextField, Select, MenuItem, FormControl, InputLabel, List, ListItem, ListItemText,\r\n  Snackbar, Chip, Switch, FormControlLabel\r\n} from '@mui/material';\r\nimport AddIcon from '@mui/icons-material/Add';\r\nimport EditIcon from '@mui/icons-material/Edit';\r\nimport DeleteIcon from '@mui/icons-material/Delete';\r\nimport RefreshIcon from '@mui/icons-material/Refresh';\r\nimport {\r\n  getCombinedScores, createCombinedScoreConfig, updateCombinedScoreConfig, deleteCombinedScoreConfig, listModels,\r\n  triggerComputeCombinedScores // Import the new function\r\n} from '../../services/api_model_workbench';\r\n\r\nconst IP_CATEGORIES = [\"trademark\", \"copyright\", \"patent\"]; // Consistent casing with backend\r\n\r\nconst CombinedScoresConfig = () => {\r\n  const [configs, setConfigs] = useState([]);\r\n  const [models, setModels] = useState([]); // Stores { id, name, type, applicable_categories, ... }\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState('');\r\n  const [refreshTrigger, setRefreshTrigger] = useState(0);\r\n\r\n  // Dialog State\r\n  const [dialogOpen, setDialogOpen] = useState(false);\r\n  const [isEditing, setIsEditing] = useState(false);\r\n  const [currentConfig, setCurrentConfig] = useState(null); // For editing\r\n  const [editingConfigId, setEditingConfigId] = useState(null); // Store ID separately for update\r\n  const [configName, setConfigName] = useState('');\r\n  const [configCategory, setConfigCategory] = useState('');\r\n  const [modelWeights, setModelWeights] = useState({}); // { model_uuid: weight }\r\n  const [isActive, setIsActive] = useState(true); // For is_active status\r\n  const [dialogLoading, setDialogLoading] = useState(false);\r\n  const [dialogError, setDialogError] = useState('');\r\n\r\n  // Delete Dialog State\r\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\r\n  const [deletingConfigId, setDeletingConfigId] = useState(null);\r\n  const [deleteLoading, setDeleteLoading] = useState(false);\r\n  const [deleteError, setDeleteError] = useState('');\r\n\r\n  // Snackbar State\r\n  const [snackbarOpen, setSnackbarOpen] = useState(false);\r\n  const [snackbarMessage, setSnackbarMessage] = useState('');\r\n  const [snackbarSeverity, setSnackbarSeverity] = useState('success');\r\n\r\n  const fetchConfigs = useCallback(async () => {\r\n    setIsLoading(true);\r\n    setError('');\r\n    try {\r\n      const response = await getCombinedScores(); // Assuming GET /api/combined-scores\r\n      // Backend sends: { id, config_name, ip_category, model_weights: {uuid: weight}, is_active }\r\n      setConfigs(response.data || []);\r\n    } catch (err) {\r\n      handleApiError(err, 'Failed to fetch combined score configurations.');\r\n      setConfigs([]);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  const fetchModels = useCallback(async () => {\r\n    try {\r\n      const response = await listModels();\r\n      // Backend sends: { id (uuid), name, type, applicable_categories, active }\r\n      setModels(response.data || []);\r\n    } catch (err) {\r\n      handleApiError(err, 'Failed to fetch models for configuration.');\r\n      setModels([]);\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    fetchConfigs();\r\n    fetchModels();\r\n  }, [fetchConfigs, fetchModels, refreshTrigger]);\r\n\r\n  const handleRefresh = () => {\r\n    setRefreshTrigger(prev => prev + 1);\r\n  };\r\n\r\n  const handleOpenCreateDialog = () => {\r\n    setIsEditing(false);\r\n    setCurrentConfig(null);\r\n    setConfigName('');\r\n    setConfigCategory('');\r\n    setModelWeights({});\r\n    setIsActive(true);\r\n    setDialogError('');\r\n    setDialogOpen(true);\r\n    if (models.length === 0) fetchModels();\r\n  };\r\n\r\n  const handleOpenEditDialog = (config) => {\r\n    setIsEditing(true);\r\n    setCurrentConfig(config);\r\n    setEditingConfigId(config.config_id); // Set the editing ID\r\n    setConfigName(config.config_name || config.name || ''); // Prefer config_name\r\n    setConfigCategory(config.ip_category);\r\n    setModelWeights(config.model_weights || {}); // Expects { uuid: weight }\r\n    setIsActive(typeof config.is_active === 'boolean' ? config.is_active : true);\r\n    setDialogError('');\r\n    setDialogOpen(true);\r\n    if (models.length === 0) fetchModels();\r\n  };\r\n\r\n  const handleCloseDialog = () => {\r\n    setDialogOpen(false);\r\n    setTimeout(() => {\r\n      setCurrentConfig(null);\r\n      setConfigName('');\r\n      setConfigCategory('');\r\n      setModelWeights({});\r\n      setIsActive(true);\r\n      setDialogError('');\r\n      setDialogLoading(false);\r\n      setEditingConfigId(null); // Reset editing ID\r\n    }, 200);\r\n  };\r\n\r\n  const handleWeightChange = (modelId, value) => { // modelId is UUID\r\n    // Store the raw input value as a string\r\n    setModelWeights(prev => ({\r\n      ...prev,\r\n      [modelId]: value\r\n    }));\r\n  };\r\n  \r\n  const handleIsActiveChange = (event) => {\r\n    setIsActive(event.target.checked);\r\n  };\r\n\r\n  const handleDialogSubmit = async () => {\r\n    setDialogLoading(true);\r\n    setDialogError('');\r\n\r\n    const processedModelWeights = Object.entries(modelWeights)\r\n      .reduce((obj, [uuid, weightStr]) => {\r\n        const weight = parseFloat(weightStr);\r\n        if (!isNaN(weight)) {\r\n          const clampedWeight = Math.max(0, Math.min(1, weight));\r\n          if (clampedWeight > 0) {\r\n            obj[uuid] = parseFloat(clampedWeight.toFixed(4)); // Store with fixed precision\r\n          }\r\n        }\r\n        return obj;\r\n      }, {});\r\n\r\n    if (Object.keys(processedModelWeights).length === 0) {\r\n      setDialogError('At least one model must have a weight greater than 0.');\r\n      setDialogLoading(false);\r\n      return;\r\n    }\r\n\r\n    // Optional: Validate sum of weights if backend requires it (e.g., sum to 1)\r\n    // const totalWeight = Object.values(processedModelWeights).reduce((sum, weight) => sum + weight, 0);\r\n    // if (Math.abs(totalWeight - 1.0) > 0.001) { /* error */ }\r\n\r\n\r\n    const configData = {\r\n      config_name: configName.trim(), // Use config_name for payload\r\n      ip_category: configCategory,\r\n      model_weights: processedModelWeights, // Object { uuid: weight }\r\n      is_active: isActive,\r\n    };\r\n\r\n    try {\r\n      if (isEditing && currentConfig) {\r\n        // For PUT, ip_category is removed by api.js if present in configData\r\n        await updateCombinedScoreConfig(editingConfigId, configData); // Use editingConfigId\r\n        showSnackbar('Configuration updated successfully.', 'success');\r\n        // Trigger combined score computation after update\r\n        await triggerComputeCombinedScores(configCategory);\r\n        showSnackbar(`Combined score computation triggered for ${configCategory}.`, 'info');\r\n      } else {\r\n        await createCombinedScoreConfig(configData);\r\n        showSnackbar('Configuration created successfully.', 'success');\r\n        // Trigger combined score computation after creation\r\n        await triggerComputeCombinedScores(configCategory);\r\n        showSnackbar(`Combined score computation triggered for ${configCategory}.`, 'info');\r\n      }\r\n      handleCloseDialog();\r\n      handleRefresh();\r\n    } catch (err) {\r\n      handleApiError(err, isEditing ? 'Failed to update configuration.' : 'Failed to create configuration.', setDialogError);\r\n    } finally {\r\n      setDialogLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleOpenDeleteDialog = (configId) => {\r\n    setDeletingConfigId(configId);\r\n    setDeleteError('');\r\n    setDeleteDialogOpen(true);\r\n  };\r\n\r\n  const handleCloseDeleteDialog = () => {\r\n    console.log('Closing delete dialog.'); // Added log\r\n    setDeleteDialogOpen(false);\r\n    setDeletingConfigId(null);\r\n    setDeleteError('');\r\n    setDeleteLoading(false);\r\n  };\r\n\r\n  const handleDeleteConfig = async (configIdToDelete) => {\r\n    setDeleteLoading(true);\r\n    setDeleteError('');\r\n    try {\r\n      await deleteCombinedScoreConfig(configIdToDelete);\r\n      showSnackbar('Configuration deleted successfully.', 'success');\r\n      handleCloseDeleteDialog();\r\n      handleRefresh();\r\n    } catch (err) {\r\n      handleApiError(err, 'Failed to delete configuration.', setDeleteError);\r\n    } finally {\r\n      setDeleteLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleApiError = (err, defaultMessage, specificErrorSetter = setError) => {\r\n    console.error(defaultMessage, err);\r\n    const message = err.response?.data?.error || err.response?.data?.detail || err.message || defaultMessage;\r\n    specificErrorSetter(message);\r\n    if (specificErrorSetter === setError) {\r\n      showSnackbar(message, 'error');\r\n    }\r\n  };\r\n\r\n  const showSnackbar = (message, severity = 'success') => {\r\n    setSnackbarMessage(message);\r\n    setSnackbarSeverity(severity);\r\n    setSnackbarOpen(true);\r\n  };\r\n\r\n  const handleCloseSnackbar = (event, reason) => {\r\n    if (reason === 'clickaway') return;\r\n    setSnackbarOpen(false);\r\n  };\r\n\r\n  const applicableModels = models.filter(model =>\r\n    model.is_active && (\r\n      model.applicable_ip_category?.includes(configCategory.toLowerCase()) ||\r\n      model.applicable_ip_category?.includes('all')\r\n    )\r\n  );\r\n\r\n  const processedModelWeights = React.useMemo(() => {\r\n    return Object.entries(modelWeights)\r\n      .filter(([, weightStr]) => {\r\n        const weightNum = parseFloat(weightStr);\r\n        return !isNaN(weightNum) && weightNum > 0;\r\n      })\r\n      .reduce((obj, [uuid, weightStr]) => {\r\n        obj[uuid] = parseFloat(weightStr);\r\n        return obj;\r\n      }, {});\r\n  }, [modelWeights]);\r\n\r\n  return (\r\n    <Box sx={{ mt: 2 }}>\r\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\r\n        <Typography variant=\"h6\">Combined Score Configurations</Typography>\r\n        <Box>\r\n           <Tooltip title=\"Refresh List\">\r\n              <IconButton onClick={handleRefresh} disabled={isLoading || dialogOpen}>\r\n                <RefreshIcon />\r\n              </IconButton>\r\n            </Tooltip>\r\n           <Button\r\n              variant=\"contained\"\r\n              startIcon={<AddIcon />}\r\n              onClick={handleOpenCreateDialog}\r\n              disabled={isLoading || dialogOpen}\r\n              sx={{ ml: 1 }}\r\n            >\r\n              Create New\r\n            </Button>\r\n        </Box>\r\n      </Box>\r\n\r\n      {error && <Alert severity=\"error\" sx={{ mb: 2 }}>{error}</Alert>}\r\n\r\n      {isLoading ? (\r\n        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}><CircularProgress /></Box>\r\n      ) : (\r\n        <TableContainer component={Paper}>\r\n          <Table size=\"small\">\r\n            <TableHead>\r\n              <TableRow>\r\n                <TableCell>Name</TableCell>\r\n                <TableCell>IP Category</TableCell>\r\n                <TableCell>Status</TableCell>\r\n                <TableCell>Model Weights</TableCell>\r\n                <TableCell>Actions</TableCell>\r\n              </TableRow>\r\n            </TableHead>\r\n            <TableBody>\r\n              {configs.length === 0 && !isLoading && (\r\n                <TableRow><TableCell colSpan={5} align=\"center\">No configurations found.</TableCell></TableRow>\r\n              )}\r\n              {configs.map((config) => {\r\n                console.log('Rendering config:', config); // Added log\r\n                const displayWeights = config.model_weights ? Object.entries(config.model_weights).map(([uuid, weight]) => {\r\n                  const model = models.find(m => m.id === uuid);\r\n                  return { name: model ? model.name : `UUID: ${uuid.substring(0,8)}...`, weight: weight };\r\n                }) : [];\r\n\r\n                return (\r\n                  <TableRow key={config.config_id}> {/* Use config_id for key */}\r\n                    <TableCell>{config.config_name || config.name}</TableCell>\r\n                    <TableCell sx={{ textTransform: 'capitalize' }}>{config.ip_category}</TableCell>\r\n                    <TableCell>\r\n                        <Chip label={config.is_active ? \"Active\" : \"Inactive\"} color={config.is_active ? \"success\" : \"default\"} size=\"small\" />\r\n                    </TableCell>\r\n                    <TableCell>\r\n                      {displayWeights.length > 0 ? displayWeights.map(mw => (\r\n                        <Chip key={mw.name} label={`${mw.name}: ${Number(mw.weight).toFixed(2)}`} size=\"small\" sx={{ mr: 0.5, mb: 0.5 }}/>\r\n                      )) : 'N/A'}\r\n                    </TableCell>\r\n                    <TableCell>\r\n                      <Tooltip title=\"Edit Configuration\">\r\n                        <IconButton size=\"small\" onClick={() => handleOpenEditDialog(config)} disabled={dialogOpen}>\r\n                          <EditIcon fontSize=\"small\" />\r\n                        </IconButton>\r\n                      </Tooltip>\r\n                      <Tooltip title=\"Delete Configuration\">\r\n                        <IconButton size=\"small\" onClick={() => handleOpenDeleteDialog(config.config_id)} disabled={dialogOpen} sx={{ pointerEvents: 'auto' }}> {/* Use config.config_id */}\r\n                          <DeleteIcon fontSize=\"small\" />\r\n                        </IconButton>\r\n                      </Tooltip>\r\n                    </TableCell>\r\n                  </TableRow>\r\n                );\r\n              })}\r\n            </TableBody>\r\n          </Table>\r\n        </TableContainer>\r\n      )}\r\n\r\n      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\r\n        <DialogTitle>{isEditing ? 'Edit Configuration' : 'Create New Configuration'}</DialogTitle>\r\n        <DialogContent>\r\n          {dialogError && <Alert severity=\"error\" sx={{ mb: 2 }}>{dialogError}</Alert>}\r\n          <TextField\r\n            autoFocus\r\n            margin=\"dense\"\r\n            id=\"config-name\"\r\n            label=\"Configuration Name\"\r\n            type=\"text\"\r\n            fullWidth\r\n            variant=\"outlined\"\r\n            value={configName}\r\n            onChange={(e) => setConfigName(e.target.value)}\r\n            disabled={dialogLoading}\r\n            sx={{ mb: 2 }}\r\n            required\r\n          />\r\n          <FormControl fullWidth margin=\"dense\" variant=\"outlined\" disabled={dialogLoading || isEditing} sx={{ mb: 2 }} required>\r\n             <InputLabel id=\"config-category-label\">IP Category</InputLabel>\r\n             <Select\r\n                labelId=\"config-category-label\"\r\n                id=\"config-category\"\r\n                value={configCategory}\r\n                label=\"IP Category\"\r\n                onChange={(e) => {\r\n                    setConfigCategory(e.target.value);\r\n                    setModelWeights({}); // Reset weights when category changes\r\n                }}\r\n             >\r\n               {IP_CATEGORIES.map(cat => <MenuItem key={cat} value={cat} sx={{ textTransform: 'capitalize' }}>{cat}</MenuItem>)}\r\n             </Select>\r\n          </FormControl>\r\n\r\n          <FormControlLabel\r\n            control={<Switch checked={isActive} onChange={handleIsActiveChange} disabled={dialogLoading} />}\r\n            label=\"Active Configuration\"\r\n            sx={{ mb: 1, mt:1 }}\r\n          />\r\n\r\n          {configCategory && (\r\n            <>\r\n              <Typography variant=\"subtitle1\" sx={{ mt: 2, mb: 1 }}>Model Weights (0.0 - 1.0)</Typography>\r\n              {applicableModels.length === 0 && models.length > 0 && (\r\n                 <Alert severity=\"warning\" sx={{ mb: 2 }}>No active models found for the selected category '{configCategory}'.</Alert>\r\n              )}\r\n              {applicableModels.length === 0 && models.length === 0 && !isLoading && (\r\n                 <Alert severity=\"error\" sx={{ mb: 2 }}>Could not load models. Please try refreshing.</Alert>\r\n              )}\r\n              <List dense sx={{maxHeight: 300, overflow: 'auto'}}>\r\n                {applicableModels.map(model => ( // model.id is the UUID\r\n                  <ListItem key={model.id} disablePadding sx={{ mb: 1.5, display: 'flex', justifyContent: 'space-between' }}>\r\n                     <ListItemText primary={model.model_name} secondary={model.model_type} sx={{ mr: 2, flexGrow: 1}}/>\r\n                     <TextField\r\n                        label=\"Weight\"\r\n                        id={`weight-${model.model_id}`}\r\n                        value={modelWeights[model.model_id] || ''}\r\n                        onChange={(e) => handleWeightChange(model.model_id, e.target.value)}\r\n                        type=\"number\"\r\n                        inputProps={{ step: \"0.01\", min: \"0\", max: \"1\" }}\r\n                        variant=\"outlined\"\r\n                        size=\"small\"\r\n                        sx={{ width: '120px' }}\r\n                        disabled={dialogLoading}\r\n                     />\r\n                  </ListItem>\r\n                ))}\r\n              </List>\r\n            </>\r\n          )}\r\n\r\n        </DialogContent>\r\n        <DialogActions>\r\n          <Button onClick={handleCloseDialog} disabled={dialogLoading}>Cancel</Button>\r\n          <Button\r\n             onClick={handleDialogSubmit}\r\n             disabled={dialogLoading || !configName || !configCategory || (applicableModels.length > 0 && Object.keys(processedModelWeights).length === 0) || (isEditing && !editingConfigId)}\r\n             variant=\"contained\"\r\n          >\r\n            {dialogLoading ? <CircularProgress size={24} /> : (isEditing ? 'Save Changes' : 'Create')}\r\n          </Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n\r\n      <Dialog open={deleteDialogOpen} onClose={handleCloseDeleteDialog}>\r\n        <DialogTitle>Confirm Deletion</DialogTitle>\r\n        <DialogContent>\r\n          {deleteError && <Alert severity=\"error\" sx={{ mb: 2 }}>{deleteError}</Alert>}\r\n          <DialogContentText>\r\n            Are you sure you want to delete this combined score configuration? This action cannot be undone.\r\n          </DialogContentText>\r\n        </DialogContent>\r\n        <DialogActions>\r\n          <Button onClick={handleCloseDeleteDialog} disabled={deleteLoading}>Cancel</Button>\r\n          <Button onClick={() => handleDeleteConfig(deletingConfigId)} color=\"error\" autoFocus disabled={deleteLoading}> {/* Removed console log */}\r\n            {deleteLoading ? <CircularProgress size={20} /> : 'Delete'}\r\n          </Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n\r\n      <Snackbar open={snackbarOpen} autoHideDuration={6000} onClose={handleCloseSnackbar} anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}>\r\n        <Alert onClose={handleCloseSnackbar} severity={snackbarSeverity} sx={{ width: '100%' }}>\r\n          {snackbarMessage}\r\n        </Alert>\r\n      </Snackbar>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default CombinedScoresConfig;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACxE,SACEC,GAAG,EAAEC,UAAU,EAAEC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAEC,cAAc,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,KAAK,EACxFC,gBAAgB,EAAEC,KAAK,EAAEC,MAAM,EAAEC,UAAU,EAAEC,OAAO,EAAEC,MAAM,EAAEC,aAAa,EAAEC,aAAa,EAC1FC,iBAAiB,EAAEC,WAAW,EAAEC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,UAAU,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,YAAY,EAClHC,QAAQ,EAAEC,IAAI,EAAEC,MAAM,EAAEC,gBAAgB,QACnC,eAAe;AACtB,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,WAAW,MAAM,6BAA6B;AACrD,SACEC,iBAAiB,EAAEC,yBAAyB,EAAEC,yBAAyB,EAAEC,yBAAyB,EAAEC,UAAU,EAC9GC,4BAA4B,CAAC;AAAA,OACxB,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE5C,MAAMC,aAAa,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC;;AAE5D,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACsD,MAAM,EAAEC,SAAS,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACwD,SAAS,EAAEC,YAAY,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC0D,KAAK,EAAEC,QAAQ,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC4D,cAAc,EAAEC,iBAAiB,CAAC,GAAG7D,QAAQ,CAAC,CAAC,CAAC;;EAEvD;EACA,MAAM,CAAC8D,UAAU,EAAEC,aAAa,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACgE,SAAS,EAAEC,YAAY,CAAC,GAAGjE,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACkE,aAAa,EAAEC,gBAAgB,CAAC,GAAGnE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACoE,eAAe,EAAEC,kBAAkB,CAAC,GAAGrE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC9D,MAAM,CAACsE,UAAU,EAAEC,aAAa,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwE,cAAc,EAAEC,iBAAiB,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC0E,YAAY,EAAEC,eAAe,CAAC,GAAG3E,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACtD,MAAM,CAAC4E,QAAQ,EAAEC,WAAW,CAAC,GAAG7E,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAChD,MAAM,CAAC8E,aAAa,EAAEC,gBAAgB,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACgF,WAAW,EAAEC,cAAc,CAAC,GAAGjF,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACA,MAAM,CAACkF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnF,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACoF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrF,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACsF,aAAa,EAAEC,gBAAgB,CAAC,GAAGvF,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACwF,WAAW,EAAEC,cAAc,CAAC,GAAGzF,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACA,MAAM,CAAC0F,YAAY,EAAEC,eAAe,CAAC,GAAG3F,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4F,eAAe,EAAEC,kBAAkB,CAAC,GAAG7F,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC8F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/F,QAAQ,CAAC,SAAS,CAAC;EAEnE,MAAMgG,YAAY,GAAG9F,WAAW,CAAC,YAAY;IAC3CuD,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,EAAE,CAAC;IACZ,IAAI;MACF,MAAMsC,QAAQ,GAAG,MAAM1D,iBAAiB,CAAC,CAAC,CAAC,CAAC;MAC5C;MACAc,UAAU,CAAC4C,QAAQ,CAACC,IAAI,IAAI,EAAE,CAAC;IACjC,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,cAAc,CAACD,GAAG,EAAE,gDAAgD,CAAC;MACrE9C,UAAU,CAAC,EAAE,CAAC;IAChB,CAAC,SAAS;MACRI,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM4C,WAAW,GAAGnG,WAAW,CAAC,YAAY;IAC1C,IAAI;MACF,MAAM+F,QAAQ,GAAG,MAAMtD,UAAU,CAAC,CAAC;MACnC;MACAY,SAAS,CAAC0C,QAAQ,CAACC,IAAI,IAAI,EAAE,CAAC;IAChC,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,cAAc,CAACD,GAAG,EAAE,2CAA2C,CAAC;MAChE5C,SAAS,CAAC,EAAE,CAAC;IACf;EACF,CAAC,EAAE,EAAE,CAAC;EAENtD,SAAS,CAAC,MAAM;IACd+F,YAAY,CAAC,CAAC;IACdK,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACL,YAAY,EAAEK,WAAW,EAAEzC,cAAc,CAAC,CAAC;EAE/C,MAAM0C,aAAa,GAAGA,CAAA,KAAM;IAC1BzC,iBAAiB,CAAC0C,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;EACrC,CAAC;EAED,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;IACnCvC,YAAY,CAAC,KAAK,CAAC;IACnBE,gBAAgB,CAAC,IAAI,CAAC;IACtBI,aAAa,CAAC,EAAE,CAAC;IACjBE,iBAAiB,CAAC,EAAE,CAAC;IACrBE,eAAe,CAAC,CAAC,CAAC,CAAC;IACnBE,WAAW,CAAC,IAAI,CAAC;IACjBI,cAAc,CAAC,EAAE,CAAC;IAClBlB,aAAa,CAAC,IAAI,CAAC;IACnB,IAAIT,MAAM,CAACmD,MAAM,KAAK,CAAC,EAAEJ,WAAW,CAAC,CAAC;EACxC,CAAC;EAED,MAAMK,oBAAoB,GAAIC,MAAM,IAAK;IACvC1C,YAAY,CAAC,IAAI,CAAC;IAClBE,gBAAgB,CAACwC,MAAM,CAAC;IACxBtC,kBAAkB,CAACsC,MAAM,CAACC,SAAS,CAAC,CAAC,CAAC;IACtCrC,aAAa,CAACoC,MAAM,CAACE,WAAW,IAAIF,MAAM,CAACG,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC;IACxDrC,iBAAiB,CAACkC,MAAM,CAACI,WAAW,CAAC;IACrCpC,eAAe,CAACgC,MAAM,CAACK,aAAa,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7CnC,WAAW,CAAC,OAAO8B,MAAM,CAACM,SAAS,KAAK,SAAS,GAAGN,MAAM,CAACM,SAAS,GAAG,IAAI,CAAC;IAC5EhC,cAAc,CAAC,EAAE,CAAC;IAClBlB,aAAa,CAAC,IAAI,CAAC;IACnB,IAAIT,MAAM,CAACmD,MAAM,KAAK,CAAC,EAAEJ,WAAW,CAAC,CAAC;EACxC,CAAC;EAED,MAAMa,iBAAiB,GAAGA,CAAA,KAAM;IAC9BnD,aAAa,CAAC,KAAK,CAAC;IACpBoD,UAAU,CAAC,MAAM;MACfhD,gBAAgB,CAAC,IAAI,CAAC;MACtBI,aAAa,CAAC,EAAE,CAAC;MACjBE,iBAAiB,CAAC,EAAE,CAAC;MACrBE,eAAe,CAAC,CAAC,CAAC,CAAC;MACnBE,WAAW,CAAC,IAAI,CAAC;MACjBI,cAAc,CAAC,EAAE,CAAC;MAClBF,gBAAgB,CAAC,KAAK,CAAC;MACvBV,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC5B,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;EAED,MAAM+C,kBAAkB,GAAGA,CAACC,OAAO,EAAEC,KAAK,KAAK;IAAE;IAC/C;IACA3C,eAAe,CAAC4B,IAAI,KAAK;MACvB,GAAGA,IAAI;MACP,CAACc,OAAO,GAAGC;IACb,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMC,oBAAoB,GAAIC,KAAK,IAAK;IACtC3C,WAAW,CAAC2C,KAAK,CAACC,MAAM,CAACC,OAAO,CAAC;EACnC,CAAC;EAED,MAAMC,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC5C,gBAAgB,CAAC,IAAI,CAAC;IACtBE,cAAc,CAAC,EAAE,CAAC;IAElB,MAAM2C,qBAAqB,GAAGC,MAAM,CAACC,OAAO,CAACpD,YAAY,CAAC,CACvDqD,MAAM,CAAC,CAACC,GAAG,EAAE,CAACC,IAAI,EAAEC,SAAS,CAAC,KAAK;MAClC,MAAMC,MAAM,GAAGC,UAAU,CAACF,SAAS,CAAC;MACpC,IAAI,CAACG,KAAK,CAACF,MAAM,CAAC,EAAE;QAClB,MAAMG,aAAa,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEN,MAAM,CAAC,CAAC;QACtD,IAAIG,aAAa,GAAG,CAAC,EAAE;UACrBN,GAAG,CAACC,IAAI,CAAC,GAAGG,UAAU,CAACE,aAAa,CAACI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpD;MACF;MACA,OAAOV,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;IAER,IAAIH,MAAM,CAACc,IAAI,CAACf,qBAAqB,CAAC,CAACnB,MAAM,KAAK,CAAC,EAAE;MACnDxB,cAAc,CAAC,uDAAuD,CAAC;MACvEF,gBAAgB,CAAC,KAAK,CAAC;MACvB;IACF;;IAEA;IACA;IACA;;IAGA,MAAM6D,UAAU,GAAG;MACjB/B,WAAW,EAAEvC,UAAU,CAACuE,IAAI,CAAC,CAAC;MAAE;MAChC9B,WAAW,EAAEvC,cAAc;MAC3BwC,aAAa,EAAEY,qBAAqB;MAAE;MACtCX,SAAS,EAAErC;IACb,CAAC;IAED,IAAI;MACF,IAAIZ,SAAS,IAAIE,aAAa,EAAE;QAC9B;QACA,MAAMzB,yBAAyB,CAAC2B,eAAe,EAAEwE,UAAU,CAAC,CAAC,CAAC;QAC9DE,YAAY,CAAC,qCAAqC,EAAE,SAAS,CAAC;QAC9D;QACA,MAAMlG,4BAA4B,CAAC4B,cAAc,CAAC;QAClDsE,YAAY,CAAC,4CAA4CtE,cAAc,GAAG,EAAE,MAAM,CAAC;MACrF,CAAC,MAAM;QACL,MAAMhC,yBAAyB,CAACoG,UAAU,CAAC;QAC3CE,YAAY,CAAC,qCAAqC,EAAE,SAAS,CAAC;QAC9D;QACA,MAAMlG,4BAA4B,CAAC4B,cAAc,CAAC;QAClDsE,YAAY,CAAC,4CAA4CtE,cAAc,GAAG,EAAE,MAAM,CAAC;MACrF;MACA0C,iBAAiB,CAAC,CAAC;MACnBZ,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOH,GAAG,EAAE;MACZC,cAAc,CAACD,GAAG,EAAEnC,SAAS,GAAG,iCAAiC,GAAG,iCAAiC,EAAEiB,cAAc,CAAC;IACxH,CAAC,SAAS;MACRF,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAMgE,sBAAsB,GAAIC,QAAQ,IAAK;IAC3C3D,mBAAmB,CAAC2D,QAAQ,CAAC;IAC7BvD,cAAc,CAAC,EAAE,CAAC;IAClBN,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAM8D,uBAAuB,GAAGA,CAAA,KAAM;IACpCC,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC,CAAC,CAAC;IACvChE,mBAAmB,CAAC,KAAK,CAAC;IAC1BE,mBAAmB,CAAC,IAAI,CAAC;IACzBI,cAAc,CAAC,EAAE,CAAC;IAClBF,gBAAgB,CAAC,KAAK,CAAC;EACzB,CAAC;EAED,MAAM6D,kBAAkB,GAAG,MAAOC,gBAAgB,IAAK;IACrD9D,gBAAgB,CAAC,IAAI,CAAC;IACtBE,cAAc,CAAC,EAAE,CAAC;IAClB,IAAI;MACF,MAAM/C,yBAAyB,CAAC2G,gBAAgB,CAAC;MACjDP,YAAY,CAAC,qCAAqC,EAAE,SAAS,CAAC;MAC9DG,uBAAuB,CAAC,CAAC;MACzB3C,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOH,GAAG,EAAE;MACZC,cAAc,CAACD,GAAG,EAAE,iCAAiC,EAAEV,cAAc,CAAC;IACxE,CAAC,SAAS;MACRF,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAMa,cAAc,GAAGA,CAACD,GAAG,EAAEmD,cAAc,EAAEC,mBAAmB,GAAG5F,QAAQ,KAAK;IAAA,IAAA6F,aAAA,EAAAC,kBAAA,EAAAC,cAAA,EAAAC,mBAAA;IAC9ET,OAAO,CAACxF,KAAK,CAAC4F,cAAc,EAAEnD,GAAG,CAAC;IAClC,MAAMyD,OAAO,GAAG,EAAAJ,aAAA,GAAArD,GAAG,CAACF,QAAQ,cAAAuD,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAActD,IAAI,cAAAuD,kBAAA,uBAAlBA,kBAAA,CAAoB/F,KAAK,OAAAgG,cAAA,GAAIvD,GAAG,CAACF,QAAQ,cAAAyD,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcxD,IAAI,cAAAyD,mBAAA,uBAAlBA,mBAAA,CAAoBE,MAAM,KAAI1D,GAAG,CAACyD,OAAO,IAAIN,cAAc;IACxGC,mBAAmB,CAACK,OAAO,CAAC;IAC5B,IAAIL,mBAAmB,KAAK5F,QAAQ,EAAE;MACpCmF,YAAY,CAACc,OAAO,EAAE,OAAO,CAAC;IAChC;EACF,CAAC;EAED,MAAMd,YAAY,GAAGA,CAACc,OAAO,EAAEE,QAAQ,GAAG,SAAS,KAAK;IACtDjE,kBAAkB,CAAC+D,OAAO,CAAC;IAC3B7D,mBAAmB,CAAC+D,QAAQ,CAAC;IAC7BnE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMoE,mBAAmB,GAAGA,CAACvC,KAAK,EAAEwC,MAAM,KAAK;IAC7C,IAAIA,MAAM,KAAK,WAAW,EAAE;IAC5BrE,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAMsE,gBAAgB,GAAG3G,MAAM,CAAC4G,MAAM,CAACC,KAAK;IAAA,IAAAC,qBAAA,EAAAC,sBAAA;IAAA,OAC1CF,KAAK,CAAClD,SAAS,KACb,EAAAmD,qBAAA,GAAAD,KAAK,CAACG,sBAAsB,cAAAF,qBAAA,uBAA5BA,qBAAA,CAA8BG,QAAQ,CAAC/F,cAAc,CAACgG,WAAW,CAAC,CAAC,CAAC,OAAAH,sBAAA,GACpEF,KAAK,CAACG,sBAAsB,cAAAD,sBAAA,uBAA5BA,sBAAA,CAA8BE,QAAQ,CAAC,KAAK,CAAC,EAC9C;EAAA,CACH,CAAC;EAED,MAAM3C,qBAAqB,GAAG7H,KAAK,CAACI,OAAO,CAAC,MAAM;IAChD,OAAO0H,MAAM,CAACC,OAAO,CAACpD,YAAY,CAAC,CAChCwF,MAAM,CAAC,CAAC,GAAGhC,SAAS,CAAC,KAAK;MACzB,MAAMuC,SAAS,GAAGrC,UAAU,CAACF,SAAS,CAAC;MACvC,OAAO,CAACG,KAAK,CAACoC,SAAS,CAAC,IAAIA,SAAS,GAAG,CAAC;IAC3C,CAAC,CAAC,CACD1C,MAAM,CAAC,CAACC,GAAG,EAAE,CAACC,IAAI,EAAEC,SAAS,CAAC,KAAK;MAClCF,GAAG,CAACC,IAAI,CAAC,GAAGG,UAAU,CAACF,SAAS,CAAC;MACjC,OAAOF,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EACV,CAAC,EAAE,CAACtD,YAAY,CAAC,CAAC;EAElB,oBACE5B,OAAA,CAAC1C,GAAG;IAACsK,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBACjB9H,OAAA,CAAC1C,GAAG;MAACsK,EAAE,EAAE;QAAEG,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACzF9H,OAAA,CAACzC,UAAU;QAAC4K,OAAO,EAAC,IAAI;QAAAL,QAAA,EAAC;MAA6B;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACnEvI,OAAA,CAAC1C,GAAG;QAAAwK,QAAA,gBACD9H,OAAA,CAAC7B,OAAO;UAACqK,KAAK,EAAC,cAAc;UAAAV,QAAA,eAC1B9H,OAAA,CAAC9B,UAAU;YAACuK,OAAO,EAAEjF,aAAc;YAACkF,QAAQ,EAAEhI,SAAS,IAAIM,UAAW;YAAA8G,QAAA,eACpE9H,OAAA,CAACR,WAAW;cAAA4I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACXvI,OAAA,CAAC/B,MAAM;UACJkK,OAAO,EAAC,WAAW;UACnBQ,SAAS,eAAE3I,OAAA,CAACX,OAAO;YAAA+I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBE,OAAO,EAAE/E,sBAAuB;UAChCgF,QAAQ,EAAEhI,SAAS,IAAIM,UAAW;UAClC4G,EAAE,EAAE;YAAEgB,EAAE,EAAE;UAAE,CAAE;UAAAd,QAAA,EACf;QAED;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAEL3H,KAAK,iBAAIZ,OAAA,CAAChC,KAAK;MAACgJ,QAAQ,EAAC,OAAO;MAACY,EAAE,EAAE;QAAEM,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,EAAElH;IAAK;MAAAwH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,EAE/D7H,SAAS,gBACRV,OAAA,CAAC1C,GAAG;MAACsK,EAAE,EAAE;QAAEG,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEa,CAAC,EAAE;MAAE,CAAE;MAAAf,QAAA,eAAC9H,OAAA,CAACjC,gBAAgB;QAAAqK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,gBAExFvI,OAAA,CAACrC,cAAc;MAACmL,SAAS,EAAEhL,KAAM;MAAAgK,QAAA,eAC/B9H,OAAA,CAACxC,KAAK;QAACuL,IAAI,EAAC,OAAO;QAAAjB,QAAA,gBACjB9H,OAAA,CAACpC,SAAS;UAAAkK,QAAA,eACR9H,OAAA,CAACnC,QAAQ;YAAAiK,QAAA,gBACP9H,OAAA,CAACtC,SAAS;cAAAoK,QAAA,EAAC;YAAI;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC3BvI,OAAA,CAACtC,SAAS;cAAAoK,QAAA,EAAC;YAAW;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAClCvI,OAAA,CAACtC,SAAS;cAAAoK,QAAA,EAAC;YAAM;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC7BvI,OAAA,CAACtC,SAAS;cAAAoK,QAAA,EAAC;YAAa;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACpCvI,OAAA,CAACtC,SAAS;cAAAoK,QAAA,EAAC;YAAO;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZvI,OAAA,CAACvC,SAAS;UAAAqK,QAAA,GACPxH,OAAO,CAACqD,MAAM,KAAK,CAAC,IAAI,CAACjD,SAAS,iBACjCV,OAAA,CAACnC,QAAQ;YAAAiK,QAAA,eAAC9H,OAAA,CAACtC,SAAS;cAACsL,OAAO,EAAE,CAAE;cAACC,KAAK,EAAC,QAAQ;cAAAnB,QAAA,EAAC;YAAwB;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAC/F,EACAjI,OAAO,CAAC4I,GAAG,CAAErF,MAAM,IAAK;YACvBuC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAExC,MAAM,CAAC,CAAC,CAAC;YAC1C,MAAMsF,cAAc,GAAGtF,MAAM,CAACK,aAAa,GAAGa,MAAM,CAACC,OAAO,CAACnB,MAAM,CAACK,aAAa,CAAC,CAACgF,GAAG,CAAC,CAAC,CAAC/D,IAAI,EAAEE,MAAM,CAAC,KAAK;cACzG,MAAMgC,KAAK,GAAG7G,MAAM,CAAC4I,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKnE,IAAI,CAAC;cAC7C,OAAO;gBAAEnB,IAAI,EAAEqD,KAAK,GAAGA,KAAK,CAACrD,IAAI,GAAG,SAASmB,IAAI,CAACoE,SAAS,CAAC,CAAC,EAAC,CAAC,CAAC,KAAK;gBAAElE,MAAM,EAAEA;cAAO,CAAC;YACzF,CAAC,CAAC,GAAG,EAAE;YAEP,oBACErF,OAAA,CAACnC,QAAQ;cAAAiK,QAAA,GAAwB,GAAC,eAChC9H,OAAA,CAACtC,SAAS;gBAAAoK,QAAA,EAAEjE,MAAM,CAACE,WAAW,IAAIF,MAAM,CAACG;cAAI;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1DvI,OAAA,CAACtC,SAAS;gBAACkK,EAAE,EAAE;kBAAE4B,aAAa,EAAE;gBAAa,CAAE;gBAAA1B,QAAA,EAAEjE,MAAM,CAACI;cAAW;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChFvI,OAAA,CAACtC,SAAS;gBAAAoK,QAAA,eACN9H,OAAA,CAACd,IAAI;kBAACuK,KAAK,EAAE5F,MAAM,CAACM,SAAS,GAAG,QAAQ,GAAG,UAAW;kBAACuF,KAAK,EAAE7F,MAAM,CAACM,SAAS,GAAG,SAAS,GAAG,SAAU;kBAAC4E,IAAI,EAAC;gBAAO;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChH,CAAC,eACZvI,OAAA,CAACtC,SAAS;gBAAAoK,QAAA,EACPqB,cAAc,CAACxF,MAAM,GAAG,CAAC,GAAGwF,cAAc,CAACD,GAAG,CAACS,EAAE,iBAChD3J,OAAA,CAACd,IAAI;kBAAeuK,KAAK,EAAE,GAAGE,EAAE,CAAC3F,IAAI,KAAK4F,MAAM,CAACD,EAAE,CAACtE,MAAM,CAAC,CAACO,OAAO,CAAC,CAAC,CAAC,EAAG;kBAACmD,IAAI,EAAC,OAAO;kBAACnB,EAAE,EAAE;oBAAEiC,EAAE,EAAE,GAAG;oBAAE3B,EAAE,EAAE;kBAAI;gBAAE,GAArGyB,EAAE,CAAC3F,IAAI;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA+F,CAClH,CAAC,GAAG;cAAK;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACZvI,OAAA,CAACtC,SAAS;gBAAAoK,QAAA,gBACR9H,OAAA,CAAC7B,OAAO;kBAACqK,KAAK,EAAC,oBAAoB;kBAAAV,QAAA,eACjC9H,OAAA,CAAC9B,UAAU;oBAAC6K,IAAI,EAAC,OAAO;oBAACN,OAAO,EAAEA,CAAA,KAAM7E,oBAAoB,CAACC,MAAM,CAAE;oBAAC6E,QAAQ,EAAE1H,UAAW;oBAAA8G,QAAA,eACzF9H,OAAA,CAACV,QAAQ;sBAACwK,QAAQ,EAAC;oBAAO;sBAAA1B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eACVvI,OAAA,CAAC7B,OAAO;kBAACqK,KAAK,EAAC,sBAAsB;kBAAAV,QAAA,eACnC9H,OAAA,CAAC9B,UAAU;oBAAC6K,IAAI,EAAC,OAAO;oBAACN,OAAO,EAAEA,CAAA,KAAMxC,sBAAsB,CAACpC,MAAM,CAACC,SAAS,CAAE;oBAAC4E,QAAQ,EAAE1H,UAAW;oBAAC4G,EAAE,EAAE;sBAAEmC,aAAa,EAAE;oBAAO,CAAE;oBAAAjC,QAAA,GAAC,GAAC,eACtI9H,OAAA,CAACT,UAAU;sBAACuK,QAAQ,EAAC;oBAAO;sBAAA1B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA,GAtBC1E,MAAM,CAACC,SAAS;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuBrB,CAAC;UAEf,CAAC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CACjB,eAEDvI,OAAA,CAAC5B,MAAM;MAAC4L,IAAI,EAAEhJ,UAAW;MAACiJ,OAAO,EAAE7F,iBAAkB;MAAC8F,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAArC,QAAA,gBAC3E9H,OAAA,CAACxB,WAAW;QAAAsJ,QAAA,EAAE5G,SAAS,GAAG,oBAAoB,GAAG;MAA0B;QAAAkH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eAC1FvI,OAAA,CAAC1B,aAAa;QAAAwJ,QAAA,GACX5F,WAAW,iBAAIlC,OAAA,CAAChC,KAAK;UAACgJ,QAAQ,EAAC,OAAO;UAACY,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAJ,QAAA,EAAE5F;QAAW;UAAAkG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC5EvI,OAAA,CAACvB,SAAS;UACR2L,SAAS;UACTC,MAAM,EAAC,OAAO;UACdf,EAAE,EAAC,aAAa;UAChBG,KAAK,EAAC,oBAAoB;UAC1Ba,IAAI,EAAC,MAAM;UACXH,SAAS;UACThC,OAAO,EAAC,UAAU;UAClB3D,KAAK,EAAEhD,UAAW;UAClB+I,QAAQ,EAAGC,CAAC,IAAK/I,aAAa,CAAC+I,CAAC,CAAC7F,MAAM,CAACH,KAAK,CAAE;UAC/CkE,QAAQ,EAAE1G,aAAc;UACxB4F,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UACduC,QAAQ;QAAA;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFvI,OAAA,CAACpB,WAAW;UAACuL,SAAS;UAACE,MAAM,EAAC,OAAO;UAAClC,OAAO,EAAC,UAAU;UAACO,QAAQ,EAAE1G,aAAa,IAAId,SAAU;UAAC0G,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAACuC,QAAQ;UAAA3C,QAAA,gBACnH9H,OAAA,CAACnB,UAAU;YAACyK,EAAE,EAAC,uBAAuB;YAAAxB,QAAA,EAAC;UAAW;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/DvI,OAAA,CAACtB,MAAM;YACJgM,OAAO,EAAC,uBAAuB;YAC/BpB,EAAE,EAAC,iBAAiB;YACpB9E,KAAK,EAAE9C,cAAe;YACtB+H,KAAK,EAAC,aAAa;YACnBc,QAAQ,EAAGC,CAAC,IAAK;cACb7I,iBAAiB,CAAC6I,CAAC,CAAC7F,MAAM,CAACH,KAAK,CAAC;cACjC3C,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,CAAE;YAAAiG,QAAA,EAEF3H,aAAa,CAAC+I,GAAG,CAACyB,GAAG,iBAAI3K,OAAA,CAACrB,QAAQ;cAAW6F,KAAK,EAAEmG,GAAI;cAAC/C,EAAE,EAAE;gBAAE4B,aAAa,EAAE;cAAa,CAAE;cAAA1B,QAAA,EAAE6C;YAAG,GAA1DA,GAAG;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAkE,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1G,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEdvI,OAAA,CAACZ,gBAAgB;UACfwL,OAAO,eAAE5K,OAAA,CAACb,MAAM;YAACyF,OAAO,EAAE9C,QAAS;YAACyI,QAAQ,EAAE9F,oBAAqB;YAACiE,QAAQ,EAAE1G;UAAc;YAAAoG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAChGkB,KAAK,EAAC,sBAAsB;UAC5B7B,EAAE,EAAE;YAAEM,EAAE,EAAE,CAAC;YAAEL,EAAE,EAAC;UAAE;QAAE;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,EAED7G,cAAc,iBACb1B,OAAA,CAAAE,SAAA;UAAA4H,QAAA,gBACE9H,OAAA,CAACzC,UAAU;YAAC4K,OAAO,EAAC,WAAW;YAACP,EAAE,EAAE;cAAEC,EAAE,EAAE,CAAC;cAAEK,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,EAAC;UAAyB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EAC3FpB,gBAAgB,CAACxD,MAAM,KAAK,CAAC,IAAInD,MAAM,CAACmD,MAAM,GAAG,CAAC,iBAChD3D,OAAA,CAAChC,KAAK;YAACgJ,QAAQ,EAAC,SAAS;YAACY,EAAE,EAAE;cAAEM,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,GAAC,oDAAkD,EAACpG,cAAc,EAAC,IAAE;UAAA;YAAA0G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACtH,EACApB,gBAAgB,CAACxD,MAAM,KAAK,CAAC,IAAInD,MAAM,CAACmD,MAAM,KAAK,CAAC,IAAI,CAACjD,SAAS,iBAChEV,OAAA,CAAChC,KAAK;YAACgJ,QAAQ,EAAC,OAAO;YAACY,EAAE,EAAE;cAAEM,EAAE,EAAE;YAAE,CAAE;YAAAJ,QAAA,EAAC;UAA6C;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAC7F,eACDvI,OAAA,CAAClB,IAAI;YAAC+L,KAAK;YAACjD,EAAE,EAAE;cAACkD,SAAS,EAAE,GAAG;cAAEC,QAAQ,EAAE;YAAM,CAAE;YAAAjD,QAAA,EAChDX,gBAAgB,CAAC+B,GAAG,CAAC7B,KAAK;YAAA;YAAM;YAC/BrH,OAAA,CAACjB,QAAQ;cAAgBiM,cAAc;cAACpD,EAAE,EAAE;gBAAEM,EAAE,EAAE,GAAG;gBAAEH,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE;cAAgB,CAAE;cAAAF,QAAA,gBACvG9H,OAAA,CAAChB,YAAY;gBAACiM,OAAO,EAAE5D,KAAK,CAAC6D,UAAW;gBAACC,SAAS,EAAE9D,KAAK,CAAC+D,UAAW;gBAACxD,EAAE,EAAE;kBAAEiC,EAAE,EAAE,CAAC;kBAAEwB,QAAQ,EAAE;gBAAC;cAAE;gBAAAjD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,eAClGvI,OAAA,CAACvB,SAAS;gBACPgL,KAAK,EAAC,QAAQ;gBACdH,EAAE,EAAE,UAAUjC,KAAK,CAACiE,QAAQ,EAAG;gBAC/B9G,KAAK,EAAE5C,YAAY,CAACyF,KAAK,CAACiE,QAAQ,CAAC,IAAI,EAAG;gBAC1Cf,QAAQ,EAAGC,CAAC,IAAKlG,kBAAkB,CAAC+C,KAAK,CAACiE,QAAQ,EAAEd,CAAC,CAAC7F,MAAM,CAACH,KAAK,CAAE;gBACpE8F,IAAI,EAAC,QAAQ;gBACbiB,UAAU,EAAE;kBAAEC,IAAI,EAAE,MAAM;kBAAE7F,GAAG,EAAE,GAAG;kBAAED,GAAG,EAAE;gBAAI,CAAE;gBACjDyC,OAAO,EAAC,UAAU;gBAClBY,IAAI,EAAC,OAAO;gBACZnB,EAAE,EAAE;kBAAE6D,KAAK,EAAE;gBAAQ,CAAE;gBACvB/C,QAAQ,EAAE1G;cAAc;gBAAAoG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA,GAbUlB,KAAK,CAACiC,EAAE;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAcb,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,eACP,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEY,CAAC,eAChBvI,OAAA,CAAC3B,aAAa;QAAAyJ,QAAA,gBACZ9H,OAAA,CAAC/B,MAAM;UAACwK,OAAO,EAAErE,iBAAkB;UAACsE,QAAQ,EAAE1G,aAAc;UAAA8F,QAAA,EAAC;QAAM;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC5EvI,OAAA,CAAC/B,MAAM;UACJwK,OAAO,EAAE5D,kBAAmB;UAC5B6D,QAAQ,EAAE1G,aAAa,IAAI,CAACR,UAAU,IAAI,CAACE,cAAc,IAAKyF,gBAAgB,CAACxD,MAAM,GAAG,CAAC,IAAIoB,MAAM,CAACc,IAAI,CAACf,qBAAqB,CAAC,CAACnB,MAAM,KAAK,CAAE,IAAKzC,SAAS,IAAI,CAACI,eAAiB;UACjL6G,OAAO,EAAC,WAAW;UAAAL,QAAA,EAEnB9F,aAAa,gBAAGhC,OAAA,CAACjC,gBAAgB;YAACgL,IAAI,EAAE;UAAG;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAIrH,SAAS,GAAG,cAAc,GAAG;QAAS;UAAAkH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAETvI,OAAA,CAAC5B,MAAM;MAAC4L,IAAI,EAAE5H,gBAAiB;MAAC6H,OAAO,EAAE9D,uBAAwB;MAAA2B,QAAA,gBAC/D9H,OAAA,CAACxB,WAAW;QAAAsJ,QAAA,EAAC;MAAgB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC3CvI,OAAA,CAAC1B,aAAa;QAAAwJ,QAAA,GACXpF,WAAW,iBAAI1C,OAAA,CAAChC,KAAK;UAACgJ,QAAQ,EAAC,OAAO;UAACY,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAJ,QAAA,EAAEpF;QAAW;UAAA0F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC5EvI,OAAA,CAACzB,iBAAiB;UAAAuJ,QAAA,EAAC;QAEnB;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAChBvI,OAAA,CAAC3B,aAAa;QAAAyJ,QAAA,gBACZ9H,OAAA,CAAC/B,MAAM;UAACwK,OAAO,EAAEtC,uBAAwB;UAACuC,QAAQ,EAAElG,aAAc;UAAAsF,QAAA,EAAC;QAAM;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClFvI,OAAA,CAAC/B,MAAM;UAACwK,OAAO,EAAEA,CAAA,KAAMnC,kBAAkB,CAAChE,gBAAgB,CAAE;UAACoH,KAAK,EAAC,OAAO;UAACU,SAAS;UAAC1B,QAAQ,EAAElG,aAAc;UAAAsF,QAAA,GAAC,GAAC,EAC5GtF,aAAa,gBAAGxC,OAAA,CAACjC,gBAAgB;YAACgL,IAAI,EAAE;UAAG;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG,QAAQ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAETvI,OAAA,CAACf,QAAQ;MAAC+K,IAAI,EAAEpH,YAAa;MAAC8I,gBAAgB,EAAE,IAAK;MAACzB,OAAO,EAAEhD,mBAAoB;MAAC0E,YAAY,EAAE;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAA/D,QAAA,eAC7I9H,OAAA,CAAChC,KAAK;QAACiM,OAAO,EAAEhD,mBAAoB;QAACD,QAAQ,EAAEhE,gBAAiB;QAAC4E,EAAE,EAAE;UAAE6D,KAAK,EAAE;QAAO,CAAE;QAAA3D,QAAA,EACpFhF;MAAe;QAAAsF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAAClI,EAAA,CA9aID,oBAAoB;AAAA0L,EAAA,GAApB1L,oBAAoB;AAgb1B,eAAeA,oBAAoB;AAAC,IAAA0L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}