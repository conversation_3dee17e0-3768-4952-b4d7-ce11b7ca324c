{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '/api'; // Use environment variable or default\n// const API_BASE_URL = 'http://localhost:5000/api';\n\nconst apiClient = axios.create({\n  baseURL: API_BASE_URL\n});\n\n// Add a response interceptor\napiClient.interceptors.response.use(response => {\n  // Check for backend-specific success messages\n  if (response.data && response.data.message) {\n    // You might want to attach this to the response object for components to use\n    // e.g., response.successMessage = response.data.message;\n  } else if (response.data && response.data.detail && response.status >= 200 && response.status < 300) {\n    // Fallback for success messages in 'detail'\n    // response.successMessage = response.data.detail;\n  }\n  // For successful image uploads, the structure is different (response.data.success, response.data.errors)\n  // This will be handled in the ImageUpload.js component as per instructions.\n  return response;\n}, error => {\n  // Check for backend-specific error messages\n  if (error.response && error.response.data) {\n    if (error.response.data.error) {\n      error.message = error.response.data.error;\n    } else if (error.response.data.detail) {\n      // Fallback for error messages in 'detail'\n      error.message = error.response.data.detail;\n    }\n    // Attach the structured errors if available (e.g., for form validation)\n    if (error.response.data.errors) {\n      error.errors = error.response.data.errors;\n    }\n  }\n  // If no specific message, Axios error.message or a default will be used\n  return Promise.reject(error);\n});\n\n// --- Data Management ---\n\n// Corresponds to POST /api/data/images\nexport const uploadImages = formData => {\n  // Renamed from uploadImage, expects FormData\n  // FormData MUST be constructed by the caller to contain 'image_type', 'files[]',\n  // and conditionally 'ip_category' (if image_type is 'product') and 'ip_owner' (optional).\n  return apiClient.post(`/data/images`, formData, {\n    headers: {\n      'Content-Type': 'multipart/form-data'\n    }\n  });\n};\n\n// Corresponds to GET /api/data/images\nexport const listImages = params => {\n  // params = { ip_category, image_type, missing_ip_owner (boolean), page, per_page }\n  const queryParams = {\n    ...params\n  };\n  if (typeof queryParams.missing_ip_owner === 'boolean') {\n    queryParams.missing_ip_owner = String(queryParams.missing_ip_owner);\n  }\n  return apiClient.get(`/data/images`, {\n    params: queryParams\n  });\n};\n\n// Corresponds to GET /api/data/images/file/<uuid:image_id>\nexport const getImageFile = imageId => {\n  // Renamed from getImage\n  // Use axios responseType 'blob' to handle image data\n  return apiClient.get(`/data/images/file/${imageId}`, {\n    responseType: 'blob'\n  });\n};\n\n// Corresponds to DELETE /api/data/images/<uuid:image_id>\nexport const deleteImage = imageId => {\n  // Simplified signature\n  return apiClient.delete(`/data/images/${imageId}`);\n};\n\n// Corresponds to PUT /api/data/images/<uuid:image_id>/ip_owner\nexport const updateImageIpOwner = (imageId, ipOwner) => {\n  return apiClient.put(`/data/images/${imageId}/ip_owner`, {\n    ip_owner: ipOwner\n  });\n};\n\n// --- Model Management ---\n\n// Corresponds to GET /api/models\nexport const listModels = params => {\n  // params = { ip_category } // Backend does not support ip_category for this endpoint.\n  const queryParams = {\n    ...params\n  };\n  delete queryParams.ip_category; // Remove ip_category as per discrepancy\n  return apiClient.get(`/models`, {\n    params: queryParams\n  });\n};\n\n// Corresponds to POST /api/models/refresh\nexport const refreshModels = () => {\n  return apiClient.post(`/models/refresh`);\n};\n\n// --- Feature Computation ---\n\n// Corresponds to POST /api/tasks/compute-features/<ip_category>\nexport const computeFeatures = ipCategory => {\n  return apiClient.post(`/tasks/compute-features/${ipCategory}`);\n};\n\n// --- Combined Scores ---\n\n// Corresponds to GET /api/combined-scores\nexport const getCombinedScores = params => {\n  // params = { ip_category, is_active (boolean) }\n  const queryParams = {\n    ...params\n  };\n  if (typeof queryParams.is_active === 'boolean') {\n    queryParams.is_active = String(queryParams.is_active);\n  }\n  return apiClient.get(`/combined-scores`, {\n    params: queryParams\n  });\n};\n\n// Corresponds to PUT /api/combined-scores/<uuid:config_id>\nexport const updateCombinedScoreConfig = (configId, configData) => {\n  // configData is expected to have:\n  // { config_name: \"string\", model_weights: { \"model_uuid_1\": 0.5, \"model_uuid_2\": 0.5 }, is_active: boolean }\n  // ip_category should NOT be sent.\n  const payload = {\n    ...configData\n  };\n  delete payload.ip_category; // Ensure ip_category is not sent\n\n  // Ensure model_weights keys are UUIDs (caller should handle this mapping)\n  // Ensure config_name is used (caller should provide it as config_name)\n  return apiClient.put(`/combined-scores/${configId}`, payload);\n};\n\n// Corresponds to POST /api/combined-scores\nexport const createCombinedScoreConfig = configData => {\n  // configData is expected to have:\n  // { config_name: \"string\", ip_category: \"string\", model_weights: { \"model_uuid_1\": 0.5, ... }, is_active: boolean }\n  // Ensure model_weights keys are UUIDs (caller should handle this mapping)\n  // Ensure config_name is used (caller should provide it as config_name)\n  return apiClient.post(`/combined-scores`, configData);\n};\n\n// Corresponds to DELETE /api/combined-scores/<uuid:config_id>\nexport const deleteCombinedScoreConfig = configId => {\n  console.log('Attempting to delete combined score config with ID:', configId); // Added log\n  const url = `/combined-scores/${configId}`;\n  console.log('DELETE request URL:', url); // Added log\n  return apiClient.delete(url);\n};\n\n// --- Results ---\n\n// Corresponds to GET /api/results/by-model\nexport const getResultsByModel = params => {\n  // params = { model_id, ip_category, page, per_page, limit }\n  return apiClient.get(`/results/by-model`, {\n    params\n  });\n};\n\n// Corresponds to GET /api/results/by-product/<uuid:product_image_id>\nexport const getResultsByProduct = (productImageId, params) => {\n  // params = { limit }\n  return apiClient.get(`/results/by-product/${productImageId}`, {\n    params\n  });\n};\n\n// Corresponds to POST /api/data/ground_truth\nexport const addGroundTruth = (productImageId, correctIpImageId) => {\n  return apiClient.post(`/data/ground_truth`, {\n    product_image_id: productImageId,\n    correct_ip_image_id: correctIpImageId\n  });\n};\n\n// Corresponds to DELETE /api/data/ground_truth\nexport const removeGroundTruth = (productImageId, correctIpImageId) => {\n  return apiClient.delete(`/data/ground_truth`, {\n    data: {\n      // DELETE requests need data payload for body\n      product_image_id: productImageId,\n      correct_ip_image_id: correctIpImageId\n    }\n  });\n};\n\n// --- Dashboard ---\n\n// Corresponds to GET /api/dashboard/performance-summary\nexport const getPerformanceSummary = ipCategory => {\n  return apiClient.get(`/dashboard/performance-summary`, {\n    params: {\n      ip_category: ipCategory\n    }\n  });\n};\n\n// Corresponds to GET /api/dashboard/score-distribution\nexport const getScoreDistribution = (modelId, ipCategory) => {\n  return apiClient.get(`/dashboard/score-distribution`, {\n    params: {\n      model_id: modelId,\n      ip_category: ipCategory\n    }\n  });\n};\n\n// Corresponds to GET /api/dashboard/confusion-matrix\nexport const getConfusionMatrix = (modelId, ipCategory, threshold) => {\n  return apiClient.get(`/dashboard/confusion-matrix`, {\n    params: {\n      model_id: modelId,\n      ip_category: ipCategory,\n      threshold: threshold\n    }\n  });\n};\n\n// --- Patent Visualization Platform ---\n\n// Corresponds to GET /api/v1/patents/dashboard/statistics\nexport const getPatentDashboardStatistics = (refresh = false) => {\n  return apiClient.get(`/v1/patents/dashboard/statistics`, {\n    params: {\n      refresh\n    }\n  });\n};\n\n// Corresponds to GET /api/v1/patents/explore\nexport const getPatentsForExploration = filters => {\n  // Filters object includes:\n  // document_id_search, patent_title_search, abstract_search,\n  // date_published_start, date_published_end, patent_types (array),\n  // tro_status, inventors_search, assignee_search, applicant_search,\n  // uspc_class_search, loc_code_search, cpc_class_search,\n  // page, per_page, sort_by, sort_dir, selected_columns (array)\n\n  const params = {\n    ...filters\n  };\n  if (Array.isArray(params.patent_types)) {\n    params.patent_types = params.patent_types.join(',');\n  }\n  if (Array.isArray(params.selected_columns)) {\n    params.columns = params.selected_columns.join(','); // API expects 'columns'\n    delete params.selected_columns;\n  }\n  // Ensure page and per_page are numbers\n  if (params.page) params.page = Number(params.page);\n  if (params.per_page) params.per_page = Number(params.per_page);\n  return apiClient.get(`/v1/patents/explore`, {\n    params\n  });\n};\n\n// Corresponds to GET /api/v1/patents/explore/&lt;patent_id&gt;\nexport const getPatentDetails = patentId => {\n  return apiClient.get(`/v1/patents/explore/${patentId}`);\n};\n\n// Corresponds to GET /api/v1/patents/explore/&lt;patent_id&gt;/images\nexport const getPatentImagesInfo = patentId => {\n  return apiClient.get(`/v1/patents/explore/${patentId}/images`);\n};\n// --- Qdrant Management ---\n\nexport const getQdrantCollections = async () => {\n  try {\n    const response = await apiClient.get(`/qdrant/collections`);\n    return response.data;\n  } catch (error) {\n    console.error(\"Error fetching Qdrant collections:\", error);\n    throw error;\n  }\n};\nexport const deleteQdrantCollection = async collectionName => {\n  try {\n    const encodedCollectionName = encodeURIComponent(collectionName);\n    const response = await apiClient.delete(`/qdrant/collections/${encodedCollectionName}`);\n    return response.data;\n  } catch (error) {\n    console.error(`Error deleting Qdrant collection ${collectionName}:`, error);\n    throw error;\n  }\n};\n\n// --- Task Status ---\n\n// Corresponds to GET /api/tasks/status/<task_id>\nexport const getTaskStatus = taskId => {\n  return apiClient.get(`/tasks/status/${taskId}`);\n};\n\n// Corresponds to POST /api/tasks/compute-combined-scores/<ip_category>\nexport const triggerComputeCombinedScores = ipCategory => {\n  return apiClient.post(`/tasks/compute-combined-scores/${ipCategory}`);\n};", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_BASE_URL", "apiClient", "create", "baseURL", "interceptors", "response", "use", "data", "message", "detail", "status", "error", "errors", "Promise", "reject", "uploadImages", "formData", "post", "headers", "listImages", "params", "queryParams", "missing_ip_owner", "String", "get", "getImageFile", "imageId", "responseType", "deleteImage", "delete", "updateImageIpOwner", "ip<PERSON><PERSON><PERSON>", "put", "ip_owner", "listModels", "ip_category", "refreshModels", "computeFeatures", "ipCategory", "getCombinedScores", "is_active", "updateCombinedScoreConfig", "configId", "configData", "payload", "createCombinedScoreConfig", "deleteCombinedScoreConfig", "console", "log", "url", "getResultsByModel", "getResultsByProduct", "productImageId", "addGroundTruth", "correctIpImageId", "product_image_id", "correct_ip_image_id", "removeGroundTruth", "getPerformanceSummary", "getScoreDistribution", "modelId", "model_id", "getConfusionMatrix", "threshold", "getPatentDashboardStatistics", "refresh", "getPatentsForExploration", "filters", "Array", "isArray", "patent_types", "join", "selected_columns", "columns", "page", "Number", "per_page", "getPatentDetails", "patentId", "getPatentImagesInfo", "getQdrantCollections", "deleteQdrantCollection", "collectionName", "encodedCollectionName", "encodeURIComponent", "getTaskStatus", "taskId", "triggerComputeCombinedScores"], "sources": ["D:/Documents/Programing/TRO/ModelTestsWorkbench/frontend/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\r\n\r\nconst API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '/api'; // Use environment variable or default\r\n// const API_BASE_URL = 'http://localhost:5000/api';\r\n\r\nconst apiClient = axios.create({\r\n  baseURL: API_BASE_URL,\r\n});\r\n\r\n// Add a response interceptor\r\napiClient.interceptors.response.use(\r\n  (response) => {\r\n    // Check for backend-specific success messages\r\n    if (response.data && response.data.message) {\r\n      // You might want to attach this to the response object for components to use\r\n      // e.g., response.successMessage = response.data.message;\r\n    } else if (response.data && response.data.detail && response.status >= 200 && response.status < 300) {\r\n      // Fallback for success messages in 'detail'\r\n      // response.successMessage = response.data.detail;\r\n    }\r\n    // For successful image uploads, the structure is different (response.data.success, response.data.errors)\r\n    // This will be handled in the ImageUpload.js component as per instructions.\r\n    return response;\r\n  },\r\n  (error) => {\r\n    // Check for backend-specific error messages\r\n    if (error.response && error.response.data) {\r\n      if (error.response.data.error) {\r\n        error.message = error.response.data.error;\r\n      } else if (error.response.data.detail) {\r\n        // Fallback for error messages in 'detail'\r\n        error.message = error.response.data.detail;\r\n      }\r\n      // Attach the structured errors if available (e.g., for form validation)\r\n      if (error.response.data.errors) {\r\n        error.errors = error.response.data.errors;\r\n      }\r\n    }\r\n    // If no specific message, Axios error.message or a default will be used\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// --- Data Management ---\r\n\r\n// Corresponds to POST /api/data/images\r\nexport const uploadImages = (formData) => { // Renamed from uploadImage, expects FormData\r\n  // FormData MUST be constructed by the caller to contain 'image_type', 'files[]',\r\n  // and conditionally 'ip_category' (if image_type is 'product') and 'ip_owner' (optional).\r\n  return apiClient.post(`/data/images`, formData, {\r\n    headers: {\r\n      'Content-Type': 'multipart/form-data',\r\n    },\r\n  });\r\n};\r\n\r\n// Corresponds to GET /api/data/images\r\nexport const listImages = (params) => {\r\n  // params = { ip_category, image_type, missing_ip_owner (boolean), page, per_page }\r\n  const queryParams = { ...params };\r\n  if (typeof queryParams.missing_ip_owner === 'boolean') {\r\n    queryParams.missing_ip_owner = String(queryParams.missing_ip_owner);\r\n  }\r\n  return apiClient.get(`/data/images`, { params: queryParams });\r\n};\r\n\r\n// Corresponds to GET /api/data/images/file/<uuid:image_id>\r\nexport const getImageFile = (imageId) => { // Renamed from getImage\r\n    // Use axios responseType 'blob' to handle image data\r\n    return apiClient.get(`/data/images/file/${imageId}`, { responseType: 'blob' });\r\n};\r\n\r\n// Corresponds to DELETE /api/data/images/<uuid:image_id>\r\nexport const deleteImage = (imageId) => { // Simplified signature\r\n  return apiClient.delete(`/data/images/${imageId}`);\r\n};\r\n\r\n// Corresponds to PUT /api/data/images/<uuid:image_id>/ip_owner\r\nexport const updateImageIpOwner = (imageId, ipOwner) => {\r\n  return apiClient.put(`/data/images/${imageId}/ip_owner`, { ip_owner: ipOwner });\r\n};\r\n\r\n// --- Model Management ---\r\n\r\n// Corresponds to GET /api/models\r\nexport const listModels = (params) => {\r\n  // params = { ip_category } // Backend does not support ip_category for this endpoint.\r\n  const queryParams = { ...params };\r\n  delete queryParams.ip_category; // Remove ip_category as per discrepancy\r\n  return apiClient.get(`/models`, { params: queryParams });\r\n};\r\n\r\n// Corresponds to POST /api/models/refresh\r\nexport const refreshModels = () => {\r\n  return apiClient.post(`/models/refresh`);\r\n};\r\n\r\n// --- Feature Computation ---\r\n\r\n// Corresponds to POST /api/tasks/compute-features/<ip_category>\r\nexport const computeFeatures = (ipCategory) => {\r\n  return apiClient.post(`/tasks/compute-features/${ipCategory}`);\r\n};\r\n\r\n// --- Combined Scores ---\r\n\r\n// Corresponds to GET /api/combined-scores\r\nexport const getCombinedScores = (params) => {\r\n  // params = { ip_category, is_active (boolean) }\r\n  const queryParams = { ...params };\r\n  if (typeof queryParams.is_active === 'boolean') {\r\n    queryParams.is_active = String(queryParams.is_active);\r\n  }\r\n  return apiClient.get(`/combined-scores`, { params: queryParams });\r\n};\r\n\r\n// Corresponds to PUT /api/combined-scores/<uuid:config_id>\r\nexport const updateCombinedScoreConfig = (configId, configData) => {\r\n    // configData is expected to have:\r\n    // { config_name: \"string\", model_weights: { \"model_uuid_1\": 0.5, \"model_uuid_2\": 0.5 }, is_active: boolean }\r\n    // ip_category should NOT be sent.\r\n    const payload = { ...configData };\r\n    delete payload.ip_category; // Ensure ip_category is not sent\r\n\r\n    // Ensure model_weights keys are UUIDs (caller should handle this mapping)\r\n    // Ensure config_name is used (caller should provide it as config_name)\r\n    return apiClient.put(`/combined-scores/${configId}`, payload);\r\n};\r\n\r\n// Corresponds to POST /api/combined-scores\r\nexport const createCombinedScoreConfig = (configData) => {\r\n  // configData is expected to have:\r\n  // { config_name: \"string\", ip_category: \"string\", model_weights: { \"model_uuid_1\": 0.5, ... }, is_active: boolean }\r\n  // Ensure model_weights keys are UUIDs (caller should handle this mapping)\r\n  // Ensure config_name is used (caller should provide it as config_name)\r\n  return apiClient.post(`/combined-scores`, configData);\r\n};\r\n\r\n// Corresponds to DELETE /api/combined-scores/<uuid:config_id>\r\nexport const deleteCombinedScoreConfig = (configId) => {\r\n  console.log('Attempting to delete combined score config with ID:', configId); // Added log\r\n  const url = `/combined-scores/${configId}`;\r\n  console.log('DELETE request URL:', url); // Added log\r\n  return apiClient.delete(url);\r\n};\r\n\r\n// --- Results ---\r\n\r\n// Corresponds to GET /api/results/by-model\r\nexport const getResultsByModel = (params) => {\r\n    // params = { model_id, ip_category, page, per_page, limit }\r\n    return apiClient.get(`/results/by-model`, { params });\r\n};\r\n\r\n// Corresponds to GET /api/results/by-product/<uuid:product_image_id>\r\nexport const getResultsByProduct = (productImageId, params) => {\r\n    // params = { limit }\r\n    return apiClient.get(`/results/by-product/${productImageId}`, { params });\r\n};\r\n\r\n// Corresponds to POST /api/data/ground_truth\r\nexport const addGroundTruth = (productImageId, correctIpImageId) => {\r\n    return apiClient.post(`/data/ground_truth`, {\r\n        product_image_id: productImageId,\r\n        correct_ip_image_id: correctIpImageId\r\n    });\r\n};\r\n\r\n// Corresponds to DELETE /api/data/ground_truth\r\nexport const removeGroundTruth = (productImageId, correctIpImageId) => {\r\n    return apiClient.delete(`/data/ground_truth`, {\r\n        data: { // DELETE requests need data payload for body\r\n            product_image_id: productImageId,\r\n            correct_ip_image_id: correctIpImageId\r\n        }\r\n    });\r\n};\r\n\r\n// --- Dashboard ---\r\n\r\n// Corresponds to GET /api/dashboard/performance-summary\r\nexport const getPerformanceSummary = (ipCategory) => {\r\n    return apiClient.get(`/dashboard/performance-summary`, { params: { ip_category: ipCategory } });\r\n};\r\n\r\n// Corresponds to GET /api/dashboard/score-distribution\r\nexport const getScoreDistribution = (modelId, ipCategory) => {\r\n    return apiClient.get(`/dashboard/score-distribution`, { params: { model_id: modelId, ip_category: ipCategory } });\r\n};\r\n\r\n// Corresponds to GET /api/dashboard/confusion-matrix\r\nexport const getConfusionMatrix = (modelId, ipCategory, threshold) => {\r\n    return apiClient.get(`/dashboard/confusion-matrix`, { params: { model_id: modelId, ip_category: ipCategory, threshold: threshold } });\r\n};\r\n\r\n// --- Patent Visualization Platform ---\r\n\r\n// Corresponds to GET /api/v1/patents/dashboard/statistics\r\nexport const getPatentDashboardStatistics = (refresh = false) => {\r\n  return apiClient.get(`/v1/patents/dashboard/statistics`, { params: { refresh } });\r\n};\r\n\r\n// Corresponds to GET /api/v1/patents/explore\r\nexport const getPatentsForExploration = (filters) => {\r\n  // Filters object includes:\r\n  // document_id_search, patent_title_search, abstract_search,\r\n  // date_published_start, date_published_end, patent_types (array),\r\n  // tro_status, inventors_search, assignee_search, applicant_search,\r\n  // uspc_class_search, loc_code_search, cpc_class_search,\r\n  // page, per_page, sort_by, sort_dir, selected_columns (array)\r\n\r\n  const params = { ...filters };\r\n  if (Array.isArray(params.patent_types)) {\r\n    params.patent_types = params.patent_types.join(',');\r\n  }\r\n  if (Array.isArray(params.selected_columns)) {\r\n    params.columns = params.selected_columns.join(','); // API expects 'columns'\r\n    delete params.selected_columns;\r\n  }\r\n  // Ensure page and per_page are numbers\r\n  if (params.page) params.page = Number(params.page);\r\n  if (params.per_page) params.per_page = Number(params.per_page);\r\n\r\n  return apiClient.get(`/v1/patents/explore`, { params });\r\n};\r\n\r\n// Corresponds to GET /api/v1/patents/explore/&lt;patent_id&gt;\r\nexport const getPatentDetails = (patentId) => {\r\n  return apiClient.get(`/v1/patents/explore/${patentId}`);\r\n};\r\n\r\n// Corresponds to GET /api/v1/patents/explore/&lt;patent_id&gt;/images\r\nexport const getPatentImagesInfo = (patentId) => {\r\n  return apiClient.get(`/v1/patents/explore/${patentId}/images`);\r\n};\r\n// --- Qdrant Management ---\r\n\r\nexport const getQdrantCollections = async () => {\r\n  try {\r\n    const response = await apiClient.get(`/qdrant/collections`);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error(\"Error fetching Qdrant collections:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const deleteQdrantCollection = async (collectionName) => {\r\n  try {\r\n    const encodedCollectionName = encodeURIComponent(collectionName);\r\n    const response = await apiClient.delete(`/qdrant/collections/${encodedCollectionName}`);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error(`Error deleting Qdrant collection ${collectionName}:`, error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// --- Task Status ---\r\n\r\n// Corresponds to GET /api/tasks/status/<task_id>\r\nexport const getTaskStatus = (taskId) => {\r\n    return apiClient.get(`/tasks/status/${taskId}`);\r\n};\r\n\r\n// Corresponds to POST /api/tasks/compute-combined-scores/<ip_category>\r\nexport const triggerComputeCombinedScores = (ipCategory) => {\r\n  return apiClient.post(`/tasks/compute-combined-scores/${ipCategory}`);\r\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,MAAM,CAAC,CAAC;AACnE;;AAEA,MAAMC,SAAS,GAAGL,KAAK,CAACM,MAAM,CAAC;EAC7BC,OAAO,EAAEN;AACX,CAAC,CAAC;;AAEF;AACAI,SAAS,CAACG,YAAY,CAACC,QAAQ,CAACC,GAAG,CAChCD,QAAQ,IAAK;EACZ;EACA,IAAIA,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;IAC1C;IACA;EAAA,CACD,MAAM,IAAIH,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACE,MAAM,IAAIJ,QAAQ,CAACK,MAAM,IAAI,GAAG,IAAIL,QAAQ,CAACK,MAAM,GAAG,GAAG,EAAE;IACnG;IACA;EAAA;EAEF;EACA;EACA,OAAOL,QAAQ;AACjB,CAAC,EACAM,KAAK,IAAK;EACT;EACA,IAAIA,KAAK,CAACN,QAAQ,IAAIM,KAAK,CAACN,QAAQ,CAACE,IAAI,EAAE;IACzC,IAAII,KAAK,CAACN,QAAQ,CAACE,IAAI,CAACI,KAAK,EAAE;MAC7BA,KAAK,CAACH,OAAO,GAAGG,KAAK,CAACN,QAAQ,CAACE,IAAI,CAACI,KAAK;IAC3C,CAAC,MAAM,IAAIA,KAAK,CAACN,QAAQ,CAACE,IAAI,CAACE,MAAM,EAAE;MACrC;MACAE,KAAK,CAACH,OAAO,GAAGG,KAAK,CAACN,QAAQ,CAACE,IAAI,CAACE,MAAM;IAC5C;IACA;IACA,IAAIE,KAAK,CAACN,QAAQ,CAACE,IAAI,CAACK,MAAM,EAAE;MAC9BD,KAAK,CAACC,MAAM,GAAGD,KAAK,CAACN,QAAQ,CAACE,IAAI,CAACK,MAAM;IAC3C;EACF;EACA;EACA,OAAOC,OAAO,CAACC,MAAM,CAACH,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;;AAEA;AACA,OAAO,MAAMI,YAAY,GAAIC,QAAQ,IAAK;EAAE;EAC1C;EACA;EACA,OAAOf,SAAS,CAACgB,IAAI,CAAC,cAAc,EAAED,QAAQ,EAAE;IAC9CE,OAAO,EAAE;MACP,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,OAAO,MAAMC,UAAU,GAAIC,MAAM,IAAK;EACpC;EACA,MAAMC,WAAW,GAAG;IAAE,GAAGD;EAAO,CAAC;EACjC,IAAI,OAAOC,WAAW,CAACC,gBAAgB,KAAK,SAAS,EAAE;IACrDD,WAAW,CAACC,gBAAgB,GAAGC,MAAM,CAACF,WAAW,CAACC,gBAAgB,CAAC;EACrE;EACA,OAAOrB,SAAS,CAACuB,GAAG,CAAC,cAAc,EAAE;IAAEJ,MAAM,EAAEC;EAAY,CAAC,CAAC;AAC/D,CAAC;;AAED;AACA,OAAO,MAAMI,YAAY,GAAIC,OAAO,IAAK;EAAE;EACvC;EACA,OAAOzB,SAAS,CAACuB,GAAG,CAAC,qBAAqBE,OAAO,EAAE,EAAE;IAAEC,YAAY,EAAE;EAAO,CAAC,CAAC;AAClF,CAAC;;AAED;AACA,OAAO,MAAMC,WAAW,GAAIF,OAAO,IAAK;EAAE;EACxC,OAAOzB,SAAS,CAAC4B,MAAM,CAAC,gBAAgBH,OAAO,EAAE,CAAC;AACpD,CAAC;;AAED;AACA,OAAO,MAAMI,kBAAkB,GAAGA,CAACJ,OAAO,EAAEK,OAAO,KAAK;EACtD,OAAO9B,SAAS,CAAC+B,GAAG,CAAC,gBAAgBN,OAAO,WAAW,EAAE;IAAEO,QAAQ,EAAEF;EAAQ,CAAC,CAAC;AACjF,CAAC;;AAED;;AAEA;AACA,OAAO,MAAMG,UAAU,GAAId,MAAM,IAAK;EACpC;EACA,MAAMC,WAAW,GAAG;IAAE,GAAGD;EAAO,CAAC;EACjC,OAAOC,WAAW,CAACc,WAAW,CAAC,CAAC;EAChC,OAAOlC,SAAS,CAACuB,GAAG,CAAC,SAAS,EAAE;IAAEJ,MAAM,EAAEC;EAAY,CAAC,CAAC;AAC1D,CAAC;;AAED;AACA,OAAO,MAAMe,aAAa,GAAGA,CAAA,KAAM;EACjC,OAAOnC,SAAS,CAACgB,IAAI,CAAC,iBAAiB,CAAC;AAC1C,CAAC;;AAED;;AAEA;AACA,OAAO,MAAMoB,eAAe,GAAIC,UAAU,IAAK;EAC7C,OAAOrC,SAAS,CAACgB,IAAI,CAAC,2BAA2BqB,UAAU,EAAE,CAAC;AAChE,CAAC;;AAED;;AAEA;AACA,OAAO,MAAMC,iBAAiB,GAAInB,MAAM,IAAK;EAC3C;EACA,MAAMC,WAAW,GAAG;IAAE,GAAGD;EAAO,CAAC;EACjC,IAAI,OAAOC,WAAW,CAACmB,SAAS,KAAK,SAAS,EAAE;IAC9CnB,WAAW,CAACmB,SAAS,GAAGjB,MAAM,CAACF,WAAW,CAACmB,SAAS,CAAC;EACvD;EACA,OAAOvC,SAAS,CAACuB,GAAG,CAAC,kBAAkB,EAAE;IAAEJ,MAAM,EAAEC;EAAY,CAAC,CAAC;AACnE,CAAC;;AAED;AACA,OAAO,MAAMoB,yBAAyB,GAAGA,CAACC,QAAQ,EAAEC,UAAU,KAAK;EAC/D;EACA;EACA;EACA,MAAMC,OAAO,GAAG;IAAE,GAAGD;EAAW,CAAC;EACjC,OAAOC,OAAO,CAACT,WAAW,CAAC,CAAC;;EAE5B;EACA;EACA,OAAOlC,SAAS,CAAC+B,GAAG,CAAC,oBAAoBU,QAAQ,EAAE,EAAEE,OAAO,CAAC;AACjE,CAAC;;AAED;AACA,OAAO,MAAMC,yBAAyB,GAAIF,UAAU,IAAK;EACvD;EACA;EACA;EACA;EACA,OAAO1C,SAAS,CAACgB,IAAI,CAAC,kBAAkB,EAAE0B,UAAU,CAAC;AACvD,CAAC;;AAED;AACA,OAAO,MAAMG,yBAAyB,GAAIJ,QAAQ,IAAK;EACrDK,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAEN,QAAQ,CAAC,CAAC,CAAC;EAC9E,MAAMO,GAAG,GAAG,oBAAoBP,QAAQ,EAAE;EAC1CK,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEC,GAAG,CAAC,CAAC,CAAC;EACzC,OAAOhD,SAAS,CAAC4B,MAAM,CAACoB,GAAG,CAAC;AAC9B,CAAC;;AAED;;AAEA;AACA,OAAO,MAAMC,iBAAiB,GAAI9B,MAAM,IAAK;EACzC;EACA,OAAOnB,SAAS,CAACuB,GAAG,CAAC,mBAAmB,EAAE;IAAEJ;EAAO,CAAC,CAAC;AACzD,CAAC;;AAED;AACA,OAAO,MAAM+B,mBAAmB,GAAGA,CAACC,cAAc,EAAEhC,MAAM,KAAK;EAC3D;EACA,OAAOnB,SAAS,CAACuB,GAAG,CAAC,uBAAuB4B,cAAc,EAAE,EAAE;IAAEhC;EAAO,CAAC,CAAC;AAC7E,CAAC;;AAED;AACA,OAAO,MAAMiC,cAAc,GAAGA,CAACD,cAAc,EAAEE,gBAAgB,KAAK;EAChE,OAAOrD,SAAS,CAACgB,IAAI,CAAC,oBAAoB,EAAE;IACxCsC,gBAAgB,EAAEH,cAAc;IAChCI,mBAAmB,EAAEF;EACzB,CAAC,CAAC;AACN,CAAC;;AAED;AACA,OAAO,MAAMG,iBAAiB,GAAGA,CAACL,cAAc,EAAEE,gBAAgB,KAAK;EACnE,OAAOrD,SAAS,CAAC4B,MAAM,CAAC,oBAAoB,EAAE;IAC1CtB,IAAI,EAAE;MAAE;MACJgD,gBAAgB,EAAEH,cAAc;MAChCI,mBAAmB,EAAEF;IACzB;EACJ,CAAC,CAAC;AACN,CAAC;;AAED;;AAEA;AACA,OAAO,MAAMI,qBAAqB,GAAIpB,UAAU,IAAK;EACjD,OAAOrC,SAAS,CAACuB,GAAG,CAAC,gCAAgC,EAAE;IAAEJ,MAAM,EAAE;MAAEe,WAAW,EAAEG;IAAW;EAAE,CAAC,CAAC;AACnG,CAAC;;AAED;AACA,OAAO,MAAMqB,oBAAoB,GAAGA,CAACC,OAAO,EAAEtB,UAAU,KAAK;EACzD,OAAOrC,SAAS,CAACuB,GAAG,CAAC,+BAA+B,EAAE;IAAEJ,MAAM,EAAE;MAAEyC,QAAQ,EAAED,OAAO;MAAEzB,WAAW,EAAEG;IAAW;EAAE,CAAC,CAAC;AACrH,CAAC;;AAED;AACA,OAAO,MAAMwB,kBAAkB,GAAGA,CAACF,OAAO,EAAEtB,UAAU,EAAEyB,SAAS,KAAK;EAClE,OAAO9D,SAAS,CAACuB,GAAG,CAAC,6BAA6B,EAAE;IAAEJ,MAAM,EAAE;MAAEyC,QAAQ,EAAED,OAAO;MAAEzB,WAAW,EAAEG,UAAU;MAAEyB,SAAS,EAAEA;IAAU;EAAE,CAAC,CAAC;AACzI,CAAC;;AAED;;AAEA;AACA,OAAO,MAAMC,4BAA4B,GAAGA,CAACC,OAAO,GAAG,KAAK,KAAK;EAC/D,OAAOhE,SAAS,CAACuB,GAAG,CAAC,kCAAkC,EAAE;IAAEJ,MAAM,EAAE;MAAE6C;IAAQ;EAAE,CAAC,CAAC;AACnF,CAAC;;AAED;AACA,OAAO,MAAMC,wBAAwB,GAAIC,OAAO,IAAK;EACnD;EACA;EACA;EACA;EACA;EACA;;EAEA,MAAM/C,MAAM,GAAG;IAAE,GAAG+C;EAAQ,CAAC;EAC7B,IAAIC,KAAK,CAACC,OAAO,CAACjD,MAAM,CAACkD,YAAY,CAAC,EAAE;IACtClD,MAAM,CAACkD,YAAY,GAAGlD,MAAM,CAACkD,YAAY,CAACC,IAAI,CAAC,GAAG,CAAC;EACrD;EACA,IAAIH,KAAK,CAACC,OAAO,CAACjD,MAAM,CAACoD,gBAAgB,CAAC,EAAE;IAC1CpD,MAAM,CAACqD,OAAO,GAAGrD,MAAM,CAACoD,gBAAgB,CAACD,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IACpD,OAAOnD,MAAM,CAACoD,gBAAgB;EAChC;EACA;EACA,IAAIpD,MAAM,CAACsD,IAAI,EAAEtD,MAAM,CAACsD,IAAI,GAAGC,MAAM,CAACvD,MAAM,CAACsD,IAAI,CAAC;EAClD,IAAItD,MAAM,CAACwD,QAAQ,EAAExD,MAAM,CAACwD,QAAQ,GAAGD,MAAM,CAACvD,MAAM,CAACwD,QAAQ,CAAC;EAE9D,OAAO3E,SAAS,CAACuB,GAAG,CAAC,qBAAqB,EAAE;IAAEJ;EAAO,CAAC,CAAC;AACzD,CAAC;;AAED;AACA,OAAO,MAAMyD,gBAAgB,GAAIC,QAAQ,IAAK;EAC5C,OAAO7E,SAAS,CAACuB,GAAG,CAAC,uBAAuBsD,QAAQ,EAAE,CAAC;AACzD,CAAC;;AAED;AACA,OAAO,MAAMC,mBAAmB,GAAID,QAAQ,IAAK;EAC/C,OAAO7E,SAAS,CAACuB,GAAG,CAAC,uBAAuBsD,QAAQ,SAAS,CAAC;AAChE,CAAC;AACD;;AAEA,OAAO,MAAME,oBAAoB,GAAG,MAAAA,CAAA,KAAY;EAC9C,IAAI;IACF,MAAM3E,QAAQ,GAAG,MAAMJ,SAAS,CAACuB,GAAG,CAAC,qBAAqB,CAAC;IAC3D,OAAOnB,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOI,KAAK,EAAE;IACdoC,OAAO,CAACpC,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;IAC1D,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMsE,sBAAsB,GAAG,MAAOC,cAAc,IAAK;EAC9D,IAAI;IACF,MAAMC,qBAAqB,GAAGC,kBAAkB,CAACF,cAAc,CAAC;IAChE,MAAM7E,QAAQ,GAAG,MAAMJ,SAAS,CAAC4B,MAAM,CAAC,uBAAuBsD,qBAAqB,EAAE,CAAC;IACvF,OAAO9E,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOI,KAAK,EAAE;IACdoC,OAAO,CAACpC,KAAK,CAAC,oCAAoCuE,cAAc,GAAG,EAAEvE,KAAK,CAAC;IAC3E,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;;AAEA;AACA,OAAO,MAAM0E,aAAa,GAAIC,MAAM,IAAK;EACrC,OAAOrF,SAAS,CAACuB,GAAG,CAAC,iBAAiB8D,MAAM,EAAE,CAAC;AACnD,CAAC;;AAED;AACA,OAAO,MAAMC,4BAA4B,GAAIjD,UAAU,IAAK;EAC1D,OAAOrC,SAAS,CAACgB,IAAI,CAAC,kCAAkCqB,UAAU,EAAE,CAAC;AACvE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}