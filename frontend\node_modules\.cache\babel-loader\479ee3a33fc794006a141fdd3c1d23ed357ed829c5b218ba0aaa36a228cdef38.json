{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '/api'; // Use environment variable or default\n\nconst apiClient = axios.create({\n  baseURL: API_BASE_URL\n});\n\n// Add a response interceptor\napiClient.interceptors.response.use(response => {\n  // Check for backend-specific success messages\n  if (response.data && response.data.message) {\n    // You might want to attach this to the response object for components to use\n    // e.g., response.successMessage = response.data.message;\n  } else if (response.data && response.data.detail && response.status >= 200 && response.status < 300) {\n    // Fallback for success messages in 'detail'\n    // response.successMessage = response.data.detail;\n  }\n  // For successful image uploads, the structure is different (response.data.success, response.data.errors)\n  // This will be handled in the ImageUpload.js component as per instructions.\n  return response;\n}, error => {\n  // Check for backend-specific error messages\n  if (error.response && error.response.data) {\n    if (error.response.data.error) {\n      error.message = error.response.data.error;\n    } else if (error.response.data.detail) {\n      // Fallback for error messages in 'detail'\n      error.message = error.response.data.detail;\n    }\n    // Attach the structured errors if available (e.g., for form validation)\n    if (error.response.data.errors) {\n      error.errors = error.response.data.errors;\n    }\n  }\n  // If no specific message, Axios error.message or a default will be used\n  return Promise.reject(error);\n});\n\n// Export the apiClient instance for use in other service files\nexport default apiClient;\n\n// Functions that were not categorized as ModelTestWorkBench or PatentViz specific\n// could remain here if they are truly generic.\n// For now, assuming all specific functions have been moved.\n// If any shared functions are identified later, they can be added here.\n\n// Example of a shared utility if needed (currently none identified from the original file)\n// export const getSomeSharedResource = () => {\n//   return apiClient.get('/shared-resource');\n// };", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_BASE_URL", "apiClient", "create", "baseURL", "interceptors", "response", "use", "data", "message", "detail", "status", "error", "errors", "Promise", "reject"], "sources": ["D:/Documents/Programing/TRO/ModelTestsWorkbench/frontend/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\r\n\r\nconst API_BASE_URL = process.env.REACT_APP_API_BASE_URL || '/api'; // Use environment variable or default\r\n\r\nconst apiClient = axios.create({\r\n  baseURL: API_BASE_URL,\r\n});\r\n\r\n// Add a response interceptor\r\napiClient.interceptors.response.use(\r\n  (response) => {\r\n    // Check for backend-specific success messages\r\n    if (response.data && response.data.message) {\r\n      // You might want to attach this to the response object for components to use\r\n      // e.g., response.successMessage = response.data.message;\r\n    } else if (response.data && response.data.detail && response.status >= 200 && response.status < 300) {\r\n      // Fallback for success messages in 'detail'\r\n      // response.successMessage = response.data.detail;\r\n    }\r\n    // For successful image uploads, the structure is different (response.data.success, response.data.errors)\r\n    // This will be handled in the ImageUpload.js component as per instructions.\r\n    return response;\r\n  },\r\n  (error) => {\r\n    // Check for backend-specific error messages\r\n    if (error.response && error.response.data) {\r\n      if (error.response.data.error) {\r\n        error.message = error.response.data.error;\r\n      } else if (error.response.data.detail) {\r\n        // Fallback for error messages in 'detail'\r\n        error.message = error.response.data.detail;\r\n      }\r\n      // Attach the structured errors if available (e.g., for form validation)\r\n      if (error.response.data.errors) {\r\n        error.errors = error.response.data.errors;\r\n      }\r\n    }\r\n    // If no specific message, Axios error.message or a default will be used\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Export the apiClient instance for use in other service files\r\nexport default apiClient;\r\n\r\n// Functions that were not categorized as ModelTestWorkBench or PatentViz specific\r\n// could remain here if they are truly generic.\r\n// For now, assuming all specific functions have been moved.\r\n// If any shared functions are identified later, they can be added here.\r\n\r\n// Example of a shared utility if needed (currently none identified from the original file)\r\n// export const getSomeSharedResource = () => {\r\n//   return apiClient.get('/shared-resource');\r\n// };"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,MAAM,CAAC,CAAC;;AAEnE,MAAMC,SAAS,GAAGL,KAAK,CAACM,MAAM,CAAC;EAC7BC,OAAO,EAAEN;AACX,CAAC,CAAC;;AAEF;AACAI,SAAS,CAACG,YAAY,CAACC,QAAQ,CAACC,GAAG,CAChCD,QAAQ,IAAK;EACZ;EACA,IAAIA,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;IAC1C;IACA;EAAA,CACD,MAAM,IAAIH,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACE,MAAM,IAAIJ,QAAQ,CAACK,MAAM,IAAI,GAAG,IAAIL,QAAQ,CAACK,MAAM,GAAG,GAAG,EAAE;IACnG;IACA;EAAA;EAEF;EACA;EACA,OAAOL,QAAQ;AACjB,CAAC,EACAM,KAAK,IAAK;EACT;EACA,IAAIA,KAAK,CAACN,QAAQ,IAAIM,KAAK,CAACN,QAAQ,CAACE,IAAI,EAAE;IACzC,IAAII,KAAK,CAACN,QAAQ,CAACE,IAAI,CAACI,KAAK,EAAE;MAC7BA,KAAK,CAACH,OAAO,GAAGG,KAAK,CAACN,QAAQ,CAACE,IAAI,CAACI,KAAK;IAC3C,CAAC,MAAM,IAAIA,KAAK,CAACN,QAAQ,CAACE,IAAI,CAACE,MAAM,EAAE;MACrC;MACAE,KAAK,CAACH,OAAO,GAAGG,KAAK,CAACN,QAAQ,CAACE,IAAI,CAACE,MAAM;IAC5C;IACA;IACA,IAAIE,KAAK,CAACN,QAAQ,CAACE,IAAI,CAACK,MAAM,EAAE;MAC9BD,KAAK,CAACC,MAAM,GAAGD,KAAK,CAACN,QAAQ,CAACE,IAAI,CAACK,MAAM;IAC3C;EACF;EACA;EACA,OAAOC,OAAO,CAACC,MAAM,CAACH,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA,eAAeV,SAAS;;AAExB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}