# Database Schema Documentation

## Introduction

This document outlines the structure of the PostgreSQL database, providing details on tables, columns, and data types. This information is intended for developers working with the database.

## Table of Contents

- [Patents Records (`patents_records`)](#patents-records-patents_records)
- [Copyrights (`copyrights`)](#copyrights-copyrights)
- [Patents CPC Assignments (`patents_cpc_assignments`)](#patents-cpc-assignments-patents_cpc_assignments)
- [Patents USPC Definitions (`patents_uspc_definitions`)](#patents-uspc-definitions-patents_uspc_definitions)
- [Trademarks (`trademarks`)](#trademarks-trademarks)
- [Patents CPC Definitions (`patents_cpc_definitions`)](#patents-cpc-definitions-patents_cpc_definitions)
- [Patents LOC Definitions (`patents_loc_definitions`)](#patents-loc-definitions-patents_loc_definitions)
- [Empty/Undefined Tables](#emptyundefined-tables)

---

## Patents Records (`patents_records`)

This table stores information related to patent records.

| Column Name             | Data Type                    |
| ----------------------- | ---------------------------- |
| `id`                  | `uuid`                     |
| `tro`                 | `boolean`                  |
| `date_published`      | `date`                     |
| `plaintiff_ids`       | `ARRAY`                    |
| `design_page_numbers` | `ARRAY`                    |
| `create_time`         | `timestamp with time zone` |
| `update_time`         | `timestamp with time zone` |
| `patent_title`        | `text`                     |
| `loc_edition`         | `text`                     |
| `uspc_class`          | `text`                     |
| `patent_type`         | `text`                     |
| `abstract`            | `text`                     |
| `associated_patents`  | `ARRAY`                    |
| `uspc_subclass`       | `text`                     |
| `pdf_source`          | `text`                     |
| `image_source`        | `text`                     |
| `certificate_source`  | `text`                     |
| `reg_no`              | `text`                     |
| `document_id`         | `text`                     |
| `loc_code`            | `text`                     |
| `inventors`           | `text`                     |
| `assignee`            | `text`                     |
| `applicant`           | `text`                     |

---

## Copyrights (`copyrights`)

This table stores information about copyright records.

| Column Name                     | Data Type                    |
| ------------------------------- | ---------------------------- |
| `id`                          | `uuid`                     |
| `registration_date`           | `date`                     |
| `date_of_creation`            | `integer`                  |
| `date_of_publication`         | `date`                     |
| `plaintiff_ids`               | `ARRAY`                    |
| `create_time`                 | `timestamp with time zone` |
| `update_time`                 | `timestamp with time zone` |
| `names`                       | `text`                     |
| `copyright_claimant`          | `text`                     |
| `authorship_on_application`   | `text`                     |
| `rights_and_permissions`      | `text`                     |
| `tro`                         | `text`                     |
| `registration_number`         | `text`                     |
| `description`                 | `text`                     |
| `type_of_work`                | `text`                     |
| `title`                       | `text`                     |
| `nation_of_first_publication` | `text`                     |

---

## Patents CPC Assignments (`patents_cpc_assignments`)

This table links patents to their CPC (Cooperative Patent Classification) assignments.

| Column Name    | Data Type |
| -------------- | --------- |
| `patents_id` | `uuid`  |
| `cpc_id`     | `uuid`  |

---

## Patents USPC Definitions (`patents_uspc_definitions`)

This table stores definitions for USPC (United States Patent Classification) codes.

| Column Name    | Data Type             |
| -------------- | --------------------- |
| `class`      | `character varying` |
| `subclass`   | `character varying` |
| `definition` | `character varying` |

---

## Trademarks (`trademarks`)

This table contains information about trademark records.

| Column Name                                       | Data Type                    |
| ------------------------------------------------- | ---------------------------- |
| `id`                                            | `uuid`                     |
| `int_cls`                                       | `ARRAY`                    |
| `filing_date`                                   | `date`                     |
| `plaintiff_ids`                                 | `ARRAY`                    |
| `nb_suits`                                      | `bigint`                   |
| `mark_current_status_code`                      | `integer`                  |
| `mark_feature_code`                             | `integer`                  |
| `mark_standard_character_indicator`             | `boolean`                  |
| `goods_services`                                | `jsonb`                    |
| `case_file_statements_other`                    | `jsonb`                    |
| `create_time`                                   | `timestamp with time zone` |
| `update_time`                                   | `timestamp with time zone` |
| `info_source`                                   | `text`                     |
| `image_source`                                  | `text`                     |
| `certificate_source`                            | `text`                     |
| `national_design_code`                          | `ARRAY`                    |
| `mark_current_status_external_description_text` | `text`                     |
| `goods_services_text_daily`                     | `text`                     |
| `mark_disclaimer_text`                          | `ARRAY`                    |
| `mark_disclaimer_text_daily`                    | `ARRAY`                    |
| `mark_image_colour_claimed_text`                | `text`                     |
| `mark_image_colour_part_claimed_text`           | `text`                     |
| `mark_image_colour_statement_daily`             | `ARRAY`                    |
| `mark_translation_statement_daily`              | `text`                     |
| `name_portrait_statement_daily`                 | `text`                     |
| `reg_no`                                        | `text`                     |
| `ser_no`                                        | `text`                     |
| `tro`                                           | `text`                     |
| `applicant_name`                                | `text`                     |
| `mark_text`                                     | `text`                     |
| `mark_description_statement_daily`              | `text`                     |
| `certification_mark_statement_daily`            | `text`                     |
| `lining_stippling_statement_daily`              | `text`                     |
| `section_2f_statement_daily`                    | `text`                     |
| `country_codes`                                 | `ARRAY`                    |
| `associated_marks`                              | `ARRAY`                    |

---

## Patents CPC Definitions (`patents_cpc_definitions`)

This table stores definitions for CPC (Cooperative Patent Classification) codes.

| Column Name    | Data Type |
| -------------- | --------- |
| `id`         | `uuid`  |
| `version`    | `text`  |
| `section`    | `text`  |
| `class`      | `text`  |
| `subclass`   | `text`  |
| `main_group` | `text`  |
| `sub_group`  | `text`  |
| `definition` | `text`  |

---

## Patents LOC Definitions (`patents_loc_definitions`)

This table stores definitions for LOC (Locarno Classification) codes related to patents.

| Column Name             | Data Type |
| ----------------------- | --------- |
| `loc_edition`         | `text`  |
| `loc_code`            | `text`  |
| `class`               | `text`  |
| `class_definition`    | `text`  |
| `subclass`            | `text`  |
| `subclass_definition` | `text`  |

---

## Empty/Undefined Tables

The following tables appeared to be empty or their schema was not retrievable during the last check:

- `patents_loc_assignments`
- `patents_uspc_assignments`
