{"ast": null, "code": "import apiClient from './api'; // Assuming api.js will now export apiClient\n\n// --- Patent Visualization Platform ---\n\n// Corresponds to GET /api/v1/patents/dashboard/statistics\nexport const getPatentDashboardStatistics = (refresh = false) => {\n  return apiClient.get(`/v1/patents/dashboard/statistics`, {\n    params: {\n      refresh\n    }\n  });\n};\n\n// Corresponds to GET /api/v1/patents/explore\nexport const getPatentsForExploration = filters => {\n  // Filters object includes various search and pagination parameters\n  const params = {\n    ...filters\n  };\n  if (Array.isArray(params.patent_types)) {\n    params.patent_types = params.patent_types.join(',');\n  }\n  if (Array.isArray(params.selected_columns)) {\n    params.columns = params.selected_columns.join(','); // API expects 'columns'\n    delete params.selected_columns;\n  }\n  if (params.page) params.page = Number(params.page);\n  if (params.per_page) params.per_page = Number(params.per_page);\n  return apiClient.get(`/v1/patents/explore`, {\n    params\n  });\n};\n\n// Corresponds to GET /api/v1/patents/explore/<patent_id>\nexport const getPatentDetails = patentId => {\n  return apiClient.get(`/v1/patents/explore/${patentId}`);\n};\n\n// Corresponds to GET /api/v1/patents/explore/<patent_id>/images\nexport const getPatentImagesInfo = patentId => {\n  return apiClient.get(`/v1/patents/explore/${patentId}/images`);\n};", "map": {"version": 3, "names": ["apiClient", "getPatentDashboardStatistics", "refresh", "get", "params", "getPatentsForExploration", "filters", "Array", "isArray", "patent_types", "join", "selected_columns", "columns", "page", "Number", "per_page", "getPatentDetails", "patentId", "getPatentImagesInfo"], "sources": ["D:/Documents/Programing/TRO/ModelTestsWorkbench/frontend/src/services/api_patent_viz.js"], "sourcesContent": ["import apiClient from './api'; // Assuming api.js will now export apiClient\r\n\r\n// --- Patent Visualization Platform ---\r\n\r\n// Corresponds to GET /api/v1/patents/dashboard/statistics\r\nexport const getPatentDashboardStatistics = (refresh = false) => {\r\n  return apiClient.get(`/v1/patents/dashboard/statistics`, { params: { refresh } });\r\n};\r\n\r\n// Corresponds to GET /api/v1/patents/explore\r\nexport const getPatentsForExploration = (filters) => {\r\n  // Filters object includes various search and pagination parameters\r\n  const params = { ...filters };\r\n  if (Array.isArray(params.patent_types)) {\r\n    params.patent_types = params.patent_types.join(',');\r\n  }\r\n  if (Array.isArray(params.selected_columns)) {\r\n    params.columns = params.selected_columns.join(','); // API expects 'columns'\r\n    delete params.selected_columns;\r\n  }\r\n  if (params.page) params.page = Number(params.page);\r\n  if (params.per_page) params.per_page = Number(params.per_page);\r\n\r\n  return apiClient.get(`/v1/patents/explore`, { params });\r\n};\r\n\r\n// Corresponds to GET /api/v1/patents/explore/<patent_id>\r\nexport const getPatentDetails = (patentId) => {\r\n  return apiClient.get(`/v1/patents/explore/${patentId}`);\r\n};\r\n\r\n// Corresponds to GET /api/v1/patents/explore/<patent_id>/images\r\nexport const getPatentImagesInfo = (patentId) => {\r\n  return apiClient.get(`/v1/patents/explore/${patentId}/images`);\r\n};\r\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,OAAO,CAAC,CAAC;;AAE/B;;AAEA;AACA,OAAO,MAAMC,4BAA4B,GAAGA,CAACC,OAAO,GAAG,KAAK,KAAK;EAC/D,OAAOF,SAAS,CAACG,GAAG,CAAC,kCAAkC,EAAE;IAAEC,MAAM,EAAE;MAAEF;IAAQ;EAAE,CAAC,CAAC;AACnF,CAAC;;AAED;AACA,OAAO,MAAMG,wBAAwB,GAAIC,OAAO,IAAK;EACnD;EACA,MAAMF,MAAM,GAAG;IAAE,GAAGE;EAAQ,CAAC;EAC7B,IAAIC,KAAK,CAACC,OAAO,CAACJ,MAAM,CAACK,YAAY,CAAC,EAAE;IACtCL,MAAM,CAACK,YAAY,GAAGL,MAAM,CAACK,YAAY,CAACC,IAAI,CAAC,GAAG,CAAC;EACrD;EACA,IAAIH,KAAK,CAACC,OAAO,CAACJ,MAAM,CAACO,gBAAgB,CAAC,EAAE;IAC1CP,MAAM,CAACQ,OAAO,GAAGR,MAAM,CAACO,gBAAgB,CAACD,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IACpD,OAAON,MAAM,CAACO,gBAAgB;EAChC;EACA,IAAIP,MAAM,CAACS,IAAI,EAAET,MAAM,CAACS,IAAI,GAAGC,MAAM,CAACV,MAAM,CAACS,IAAI,CAAC;EAClD,IAAIT,MAAM,CAACW,QAAQ,EAAEX,MAAM,CAACW,QAAQ,GAAGD,MAAM,CAACV,MAAM,CAACW,QAAQ,CAAC;EAE9D,OAAOf,SAAS,CAACG,GAAG,CAAC,qBAAqB,EAAE;IAAEC;EAAO,CAAC,CAAC;AACzD,CAAC;;AAED;AACA,OAAO,MAAMY,gBAAgB,GAAIC,QAAQ,IAAK;EAC5C,OAAOjB,SAAS,CAACG,GAAG,CAAC,uBAAuBc,QAAQ,EAAE,CAAC;AACzD,CAAC;;AAED;AACA,OAAO,MAAMC,mBAAmB,GAAID,QAAQ,IAAK;EAC/C,OAAOjB,SAAS,CAACG,GAAG,CAAC,uBAAuBc,QAAQ,SAAS,CAAC;AAChE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}