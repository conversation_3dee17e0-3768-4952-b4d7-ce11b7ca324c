import React, { useState, useEffect, useCallback } from 'react';
import {
  Paper,
  Grid,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Checkbox,
  ListItemText,
  Button,
  Box,
} from '@mui/material';
import { DayPicker } from 'react-day-picker'; // Changed
import 'react-day-picker/style.css'; // Added
import debounce from 'lodash.debounce';

// TDD: TEST: FilterControls renders all specified filter inputs (text, date range, dropdowns)
// TDD: TEST: FilterControls reflects current_filters in input values
// TDD: TEST: FilterControls calls on_filter_change with updated values
// TDD: TEST: FilterControls renders column selection dropdown and calls on_column_selection_change

const PATENT_TYPES_OPTIONS = ["Utility", "Design", "Application", "Plant", "Reissue", "Other"];
const TRO_STATUS_OPTIONS = ["All", "True", "False"];

function FilterControls({
  current_filters,
  on_filter_change,
  available_columns,
  on_column_selection_change,
}) {
  const [localFilters, setLocalFilters] = useState(current_filters);

  useEffect(() => {
    // Sync local state if parent filters change (e.g., on initial load or external reset)
    setLocalFilters(current_filters);
  }, [current_filters]);

  // Debounced callback for text inputs
  const debouncedFilterChange = useCallback(
    debounce((newValues) => {
      on_filter_change(newValues);
    }, 500),
    [on_filter_change]
  );

  const handleInputChange = (field, value) => {
    const updatedLocalFilters = { ...localFilters, [field]: value };
    setLocalFilters(updatedLocalFilters);
    // For text fields, debounce. For others, call immediately or via a specific handler.
    if (typeof value === 'string' && (field.endsWith('_search') || field.includes('Search'))) {
      debouncedFilterChange({ [field]: value });
    } else if (field !== 'date_published_range') {
      // Non-text, non-date range fields update immediately
      on_filter_change({ [field]: value });
    }
  };

  const handleDateRangeChange = (selectedRange) => {
    // selectedRange is { from: Date | undefined, to: Date | undefined } or undefined
    const newRange = selectedRange || { from: null, to: null };
    const updatedValues = {
      date_published_range: {
        from: newRange.from ? newRange.from : null,
        to: newRange.to ? newRange.to : null,
      }
    };
    setLocalFilters(prev => ({ ...prev, ...updatedValues }));
    on_filter_change(updatedValues);
  };

  const handleColumnSelectChange = (event) => {
    const {
      target: { value },
    } = event;
    const selectedFieldNames = typeof value === 'string' ? value.split(',') : value;
    
    // Ensure alwaysVisible columns are always included
    const alwaysVisibleFields = available_columns
        .filter(col => col.alwaysVisible)
        .map(col => col.field);
    
    const newSelectedColumns = Array.from(new Set([...alwaysVisibleFields, ...selectedFieldNames]));

    on_column_selection_change(newSelectedColumns);
  };
  
  const handleResetFilters = () => {
    // This should ideally call a reset function in the parent to reset to INITIAL_FILTERS
    // For now, we'll just clear local state and call on_filter_change with empty-ish values
    // The parent (PatentExplorerPage) should define what "reset" means.
    // A more robust solution would involve the parent passing down a reset function or INITIAL_FILTERS.
     const resetValues = {
        document_id_search: "", patent_title_search: "", abstract_search: "",
        date_published_range: { from: null, to: null }, // Changed
        patent_types: [],
        tro_status: "All", inventors_search: "", assignee_search: "", applicant_search: "",
        uspc_class_search: "", loc_code_search: "", cpc_class_search: "",
     };
     setLocalFilters(prev => ({...prev, ...resetValues}));
     on_filter_change(resetValues);
  };

  const formatDate = (date) => date ? date.toLocaleDateString() : "";

  let footer = <p>Please pick the first day.</p>;
  if (localFilters.date_published_range?.from) {
    if (!localFilters.date_published_range.to) {
      footer = <p>{formatDate(localFilters.date_published_range.from)}</p>;
    } else if (localFilters.date_published_range.to) {
      footer = (
        <p>
          {formatDate(localFilters.date_published_range.from)}–{formatDate(localFilters.date_published_range.to)}
        </p>
      );
    }
  }

  return (
      <Paper sx={{ p: 2, mb: 2 }} elevation={2}>
        <Grid container spacing={2} alignItems="flex-start"> {/* Changed alignItems for DayPicker */}
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              label="Document ID"
              fullWidth
              variant="outlined"
              size="small"
              value={localFilters.document_id_search || ''}
              onChange={(e) => handleInputChange('document_id_search', e.target.value)}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              label="Patent Title"
              fullWidth
              variant="outlined"
              size="small"
              value={localFilters.patent_title_search || ''}
              onChange={(e) => handleInputChange('patent_title_search', e.target.value)}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              label="Abstract"
              fullWidth
              variant="outlined"
              size="small"
              value={localFilters.abstract_search || ''}
              onChange={(e) => handleInputChange('abstract_search', e.target.value)}
            />
          </Grid>
           <Grid item xs={12} sm={6} md={3}>
            <TextField
              label="Inventors"
              fullWidth
              variant="outlined"
              size="small"
              value={localFilters.inventors_search || ''}
              onChange={(e) => handleInputChange('inventors_search', e.target.value)}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              label="Assignee"
              fullWidth
              variant="outlined"
              size="small"
              value={localFilters.assignee_search || ''}
              onChange={(e) => handleInputChange('assignee_search', e.target.value)}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              label="Applicant"
              fullWidth
              variant="outlined"
              size="small"
              value={localFilters.applicant_search || ''}
              onChange={(e) => handleInputChange('applicant_search', e.target.value)}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              label="USPC Class"
              fullWidth
              variant="outlined"
              size="small"
              value={localFilters.uspc_class_search || ''}
              onChange={(e) => handleInputChange('uspc_class_search', e.target.value)}
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              label="LOC Code"
              fullWidth
              variant="outlined"
              size="small"
              value={localFilters.loc_code_search || ''}
              onChange={(e) => handleInputChange('loc_code_search', e.target.value)}
            />
          </Grid>
           <Grid item xs={12} sm={6} md={3}>
            <TextField
              label="CPC Class"
              fullWidth
              variant="outlined"
              size="small"
              value={localFilters.cpc_class_search || ''}
              onChange={(e) => handleInputChange('cpc_class_search', e.target.value)}
            />
          </Grid>

          <Grid item xs={12} sm={12} md={6} lg={4} sx={{ '& .rdp': { margin: '0 auto' } }}> {/* Adjusted grid for better layout */}
            <InputLabel shrink sx={{mb: 1, ml: 0.5, fontSize: '0.75rem'}}>Date Published Range</InputLabel>
            <DayPicker
              mode="range"
              selected={localFilters.date_published_range}
              onSelect={handleDateRangeChange}
              footer={footer}
              showOutsideDays
              fixedWeeks
              captionLayout="dropdown-buttons"
              fromYear={1900}
              toYear={new Date().getFullYear() + 5}
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth size="small" variant="outlined">
              <InputLabel>Patent Types</InputLabel>
              <Select
                multiple
                value={localFilters.patent_types || []}
                onChange={(e) => handleInputChange('patent_types', e.target.value)}
                label="Patent Types"
                renderValue={(selected) => selected.join(', ')}
              >
                {PATENT_TYPES_OPTIONS.map((type) => (
                  <MenuItem key={type} value={type}>
                    <Checkbox checked={(localFilters.patent_types || []).indexOf(type) > -1} />
                    <ListItemText primary={type} />
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <FormControl fullWidth size="small" variant="outlined">
              <InputLabel>TRO Status</InputLabel>
              <Select
                value={localFilters.tro_status || 'All'}
                onChange={(e) => handleInputChange('tro_status', e.target.value)}
                label="TRO Status"
              >
                {TRO_STATUS_OPTIONS.map((status) => (
                  <MenuItem key={status} value={status}>
                    {status}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} sm={6} md={4} lg={3}>
            <FormControl fullWidth size="small" variant="outlined">
              <InputLabel>Select Columns</InputLabel>
              <Select
                multiple
                value={current_filters.selected_columns || []} // Use parent's value directly for controlled component
                onChange={handleColumnSelectChange}
                label="Select Columns"
                renderValue={(selected) => {
                    // Map field names to headerNames for display
                    return selected
                        .map(field => available_columns.find(col => col.field === field)?.headerName || field)
                        .join(', ');
                }}
              >
                {available_columns.map((column) => (
                  <MenuItem key={column.field} value={column.field} disabled={column.alwaysVisible}>
                    <Checkbox 
                        checked={(current_filters.selected_columns || []).indexOf(column.field) > -1} 
                        disabled={column.alwaysVisible}
                    />
                    <ListItemText primary={column.headerName} />
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
           <Grid item xs={12} sm={6} md={2} sx={{display: 'flex', alignItems: 'center'}}>
             <Button variant="outlined" onClick={handleResetFilters} size="medium" fullWidth>
                Reset Filters
             </Button>
           </Grid>
        </Grid>
      </Paper>
  );
}

export default FilterControls;