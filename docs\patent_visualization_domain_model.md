# Patent Visualization Platform - Domain Model

## 1. Introduction

This document outlines the domain model for the Patent Visualization Platform. It defines the core entities, their attributes, and relationships, derived from the [Product Requirements Document](Patent_Visualization_PRD.md) and the existing [Database Schema](1_database_schema.md).

## 2. Core Entities

### 2.1. PatentRecord

* **Description:** Represents a single patent document. This is the central entity for most operations.
* **Source:** [`patents_records`](1_database_schema.md:18) table.
* **Key Attributes:**
  * `id` (uuid): Unique identifier.
  * `document_id` (text): Public document identifier.
  * `patent_title` (text): Title of the patent.
  * `abstract` (text): Abstract of the patent.
  * `date_published` (date): Date the patent was published.
  * `patent_type` (text): Type of patent (e.g., Utility, Design). Interpreted based on prefix (B, S, A, P, E).
  * `tro` (boolean): Indicates if the patent is related to a TRO.
  * `applicant` (text): Applicant information.
  * `inventors` (text): Inventor information.
  * `assignee` (text): Assignee information.
  * `associated_patents` (ARRAY of text): List of associated patent identifiers.
  * `uspc_class` (text): USPC primary classification.
  * `uspc_subclass` (text): USPC sub-classification.
  * `loc_code` (text): LOC classification code.
  * `loc_edition` (text): LOC classification edition.
  * `reg_no` (text): Registration number, used for image path construction.
  * (Other fields from `patents_records` as needed for display)
* **Relationships:**
  * Has many `CPCAssignments` (via `patents_records.id` to `patents_cpc_assignments.patents_id`).

### 2.2. ClassificationDefinition (Abstract Concept)

* **Description:** Represents the definition or description of a patent classification code. This is not a single table but an abstraction over multiple definition tables.
* **Sub-types:**
  * `USPCDefinition`
  * `LOCDefinition`
  * `CPCDefinition`

### 2.3. USPCDefinition

* **Description:** Definition for a USPC code.
* **Source:** [`patents_uspc_definitions`](1_database_schema.md:84) table.
* **Key Attributes:**
  * `class` (character varying): USPC class code.
  * `subclass` (character varying): USPC subclass code.
  * `definition` (character varying): Textual definition.
* **Relationships:**
  * A `PatentRecord` can be associated with one `USPCDefinition` (via `patents_records.uspc_class` and `patents_records.uspc_subclass`).

### 2.4. LOCDefinition

* **Description:** Definition for a LOC code.
* **Source:** [`patents_loc_definitions`](1_database_schema.md:153) table.
* **Key Attributes:**
  * `loc_edition` (text): LOC edition.
  * `loc_code` (text): LOC code.
  * `class` (text): LOC class.
  * `class_definition` (text): Textual definition of the class.
  * `subclass` (text): LOC subclass.
  * `subclass_definition` (text): Textual definition of the subclass.
* **Relationships:**
  * A `PatentRecord` can be associated with one `LOCDefinition` (via `patents_records.loc_code` and `patents_records.loc_edition`).

### 2.5. CPCDefinition

* **Description:** Definition for a CPC code.
* **Source:** [`patents_cpc_definitions`](1_database_schema.md:139) table.
* **Key Attributes:**
  * `id` (uuid): Unique identifier for the CPC definition.
  * `version` (text)
  * `section` (text)
  * `class` (text): CPC class.
  * `subclass` (text)
  * `main_group` (text)
  * `sub_group` (text)
  * `definition` (text): Textual definition.
* **Relationships:**
  * Many `CPCAssignments` can point to one `CPCDefinition` (via `patents_cpc_assignments.cpc_id` to `patents_cpc_definitions.id`).

### 2.6. CPCAssignment

* **Description:** Represents the link between a `PatentRecord` and a `CPCDefinition`.
* **Source:** [`patents_cpc_assignments`](1_database_schema.md:74) table.
* **Key Attributes:**
  * `patents_id` (uuid): Foreign key to `patents_records.id`.
  * `cpc_id` (uuid): Foreign key to `patents_cpc_definitions.id`.
* **Relationships:**
  * Belongs to one `PatentRecord`.
  * Belongs to one `CPCDefinition`.

### 2.7. PatentImage

* **Description:** Represents an image file associated with a `PatentRecord`. This is not a direct database entity but derived from file system structure.
* **Key Attributes:**
  * `patent_reg_no` (text): The `reg_no` of the parent patent.
  * `image_path` (text): Full path to the image file.
  * `image_filename` (text): Name of the image file.
* **Derivation Logic:**
  * Path constructed using `get_master_folder()`, `patent_reg_no`, `date_published`, and specific directory structure rules (see PRD 5.2.3).
  * Files in the constructed directory, excluding `.xml` files.

## 3. Dashboard Statistics (Derived Data)

These are not persistent entities but represent data aggregated for dashboard display.

### 3.1. GeneralStatistic

* **Description:** A generic structure for holding a statistical value.
* **Attributes:**
  * `label` (text): Description of the statistic (e.g., "Total Patents", "% TRO True").
  * `value` (number or text): The calculated statistic.
  * `details` (optional, map/object): Additional context or breakdown.

### 3.2. DistributionStatistic (for Pie Charts)

* **Description:** Represents data for a distribution chart (e.g., patent types, classifications).
* **Attributes:**
  * `label` (text): Title of the chart (e.g., "Patent Type Distribution").
  * `segments` (ARRAY of objects): Each object represents a segment of the pie.
    * `name` (text): Name of the segment (e.g., "Utility Patent", "CPC Class A01").
    * `count` (number): Number of items in this segment.
    * `percentage` (number): Percentage of total for this segment.
  * `top_n_plus_others_applied` (boolean): Indicates if "Top N and Others" logic was used.

### 3.3. MissingDataStatistic

* **Description:** Represents the percentage of records missing a specific field.
* **Attributes:**
  * `field_name` (text): The field being checked (e.g., "Applicant", "USPC Classification").
  * `percentage_missing` (number).

## 4. User Interface State (Conceptual)

These represent states managed by the frontend.

### 4.1. PatentExplorerFilters

* **Description:** Current filter criteria applied in the Patent Exploration table.
* **Attributes:**
  * `document_id_search` (text)
  * `patent_title_search` (text)
  * `abstract_search` (text)
  * `date_published_range` (object: { `start_date`, `end_date` })
  * `selected_patent_types` (ARRAY of text)
  * `tro_status` (enum: "All", "True", "False")
  * `inventors_search` (text)
  * `assignee_search` (text)
  * `applicant_search` (text)
  * `uspc_class_search` (text)
  * `loc_code_search` (text)
  * `cpc_class_search` (text)
  * `page_number` (integer)
  * `items_per_page` (integer)
  * `sort_by_column` (text)
  * `sort_direction` (enum: "asc", "desc")
  * `selected_columns_to_display` (ARRAY of text)

### 4.2. PatentExplorerQuickStats

* **Description:** Quick statistics displayed above the filtered patent table.
* **Attributes:**
  * `total_filtered_results` (integer)
  * `percentage_tro_in_results` (number)
  * `percentage_design_in_results` (number)

## 5. Relationships Overview Diagram (Conceptual)

```mermaid
erDiagram
    PatentRecord ||--o{ CPCAssignment : "has"
    CPCAssignment }|--|| CPCDefinition : "defines"

    PatentRecord }o--|| USPCDefinition : "classified by (USPC)"
    PatentRecord }o--|| LOCDefinition : "classified by (LOC)"

    PatentRecord ||--o{ PatentImage : "has images"

    %% Dashboard Statistics are derived from PatentRecord aggregates
    %% UI State entities manage frontend interactions
```

This domain model provides a structured view of the data and concepts involved in the Patent Visualization Platform, serving as a foundation for pseudocode design.
