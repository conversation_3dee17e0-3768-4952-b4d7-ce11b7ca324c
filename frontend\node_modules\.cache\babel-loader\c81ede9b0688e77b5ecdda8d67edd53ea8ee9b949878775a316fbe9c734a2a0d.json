{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents\\\\Programing\\\\TRO\\\\ModelTestsWorkbench\\\\frontend\\\\src\\\\components\\\\model-test-workbench\\\\ImageBrowser.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Box, Typography, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, CircularProgress, Alert, IconButton, Tooltip, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, Button, TextField, Pagination, Grid,\n// Added for layout\nTableSortLabel,\n// Added for sorting\nLinearProgress,\n// Added for subtle loading indication\nSelect, MenuItem, FormControl, InputLabel } from '@mui/material';\nimport EditIcon from '@mui/icons-material/Edit';\nimport DeleteIcon from '@mui/icons-material/Delete';\nimport RefreshIcon from '@mui/icons-material/Refresh';\nimport { visuallyHidden } from '@mui/utils'; // Added for sorting accessibility\nimport { listImages, updateImageIpOwner, deleteImage } from '../../services/api_model_workbench';\n\n// Helper to format date string\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst formatDate = dateString => {\n  if (!dateString) return 'N/A';\n  try {\n    return new Date(dateString).toLocaleString();\n  } catch (e) {\n    return dateString; // Return original if parsing fails\n  }\n};\nconst ITEMS_PER_PAGE = 20;\nconst headCells = [{\n  id: 'thumbnail',\n  numeric: false,\n  disablePadding: true,\n  label: 'Thumbnail',\n  sortable: false\n}, {\n  id: 'original_filename',\n  numeric: false,\n  disablePadding: false,\n  label: 'Filename',\n  sortable: true\n}, {\n  id: 'image_type',\n  numeric: false,\n  disablePadding: false,\n  label: 'Type',\n  sortable: true\n}, {\n  id: 'ip_category',\n  numeric: false,\n  disablePadding: false,\n  label: 'IP',\n  sortable: true\n}, {\n  id: 'ip_owner',\n  numeric: false,\n  disablePadding: false,\n  label: 'IP Owner',\n  sortable: true\n}, {\n  id: 'created_at',\n  numeric: false,\n  disablePadding: false,\n  label: 'Created At',\n  sortable: true\n}, {\n  id: 'actions',\n  numeric: false,\n  disablePadding: false,\n  label: 'Actions',\n  sortable: false\n}];\n\n// --- Hardcoded Dropdown Options ---\nconst HARDCODED_IP_CATEGORIES = [{\n  value: '',\n  label: 'All IP Categories'\n}, {\n  value: 'trademark',\n  label: 'Trademark'\n}, {\n  value: 'copyright',\n  label: 'Copyright'\n}, {\n  value: 'patent',\n  label: 'Patent'\n}];\nconst HARDCODED_IMAGE_TYPES = [{\n  value: '',\n  label: 'All Image Types'\n}, {\n  value: 'product',\n  label: 'Product'\n}, {\n  value: 'ip',\n  label: 'IP'\n}];\n\n// Debounce utility function\nconst debounce = (func, delay) => {\n  let timeout;\n  return (...args) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), delay);\n  };\n};\nconst ImageBrowser = () => {\n  _s();\n  const [images, setImages] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [refreshTrigger, setRefreshTrigger] = useState(0);\n\n  // Pagination state\n  const [totalPagesApi, setTotalPagesApi] = useState(0); // API's total pages\n  const [currentPageUi, setCurrentPageUi] = useState(1); // UI's current page (for Pagination component)\n\n  // State for Edit Dialog\n  const [editDialogOpen, setEditDialogOpen] = useState(false);\n  const [editingImage, setEditingImage] = useState(null);\n  const [newIpOwner, setNewIpOwner] = useState('');\n  const [editLoading, setEditLoading] = useState(false);\n  const [editError, setEditError] = useState('');\n\n  // State for Delete Dialog\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\n  const [deletingImageId, setDeletingImageId] = useState(null);\n  const [deleteLoading, setDeleteLoading] = useState(false);\n  const [deleteError, setDeleteError] = useState('');\n\n  // Filter state\n  const [filenameFilter, setFilenameFilter] = useState(''); // For immediate input display\n  const [debouncedFilenameFilter, setDebouncedFilenameFilter] = useState(''); // For API calls\n  const [ipFilter, setIpFilter] = useState('');\n  const [typeFilter, setTypeFilter] = useState('');\n\n  // State for dropdown options (now using hardcoded values)\n  const ipCategoryOptions = HARDCODED_IP_CATEGORIES;\n  const imageTypeOptions = HARDCODED_IMAGE_TYPES;\n\n  // Sorting state\n  const [order, setOrder] = useState('desc');\n  const [orderBy, setOrderBy] = useState('created_at');\n  const fetchData = useCallback(async (page, currentFilters, currentSortConfig) => {\n    setIsLoading(true);\n    setError('');\n    try {\n      const params = {\n        page,\n        per_page: ITEMS_PER_PAGE,\n        original_filename: currentFilters.debouncedFilenameFilter || undefined,\n        // Use debounced value\n        ip_category: currentFilters.ipFilter || undefined,\n        image_type: currentFilters.typeFilter || undefined,\n        sort_by: currentSortConfig.orderBy || undefined,\n        sort_order: currentSortConfig.orderBy ? currentSortConfig.order === 'desc' ? 'desc' : 'asc' : undefined\n      };\n      // console.log(\"ImageBrowser: Fetching data with params:\", params);\n      const response = await listImages(params);\n      // console.log('ImageBrowser API response.data:', response.data);\n      const apiData = response.data;\n      if (apiData && typeof apiData === 'object') {\n        const imageList = apiData.images;\n        const paginationData = apiData.pagination;\n        if (Array.isArray(imageList)) {\n          setImages(imageList);\n        } else {\n          console.error('ImageBrowser: response.data.images is not an array. Received:', imageList);\n          setImages([]);\n        }\n        if (paginationData && typeof paginationData === 'object') {\n          setTotalPagesApi(paginationData.total_pages || 0);\n        } else {\n          console.error('ImageBrowser: response.data.pagination is not a valid object or is missing. Received:', paginationData);\n          setTotalPagesApi(0);\n        }\n      } else {\n        console.error('ImageBrowser: response.data is not a valid object or is missing. Received:', apiData);\n        setImages([]);\n        setTotalPagesApi(0);\n      }\n    } catch (err) {\n      var _err$response, _err$response$data;\n      console.error(\"ImageBrowser: Failed to fetch images. Error object:\", err);\n      setError(((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.detail) || err.message || 'Failed to fetch images.');\n      setImages([]);\n      setTotalPagesApi(0);\n    } finally {\n      setIsLoading(false);\n    }\n  }, []); // fetchData itself doesn't depend on changing state, its callers do.\n\n  useEffect(() => {\n    // Use debouncedFilenameFilter for the API call\n    const currentFilters = {\n      debouncedFilenameFilter,\n      ipFilter,\n      typeFilter\n    };\n    const currentSortConfig = {\n      orderBy,\n      order\n    };\n    fetchData(currentPageUi, currentFilters, currentSortConfig);\n  }, [fetchData, refreshTrigger, currentPageUi, debouncedFilenameFilter, ipFilter, typeFilter, orderBy, order]);\n\n  // Debounced function to update the filter used for API calls\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  const updateDebouncedFilenameFilter = useCallback(debounce(value => {\n    setDebouncedFilenameFilter(value);\n    setCurrentPageUi(1); // Reset to first page when debounced search term changes\n  }, 500),\n  // 500ms delay\n  []);\n  const handleFilenameChange = event => {\n    const {\n      value\n    } = event.target;\n    setFilenameFilter(value); // Update input field immediately\n    updateDebouncedFilenameFilter(value); // Call debounced function to update API filter\n  };\n  const handleRefresh = () => {\n    setRefreshTrigger(prev => prev + 1);\n  };\n\n  // Specific handler for IP and Type filters (non-debounced)\n  const handleSelectFilterChange = setter => event => {\n    setter(event.target.value);\n    setCurrentPageUi(1); // Reset to first page on filter change\n  };\n  const handleRequestSort = (event, property) => {\n    const isAsc = orderBy === property && order === 'asc';\n    setOrder(isAsc ? 'desc' : 'asc');\n    setOrderBy(property);\n    setCurrentPageUi(1); // Reset to first page on sort change\n  };\n\n  // --- Edit IP Owner Logic ---\n  const handleOpenEditDialog = image => {\n    setEditingImage({\n      id: image.id,\n      currentOwner: image.ip_owner\n    });\n    setNewIpOwner(image.ip_owner || '');\n    setEditError('');\n    setEditDialogOpen(true);\n  };\n  const handleCloseEditDialog = () => {\n    setEditDialogOpen(false);\n    setEditingImage(null);\n    setNewIpOwner('');\n    setEditError('');\n  };\n  const handleUpdateIpOwner = async () => {\n    if (!editingImage || !newIpOwner.trim()) {\n      setEditError('IP Owner cannot be empty.');\n      return;\n    }\n    setEditLoading(true);\n    setEditError('');\n    try {\n      await updateImageIpOwner(editingImage.id, newIpOwner.trim());\n      handleCloseEditDialog();\n      handleRefresh();\n    } catch (err) {\n      var _err$response2, _err$response2$data;\n      console.error(\"Failed to update IP owner:\", err);\n      setEditError(((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.detail) || err.message || 'Failed to update IP owner.');\n    } finally {\n      setEditLoading(false);\n    }\n  };\n\n  // --- Delete Image Logic ---\n  const handleOpenDeleteDialog = imageId => {\n    console.log(\"handleOpenDeleteDialog entered. imageId:\", imageId);\n    setDeletingImageId(imageId);\n    setDeleteError('');\n    setDeleteDialogOpen(true);\n  };\n  const handleCloseDeleteDialog = () => {\n    setDeleteDialogOpen(false);\n    setDeletingImageId(null);\n    setDeleteError('');\n  };\n  const handleDeleteImage = async () => {\n    console.log(\"handleDeleteImage entered. deletingImageId:\", deletingImageId);\n    if (!deletingImageId) return;\n    setDeleteLoading(true);\n    setDeleteError('');\n    try {\n      console.log(`Attempting to delete image with ID: ${deletingImageId}`);\n      await deleteImage(deletingImageId);\n      console.log(`Delete API call successful for image ID: ${deletingImageId}`);\n      handleCloseDeleteDialog();\n      handleRefresh();\n    } catch (err) {\n      var _err$response3, _err$response3$data;\n      console.error(`Delete API call failed for image ID: ${deletingImageId}`, err);\n      setDeleteError(((_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.detail) || err.message || 'Failed to delete image.');\n    } finally {\n      setDeleteLoading(false);\n    }\n  };\n  const handlePageChange = (event, value) => {\n    setCurrentPageUi(value);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      mt: 4\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: \"Image Browser & Metadata\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"Refresh List\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: handleRefresh,\n            disabled: isLoading,\n            children: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 285,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 2,\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle1\",\n        gutterBottom: true,\n        children: \"Filters\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          children: /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"Filename\",\n            variant: \"outlined\",\n            size: \"small\",\n            value: filenameFilter // TextField displays the immediate value\n            ,\n            onChange: handleFilenameChange // Use the new handler for debouncing\n            ,\n            disabled: isLoading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 3,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            size: \"small\",\n            variant: \"outlined\",\n            disabled: isLoading,\n            sx: {\n              minWidth: '160px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              id: \"ip-category-filter-label\",\n              children: \"IP Category\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              labelId: \"ip-category-filter-label\",\n              id: \"ip-category-filter\",\n              value: ipFilter,\n              label: \"IP Category\",\n              onChange: handleSelectFilterChange(setIpFilter) // Use non-debounced for select\n              ,\n              children: ipCategoryOptions.map(option => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: option.value,\n                children: option.label\n              }, option.value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 3,\n          children: /*#__PURE__*/_jsxDEV(FormControl, {\n            fullWidth: true,\n            size: \"small\",\n            variant: \"outlined\",\n            disabled: isLoading,\n            sx: {\n              minWidth: '160px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n              id: \"image-type-filter-label\",\n              children: \"Image Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              labelId: \"image-type-filter-label\",\n              id: \"image-type-filter\",\n              value: typeFilter,\n              label: \"Image Type\",\n              onChange: handleSelectFilterChange(setTypeFilter) // Use non-debounced for select\n              ,\n              children: imageTypeOptions.map(option => /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: option.value,\n                children: option.label\n              }, option.value, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 350,\n      columnNumber: 17\n    }, this), isLoading && !images.length ?\n    /*#__PURE__*/\n    // Show main loader only if no images are displayed yet\n    _jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        p: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 353,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n        component: Paper,\n        sx: {\n          position: 'relative'\n        },\n        children: [isLoading && /*#__PURE__*/_jsxDEV(LinearProgress, {\n          sx: {\n            position: 'absolute',\n            top: 0,\n            width: '100%'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 27\n        }, this), /*#__PURE__*/_jsxDEV(Table, {\n          sx: {\n            minWidth: 650\n          },\n          \"aria-label\": \"image browser table\",\n          size: \"small\",\n          children: [/*#__PURE__*/_jsxDEV(TableHead, {\n            children: /*#__PURE__*/_jsxDEV(TableRow, {\n              children: headCells.map(headCell => /*#__PURE__*/_jsxDEV(TableCell, {\n                align: headCell.numeric ? 'right' : 'left',\n                padding: headCell.disablePadding ? 'none' : 'normal',\n                sortDirection: orderBy === headCell.id ? order : false,\n                children: headCell.sortable ? /*#__PURE__*/_jsxDEV(TableSortLabel, {\n                  active: orderBy === headCell.id,\n                  direction: orderBy === headCell.id ? order : 'asc',\n                  onClick: event => handleRequestSort(event, headCell.id),\n                  disabled: isLoading,\n                  children: [headCell.label, orderBy === headCell.id ? /*#__PURE__*/_jsxDEV(Box, {\n                    component: \"span\",\n                    sx: visuallyHidden,\n                    children: order === 'desc' ? 'sorted descending' : 'sorted ascending'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 379,\n                    columnNumber: 29\n                  }, this) : null]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 25\n                }, this) : headCell.label\n              }, headCell.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n            children: [images.length === 0 && !isLoading && /*#__PURE__*/_jsxDEV(TableRow, {\n              children: /*#__PURE__*/_jsxDEV(TableCell, {\n                colSpan: headCells.length,\n                align: \"center\",\n                children: \"No images found.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 394,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 19\n            }, this), images.map(image => /*#__PURE__*/_jsxDEV(TableRow, {\n              hover: true,\n              // Use image.id as fallback if image_id is missing\n              sx: {\n                '&:last-child td, &:last-child th': {\n                  border: 0\n                }\n              },\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                component: \"th\",\n                scope: \"row\",\n                padding: \"none\",\n                sx: {\n                  pl: '6px'\n                },\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: `/api/data/images/file/${image.image_id || image.id}`,\n                  alt: image.original_filename,\n                  style: {\n                    height: '50px',\n                    width: 'auto',\n                    maxWidth: '70px',\n                    objectFit: 'contain',\n                    verticalAlign: 'middle',\n                    padding: '4px 0'\n                  },\n                  onError: e => {\n                    e.target.style.display = 'none';\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: image.original_filename\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: image.image_type || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 412,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: image.ip_category || 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 413,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: image.ip_owner || /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  color: \"textSecondary\",\n                  children: \"(Missing)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 42\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: formatDate(image.created_at)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 417,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Edit IP Owner\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => handleOpenEditDialog(image),\n                      disabled: editLoading || deleteLoading || isLoading,\n                      children: /*#__PURE__*/_jsxDEV(EditIcon, {\n                        fontSize: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 422,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 421,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 420,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 419,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Delete Image\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => handleOpenDeleteDialog(image.image_id),\n                      disabled: editLoading || deleteLoading || isLoading,\n                      children: /*#__PURE__*/_jsxDEV(DeleteIcon, {\n                        fontSize: \"small\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 429,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 428,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 427,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 21\n              }, this)]\n            }, image.image_id || image.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 19\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 11\n      }, this), totalPagesApi > 1 && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2,\n          display: 'flex',\n          justifyContent: 'center'\n        },\n        children: /*#__PURE__*/_jsxDEV(Pagination, {\n          count: totalPagesApi,\n          page: currentPageUi,\n          onChange: handlePageChange,\n          color: \"primary\",\n          showFirstButton: true,\n          showLastButton: true,\n          disabled: isLoading\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 442,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 441,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: editDialogOpen,\n      onClose: handleCloseEditDialog,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Edit IP Owner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 458,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(DialogContentText, {\n          sx: {\n            mb: 2\n          },\n          children: [\"Enter the IP Owner for the image: \", editingImage === null || editingImage === void 0 ? void 0 : editingImage.id]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 11\n        }, this), editError && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            mb: 2\n          },\n          children: editError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          autoFocus: true,\n          margin: \"dense\",\n          id: \"ip_owner\",\n          label: \"IP Owner\",\n          type: \"text\",\n          fullWidth: true,\n          variant: \"standard\",\n          value: newIpOwner,\n          onChange: e => setNewIpOwner(e.target.value),\n          disabled: editLoading\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 464,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 459,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseEditDialog,\n          disabled: editLoading,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleUpdateIpOwner,\n          disabled: editLoading || !newIpOwner.trim(),\n          children: editLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 28\n          }, this) : 'Save'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 477,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 457,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: deleteDialogOpen,\n      onClose: handleCloseDeleteDialog,\n      \"aria-labelledby\": \"alert-dialog-title\",\n      \"aria-describedby\": \"alert-dialog-description\",\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        id: \"alert-dialog-title\",\n        children: \"Confirm Deletion\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 492,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [deleteError && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"error\",\n          sx: {\n            mb: 2\n          },\n          children: deleteError\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 494,\n          columnNumber: 27\n        }, this), /*#__PURE__*/_jsxDEV(DialogContentText, {\n          id: \"alert-dialog-description\",\n          children: \"Warning: Deleting this image will permanently remove its data and any associated features or comparison results. This action cannot be undone. Are you sure you want to proceed?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 495,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 493,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDeleteDialog,\n          disabled: deleteLoading,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleDeleteImage,\n          color: \"error\",\n          autoFocus: true,\n          disabled: deleteLoading,\n          children: deleteLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 504,\n            columnNumber: 30\n          }, this) : 'Delete'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 503,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 499,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 486,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 284,\n    columnNumber: 5\n  }, this);\n};\n_s(ImageBrowser, \"ieDZuia/78/95b5+p2OCdjv/tqU=\");\n_c = ImageBrowser;\nexport default ImageBrowser;\nvar _c;\n$RefreshReg$(_c, \"ImageBrowser\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Box", "Typography", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "CircularProgress", "<PERSON><PERSON>", "IconButton", "<PERSON><PERSON><PERSON>", "Dialog", "DialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogTitle", "<PERSON><PERSON>", "TextField", "Pagination", "Grid", "TableSortLabel", "LinearProgress", "Select", "MenuItem", "FormControl", "InputLabel", "EditIcon", "DeleteIcon", "RefreshIcon", "visuallyHidden", "listImages", "updateImageIpOwner", "deleteImage", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "formatDate", "dateString", "Date", "toLocaleString", "e", "ITEMS_PER_PAGE", "head<PERSON>ells", "id", "numeric", "disablePadding", "label", "sortable", "HARDCODED_IP_CATEGORIES", "value", "HARDCODED_IMAGE_TYPES", "debounce", "func", "delay", "timeout", "args", "clearTimeout", "setTimeout", "ImageBrowser", "_s", "images", "setImages", "isLoading", "setIsLoading", "error", "setError", "refreshTrigger", "setRefreshTrigger", "totalPagesApi", "setTotalPagesApi", "currentPageUi", "setCurrentPageUi", "editDialogOpen", "setEditDialogOpen", "editingImage", "setEditingImage", "newIpOwner", "setNewIpOwner", "editLoading", "setEditLoading", "editError", "setEditError", "deleteDialogOpen", "setDeleteDialogOpen", "deletingImageId", "setDeletingImageId", "deleteLoading", "setDeleteLoading", "deleteError", "setDeleteError", "filenameFilter", "setFilenameFilter", "debouncedFilenameFilter", "setDebouncedFilenameFilter", "ipFilter", "setIp<PERSON>ilter", "typeFilter", "setTypeFilter", "ipCategoryOptions", "imageTypeOptions", "order", "setOrder", "orderBy", "setOrderBy", "fetchData", "page", "currentFilters", "currentSortConfig", "params", "per_page", "original_filename", "undefined", "ip_category", "image_type", "sort_by", "sort_order", "response", "apiData", "data", "imageList", "paginationData", "pagination", "Array", "isArray", "console", "total_pages", "err", "_err$response", "_err$response$data", "detail", "message", "updateDebouncedFilenameFilter", "handleFilenameChange", "event", "target", "handleRefresh", "prev", "handleSelectFilterChange", "setter", "handleRequestSort", "property", "isAsc", "handleOpenEditDialog", "image", "current<PERSON>wner", "ip_owner", "handleCloseEditDialog", "handleUpdateIpOwner", "trim", "_err$response2", "_err$response2$data", "handleOpenDeleteDialog", "imageId", "log", "handleCloseDeleteDialog", "handleDeleteImage", "_err$response3", "_err$response3$data", "handlePageChange", "sx", "mt", "children", "display", "justifyContent", "alignItems", "mb", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "onClick", "disabled", "p", "gutterBottom", "container", "spacing", "item", "xs", "sm", "fullWidth", "size", "onChange", "min<PERSON><PERSON><PERSON>", "labelId", "map", "option", "severity", "length", "component", "position", "top", "width", "head<PERSON>ell", "align", "padding", "sortDirection", "active", "direction", "colSpan", "hover", "border", "scope", "pl", "src", "image_id", "alt", "style", "height", "max<PERSON><PERSON><PERSON>", "objectFit", "verticalAlign", "onError", "color", "created_at", "fontSize", "count", "showFirstButton", "showLastButton", "open", "onClose", "autoFocus", "margin", "type", "_c", "$RefreshReg$"], "sources": ["D:/Documents/Programing/TRO/ModelTestsWorkbench/frontend/src/components/model-test-workbench/ImageBrowser.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableContainer,\r\n  TableHead,\r\n  TableRow,\r\n  Paper,\r\n  CircularProgress,\r\n  Alert,\r\n  IconButton,\r\n  Tooltip,\r\n  Dialog,\r\n  DialogActions,\r\n  DialogContent,\r\n  DialogContentText,\r\n  DialogTitle,\r\n  Button,\r\n  TextField,\r\n  Pagination,\r\n  Grid, // Added for layout\r\n  TableSortLabel, // Added for sorting\r\n  LinearProgress, // Added for subtle loading indication\r\n  Select,\r\n  MenuItem,\r\n  FormControl,\r\n  InputLabel,\r\n} from '@mui/material';\r\nimport EditIcon from '@mui/icons-material/Edit';\r\nimport DeleteIcon from '@mui/icons-material/Delete';\r\nimport RefreshIcon from '@mui/icons-material/Refresh';\r\nimport { visuallyHidden } from '@mui/utils'; // Added for sorting accessibility\r\nimport { listImages, updateImageIpOwner, deleteImage } from '../../services/api_model_workbench';\r\n\r\n// Helper to format date string\r\nconst formatDate = (dateString) => {\r\n  if (!dateString) return 'N/A';\r\n  try {\r\n    return new Date(dateString).toLocaleString();\r\n  } catch (e) {\r\n    return dateString; // Return original if parsing fails\r\n  }\r\n};\r\n\r\nconst ITEMS_PER_PAGE = 20;\r\n\r\nconst headCells = [\r\n  { id: 'thumbnail', numeric: false, disablePadding: true, label: 'Thumbnail', sortable: false },\r\n  { id: 'original_filename', numeric: false, disablePadding: false, label: 'Filename', sortable: true },\r\n  { id: 'image_type', numeric: false, disablePadding: false, label: 'Type', sortable: true },\r\n  { id: 'ip_category', numeric: false, disablePadding: false, label: 'IP', sortable: true },\r\n  { id: 'ip_owner', numeric: false, disablePadding: false, label: 'IP Owner', sortable: true },\r\n  { id: 'created_at', numeric: false, disablePadding: false, label: 'Created At', sortable: true },\r\n  { id: 'actions', numeric: false, disablePadding: false, label: 'Actions', sortable: false },\r\n];\r\n\r\n// --- Hardcoded Dropdown Options ---\r\nconst HARDCODED_IP_CATEGORIES = [\r\n  { value: '', label: 'All IP Categories' },\r\n  { value: 'trademark', label: 'Trademark' },\r\n  { value: 'copyright', label: 'Copyright' },\r\n  { value: 'patent', label: 'Patent' },\r\n];\r\n\r\nconst HARDCODED_IMAGE_TYPES = [\r\n  { value: '', label: 'All Image Types' },\r\n  { value: 'product', label: 'Product' },\r\n  { value: 'ip', label: 'IP' },\r\n];\r\n\r\n// Debounce utility function\r\nconst debounce = (func, delay) => {\r\n  let timeout;\r\n  return (...args) => {\r\n    clearTimeout(timeout);\r\n    timeout = setTimeout(() => func(...args), delay);\r\n  };\r\n};\r\n\r\nconst ImageBrowser = () => {\r\n  const [images, setImages] = useState([]);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState('');\r\n  const [refreshTrigger, setRefreshTrigger] = useState(0);\r\n\r\n  // Pagination state\r\n  const [totalPagesApi, setTotalPagesApi] = useState(0);   // API's total pages\r\n  const [currentPageUi, setCurrentPageUi] = useState(1);   // UI's current page (for Pagination component)\r\n\r\n  // State for Edit Dialog\r\n  const [editDialogOpen, setEditDialogOpen] = useState(false);\r\n  const [editingImage, setEditingImage] = useState(null);\r\n  const [newIpOwner, setNewIpOwner] = useState('');\r\n  const [editLoading, setEditLoading] = useState(false);\r\n  const [editError, setEditError] = useState('');\r\n\r\n  // State for Delete Dialog\r\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\r\n  const [deletingImageId, setDeletingImageId] = useState(null);\r\n  const [deleteLoading, setDeleteLoading] = useState(false);\r\n  const [deleteError, setDeleteError] = useState('');\r\n\r\n  // Filter state\r\n  const [filenameFilter, setFilenameFilter] = useState(''); // For immediate input display\r\n  const [debouncedFilenameFilter, setDebouncedFilenameFilter] = useState(''); // For API calls\r\n  const [ipFilter, setIpFilter] = useState('');\r\n  const [typeFilter, setTypeFilter] = useState('');\r\n\r\n  // State for dropdown options (now using hardcoded values)\r\n  const ipCategoryOptions = HARDCODED_IP_CATEGORIES;\r\n  const imageTypeOptions = HARDCODED_IMAGE_TYPES;\r\n\r\n  // Sorting state\r\n  const [order, setOrder] = useState('desc');\r\n  const [orderBy, setOrderBy] = useState('created_at');\r\n\r\n\r\n  const fetchData = useCallback(async (page, currentFilters, currentSortConfig) => {\r\n    setIsLoading(true);\r\n    setError('');\r\n    try {\r\n      const params = {\r\n        page,\r\n        per_page: ITEMS_PER_PAGE,\r\n        original_filename: currentFilters.debouncedFilenameFilter || undefined, // Use debounced value\r\n        ip_category: currentFilters.ipFilter || undefined,\r\n        image_type: currentFilters.typeFilter || undefined,\r\n        sort_by: currentSortConfig.orderBy || undefined,\r\n        sort_order: currentSortConfig.orderBy ? (currentSortConfig.order === 'desc' ? 'desc' : 'asc') : undefined,\r\n      };\r\n      // console.log(\"ImageBrowser: Fetching data with params:\", params);\r\n      const response = await listImages(params);\r\n      // console.log('ImageBrowser API response.data:', response.data);\r\n      const apiData = response.data;\r\n      if (apiData && typeof apiData === 'object') {\r\n        const imageList = apiData.images;\r\n        const paginationData = apiData.pagination;\r\n\r\n        if (Array.isArray(imageList)) {\r\n          setImages(imageList);\r\n        } else {\r\n          console.error('ImageBrowser: response.data.images is not an array. Received:', imageList);\r\n          setImages([]);\r\n        }\r\n\r\n        if (paginationData && typeof paginationData === 'object') {\r\n          setTotalPagesApi(paginationData.total_pages || 0);\r\n        } else {\r\n          console.error('ImageBrowser: response.data.pagination is not a valid object or is missing. Received:', paginationData);\r\n          setTotalPagesApi(0);\r\n        }\r\n      } else {\r\n        console.error('ImageBrowser: response.data is not a valid object or is missing. Received:', apiData);\r\n        setImages([]);\r\n        setTotalPagesApi(0);\r\n      }\r\n    } catch (err) {\r\n      console.error(\"ImageBrowser: Failed to fetch images. Error object:\", err);\r\n      setError(err.response?.data?.detail || err.message || 'Failed to fetch images.');\r\n      setImages([]);\r\n      setTotalPagesApi(0);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, []); // fetchData itself doesn't depend on changing state, its callers do.\r\n\r\n  useEffect(() => {\r\n    // Use debouncedFilenameFilter for the API call\r\n    const currentFilters = { debouncedFilenameFilter, ipFilter, typeFilter };\r\n    const currentSortConfig = { orderBy, order };\r\n    fetchData(currentPageUi, currentFilters, currentSortConfig);\r\n  }, [fetchData, refreshTrigger, currentPageUi, debouncedFilenameFilter, ipFilter, typeFilter, orderBy, order]);\r\n\r\n\r\n  // Debounced function to update the filter used for API calls\r\n  // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  const updateDebouncedFilenameFilter = useCallback(\r\n    debounce((value) => {\r\n      setDebouncedFilenameFilter(value);\r\n      setCurrentPageUi(1); // Reset to first page when debounced search term changes\r\n    }, 500), // 500ms delay\r\n    []\r\n  );\r\n\r\n  const handleFilenameChange = (event) => {\r\n    const { value } = event.target;\r\n    setFilenameFilter(value); // Update input field immediately\r\n    updateDebouncedFilenameFilter(value); // Call debounced function to update API filter\r\n  };\r\n\r\n  const handleRefresh = () => {\r\n    setRefreshTrigger(prev => prev + 1);\r\n  };\r\n\r\n  // Specific handler for IP and Type filters (non-debounced)\r\n  const handleSelectFilterChange = (setter) => (event) => {\r\n    setter(event.target.value);\r\n    setCurrentPageUi(1); // Reset to first page on filter change\r\n  };\r\n\r\n\r\n  const handleRequestSort = (event, property) => {\r\n    const isAsc = orderBy === property && order === 'asc';\r\n    setOrder(isAsc ? 'desc' : 'asc');\r\n    setOrderBy(property);\r\n    setCurrentPageUi(1); // Reset to first page on sort change\r\n  };\r\n\r\n  // --- Edit IP Owner Logic ---\r\n  const handleOpenEditDialog = (image) => {\r\n    setEditingImage({ id: image.id, currentOwner: image.ip_owner });\r\n    setNewIpOwner(image.ip_owner || '');\r\n    setEditError('');\r\n    setEditDialogOpen(true);\r\n  };\r\n\r\n  const handleCloseEditDialog = () => {\r\n    setEditDialogOpen(false);\r\n    setEditingImage(null);\r\n    setNewIpOwner('');\r\n    setEditError('');\r\n  };\r\n\r\n  const handleUpdateIpOwner = async () => {\r\n    if (!editingImage || !newIpOwner.trim()) {\r\n      setEditError('IP Owner cannot be empty.');\r\n      return;\r\n    }\r\n    setEditLoading(true);\r\n    setEditError('');\r\n    try {\r\n      await updateImageIpOwner(editingImage.id, newIpOwner.trim());\r\n      handleCloseEditDialog();\r\n      handleRefresh();\r\n    } catch (err) {\r\n      console.error(\"Failed to update IP owner:\", err);\r\n      setEditError(err.response?.data?.detail || err.message || 'Failed to update IP owner.');\r\n    } finally {\r\n      setEditLoading(false);\r\n    }\r\n  };\r\n\r\n  // --- Delete Image Logic ---\r\n  const handleOpenDeleteDialog = (imageId) => {\r\n    console.log(\"handleOpenDeleteDialog entered. imageId:\", imageId);\r\n    setDeletingImageId(imageId);\r\n    setDeleteError('');\r\n    setDeleteDialogOpen(true);\r\n  };\r\n\r\n  const handleCloseDeleteDialog = () => {\r\n    setDeleteDialogOpen(false);\r\n    setDeletingImageId(null);\r\n    setDeleteError('');\r\n  };\r\n\r\n  const handleDeleteImage = async () => {\r\n    console.log(\"handleDeleteImage entered. deletingImageId:\", deletingImageId);\r\n    if (!deletingImageId) return;\r\n    setDeleteLoading(true);\r\n    setDeleteError('');\r\n    try {\r\n      console.log(`Attempting to delete image with ID: ${deletingImageId}`);\r\n      await deleteImage(deletingImageId);\r\n      console.log(`Delete API call successful for image ID: ${deletingImageId}`);\r\n      handleCloseDeleteDialog();\r\n      handleRefresh();\r\n    } catch (err) {\r\n      console.error(`Delete API call failed for image ID: ${deletingImageId}`, err);\r\n      setDeleteError(err.response?.data?.detail || err.message || 'Failed to delete image.');\r\n    } finally {\r\n      setDeleteLoading(false);\r\n    }\r\n  };\r\n\r\n  const handlePageChange = (event, value) => {\r\n    setCurrentPageUi(value);\r\n  };\r\n\r\n  return (\r\n    <Box sx={{ mt: 4 }}>\r\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\r\n        <Typography variant=\"h6\">Image Browser & Metadata</Typography>\r\n        <Tooltip title=\"Refresh List\">\r\n          <span>\r\n            <IconButton onClick={handleRefresh} disabled={isLoading}>\r\n              <RefreshIcon />\r\n            </IconButton>\r\n          </span>\r\n        </Tooltip>\r\n      </Box>\r\n\r\n      {/* Filter Inputs */}\r\n      <Paper sx={{ p: 2, mb: 2 }}>\r\n        <Typography variant=\"subtitle1\" gutterBottom>Filters</Typography>\r\n        <Grid container spacing={2}>\r\n          <Grid item xs={12} sm={6}>\r\n            <TextField\r\n              fullWidth\r\n              label=\"Filename\"\r\n              variant=\"outlined\"\r\n              size=\"small\"\r\n              value={filenameFilter} // TextField displays the immediate value\r\n              onChange={handleFilenameChange} // Use the new handler for debouncing\r\n              disabled={isLoading}\r\n            />\r\n          </Grid>\r\n          <Grid item xs={12} sm={3}>\r\n            <FormControl fullWidth size=\"small\" variant=\"outlined\" disabled={isLoading} sx={{ minWidth: '160px' }}>\r\n              <InputLabel id=\"ip-category-filter-label\">IP Category</InputLabel>\r\n              <Select\r\n                labelId=\"ip-category-filter-label\"\r\n                id=\"ip-category-filter\"\r\n                value={ipFilter}\r\n                label=\"IP Category\"\r\n                onChange={handleSelectFilterChange(setIpFilter)} // Use non-debounced for select\r\n              >\r\n                {ipCategoryOptions.map(option => (\r\n                  <MenuItem key={option.value} value={option.value}>\r\n                    {option.label}\r\n                  </MenuItem>\r\n                ))}\r\n              </Select>\r\n            </FormControl>\r\n          </Grid>\r\n          <Grid item xs={12} sm={3}>\r\n            <FormControl fullWidth size=\"small\" variant=\"outlined\" disabled={isLoading} sx={{ minWidth: '160px' }}>\r\n              <InputLabel id=\"image-type-filter-label\">Image Type</InputLabel>\r\n              <Select\r\n                labelId=\"image-type-filter-label\"\r\n                id=\"image-type-filter\"\r\n                value={typeFilter}\r\n                label=\"Image Type\"\r\n                onChange={handleSelectFilterChange(setTypeFilter)} // Use non-debounced for select\r\n              >\r\n                {imageTypeOptions.map(option => (\r\n                  <MenuItem key={option.value} value={option.value}>\r\n                    {option.label}\r\n                  </MenuItem>\r\n                ))}\r\n              </Select>\r\n            </FormControl>\r\n          </Grid>\r\n        </Grid>\r\n      </Paper>\r\n\r\n      {error && <Alert severity=\"error\" sx={{ mb: 2 }}>{error}</Alert>}\r\n\r\n      {isLoading && !images.length ? ( // Show main loader only if no images are displayed yet\r\n        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>\r\n          <CircularProgress />\r\n        </Box>\r\n      ) : (\r\n        <>\r\n          <TableContainer component={Paper} sx={{ position: 'relative' }}>\r\n            {isLoading && <LinearProgress sx={{position: 'absolute', top: 0, width: '100%'}} /> }\r\n            <Table sx={{ minWidth: 650 }} aria-label=\"image browser table\" size=\"small\">\r\n              <TableHead>\r\n                <TableRow>\r\n                  {headCells.map((headCell) => (\r\n                    <TableCell\r\n                      key={headCell.id}\r\n                      align={headCell.numeric ? 'right' : 'left'}\r\n                      padding={headCell.disablePadding ? 'none' : 'normal'}\r\n                      sortDirection={orderBy === headCell.id ? order : false}\r\n                    >\r\n                      {headCell.sortable ? (\r\n                        <TableSortLabel\r\n                          active={orderBy === headCell.id}\r\n                          direction={orderBy === headCell.id ? order : 'asc'}\r\n                          onClick={(event) => handleRequestSort(event, headCell.id)}\r\n                          disabled={isLoading}\r\n                        >\r\n                          {headCell.label}\r\n                          {orderBy === headCell.id ? (\r\n                            <Box component=\"span\" sx={visuallyHidden}>\r\n                              {order === 'desc' ? 'sorted descending' : 'sorted ascending'}\r\n                            </Box>\r\n                          ) : null}\r\n                        </TableSortLabel>\r\n                      ) : (\r\n                        headCell.label\r\n                      )}\r\n                    </TableCell>\r\n                  ))}\r\n                </TableRow>\r\n              </TableHead>\r\n              <TableBody>\r\n                {images.length === 0 && !isLoading && (\r\n                  <TableRow>\r\n                    <TableCell colSpan={headCells.length} align=\"center\">No images found.</TableCell>\r\n                  </TableRow>\r\n                )}\r\n                {images.map((image) => (\r\n                  <TableRow\r\n                    hover\r\n                    key={image.image_id || image.id} // Use image.id as fallback if image_id is missing\r\n                    sx={{ '&:last-child td, &:last-child th': { border: 0 } }}\r\n                  >\r\n                    <TableCell component=\"th\" scope=\"row\" padding=\"none\" sx={{pl: '6px'}}>\r\n                      <img\r\n                        src={`/api/data/images/file/${image.image_id || image.id}`}\r\n                        alt={image.original_filename}\r\n                        style={{ height: '50px', width: 'auto', maxWidth: '70px', objectFit: 'contain', verticalAlign: 'middle', padding: '4px 0' }}\r\n                        onError={(e) => { e.target.style.display = 'none'; }}\r\n                      />\r\n                    </TableCell>\r\n                    <TableCell>{image.original_filename}</TableCell>\r\n                    <TableCell>{image.image_type || 'N/A'}</TableCell>\r\n                    <TableCell>{image.ip_category || 'N/A'}</TableCell>\r\n                    <TableCell>\r\n                      {image.ip_owner || <Typography variant=\"caption\" color=\"textSecondary\">(Missing)</Typography>}\r\n                    </TableCell>\r\n                    <TableCell>{formatDate(image.created_at)}</TableCell>\r\n                    <TableCell>\r\n                      <Tooltip title=\"Edit IP Owner\">\r\n                        <span>\r\n                          <IconButton size=\"small\" onClick={() => handleOpenEditDialog(image)} disabled={editLoading || deleteLoading || isLoading}>\r\n                            <EditIcon fontSize=\"small\" />\r\n                          </IconButton>\r\n                        </span>\r\n                      </Tooltip>\r\n                      <Tooltip title=\"Delete Image\">\r\n                        <span>\r\n                          <IconButton size=\"small\" onClick={() => handleOpenDeleteDialog(image.image_id)} disabled={editLoading || deleteLoading || isLoading}>\r\n                            <DeleteIcon fontSize=\"small\" />\r\n                          </IconButton>\r\n                        </span>\r\n                      </Tooltip>\r\n                    </TableCell>\r\n                  </TableRow>\r\n                ))}\r\n              </TableBody>\r\n            </Table>\r\n          </TableContainer>\r\n\r\n          {totalPagesApi > 1 && (\r\n            <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>\r\n              <Pagination\r\n                count={totalPagesApi}\r\n                page={currentPageUi}\r\n                onChange={handlePageChange}\r\n                color=\"primary\"\r\n                showFirstButton\r\n                showLastButton\r\n                disabled={isLoading}\r\n              />\r\n            </Box>\r\n          )}\r\n        </>\r\n      )}\r\n\r\n      {/* Edit IP Owner Dialog */}\r\n      <Dialog open={editDialogOpen} onClose={handleCloseEditDialog}>\r\n        <DialogTitle>Edit IP Owner</DialogTitle>\r\n        <DialogContent>\r\n          <DialogContentText sx={{ mb: 2 }}>\r\n            Enter the IP Owner for the image: {editingImage?.id}\r\n          </DialogContentText>\r\n          {editError && <Alert severity=\"error\" sx={{ mb: 2 }}>{editError}</Alert>}\r\n          <TextField\r\n            autoFocus\r\n            margin=\"dense\"\r\n            id=\"ip_owner\"\r\n            label=\"IP Owner\"\r\n            type=\"text\"\r\n            fullWidth\r\n            variant=\"standard\"\r\n            value={newIpOwner}\r\n            onChange={(e) => setNewIpOwner(e.target.value)}\r\n            disabled={editLoading}\r\n          />\r\n        </DialogContent>\r\n        <DialogActions>\r\n          <Button onClick={handleCloseEditDialog} disabled={editLoading}>Cancel</Button>\r\n          <Button onClick={handleUpdateIpOwner} disabled={editLoading || !newIpOwner.trim()}>\r\n            {editLoading ? <CircularProgress size={20} /> : 'Save'}\r\n          </Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n\r\n      {/* Delete Confirmation Dialog */}\r\n      <Dialog\r\n        open={deleteDialogOpen}\r\n        onClose={handleCloseDeleteDialog}\r\n        aria-labelledby=\"alert-dialog-title\"\r\n        aria-describedby=\"alert-dialog-description\"\r\n      >\r\n        <DialogTitle id=\"alert-dialog-title\">{\"Confirm Deletion\"}</DialogTitle>\r\n        <DialogContent>\r\n          {deleteError && <Alert severity=\"error\" sx={{ mb: 2 }}>{deleteError}</Alert>}\r\n          <DialogContentText id=\"alert-dialog-description\">\r\n            Warning: Deleting this image will permanently remove its data and any associated features or comparison results. This action cannot be undone. Are you sure you want to proceed?\r\n          </DialogContentText>\r\n        </DialogContent>\r\n        <DialogActions>\r\n          <Button onClick={handleCloseDeleteDialog} disabled={deleteLoading}>\r\n            Cancel\r\n          </Button>\r\n          <Button onClick={handleDeleteImage} color=\"error\" autoFocus disabled={deleteLoading}>\r\n            {deleteLoading ? <CircularProgress size={20} /> : 'Delete'}\r\n          </Button>\r\n        </DialogActions>\r\n      </Dialog>\r\n\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default ImageBrowser;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,gBAAgB,EAChBC,KAAK,EACLC,UAAU,EACVC,OAAO,EACPC,MAAM,EACNC,aAAa,EACbC,aAAa,EACbC,iBAAiB,EACjBC,WAAW,EACXC,MAAM,EACNC,SAAS,EACTC,UAAU,EACVC,IAAI;AAAE;AACNC,cAAc;AAAE;AAChBC,cAAc;AAAE;AAChBC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,UAAU,QACL,eAAe;AACtB,OAAOC,QAAQ,MAAM,0BAA0B;AAC/C,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,WAAW,MAAM,6BAA6B;AACrD,SAASC,cAAc,QAAQ,YAAY,CAAC,CAAC;AAC7C,SAASC,UAAU,EAAEC,kBAAkB,EAAEC,WAAW,QAAQ,oCAAoC;;AAEhG;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,UAAU,GAAIC,UAAU,IAAK;EACjC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;EAC7B,IAAI;IACF,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,cAAc,CAAC,CAAC;EAC9C,CAAC,CAAC,OAAOC,CAAC,EAAE;IACV,OAAOH,UAAU,CAAC,CAAC;EACrB;AACF,CAAC;AAED,MAAMI,cAAc,GAAG,EAAE;AAEzB,MAAMC,SAAS,GAAG,CAChB;EAAEC,EAAE,EAAE,WAAW;EAAEC,OAAO,EAAE,KAAK;EAAEC,cAAc,EAAE,IAAI;EAAEC,KAAK,EAAE,WAAW;EAAEC,QAAQ,EAAE;AAAM,CAAC,EAC9F;EAAEJ,EAAE,EAAE,mBAAmB;EAAEC,OAAO,EAAE,KAAK;EAAEC,cAAc,EAAE,KAAK;EAAEC,KAAK,EAAE,UAAU;EAAEC,QAAQ,EAAE;AAAK,CAAC,EACrG;EAAEJ,EAAE,EAAE,YAAY;EAAEC,OAAO,EAAE,KAAK;EAAEC,cAAc,EAAE,KAAK;EAAEC,KAAK,EAAE,MAAM;EAAEC,QAAQ,EAAE;AAAK,CAAC,EAC1F;EAAEJ,EAAE,EAAE,aAAa;EAAEC,OAAO,EAAE,KAAK;EAAEC,cAAc,EAAE,KAAK;EAAEC,KAAK,EAAE,IAAI;EAAEC,QAAQ,EAAE;AAAK,CAAC,EACzF;EAAEJ,EAAE,EAAE,UAAU;EAAEC,OAAO,EAAE,KAAK;EAAEC,cAAc,EAAE,KAAK;EAAEC,KAAK,EAAE,UAAU;EAAEC,QAAQ,EAAE;AAAK,CAAC,EAC5F;EAAEJ,EAAE,EAAE,YAAY;EAAEC,OAAO,EAAE,KAAK;EAAEC,cAAc,EAAE,KAAK;EAAEC,KAAK,EAAE,YAAY;EAAEC,QAAQ,EAAE;AAAK,CAAC,EAChG;EAAEJ,EAAE,EAAE,SAAS;EAAEC,OAAO,EAAE,KAAK;EAAEC,cAAc,EAAE,KAAK;EAAEC,KAAK,EAAE,SAAS;EAAEC,QAAQ,EAAE;AAAM,CAAC,CAC5F;;AAED;AACA,MAAMC,uBAAuB,GAAG,CAC9B;EAAEC,KAAK,EAAE,EAAE;EAAEH,KAAK,EAAE;AAAoB,CAAC,EACzC;EAAEG,KAAK,EAAE,WAAW;EAAEH,KAAK,EAAE;AAAY,CAAC,EAC1C;EAAEG,KAAK,EAAE,WAAW;EAAEH,KAAK,EAAE;AAAY,CAAC,EAC1C;EAAEG,KAAK,EAAE,QAAQ;EAAEH,KAAK,EAAE;AAAS,CAAC,CACrC;AAED,MAAMI,qBAAqB,GAAG,CAC5B;EAAED,KAAK,EAAE,EAAE;EAAEH,KAAK,EAAE;AAAkB,CAAC,EACvC;EAAEG,KAAK,EAAE,SAAS;EAAEH,KAAK,EAAE;AAAU,CAAC,EACtC;EAAEG,KAAK,EAAE,IAAI;EAAEH,KAAK,EAAE;AAAK,CAAC,CAC7B;;AAED;AACA,MAAMK,QAAQ,GAAGA,CAACC,IAAI,EAAEC,KAAK,KAAK;EAChC,IAAIC,OAAO;EACX,OAAO,CAAC,GAAGC,IAAI,KAAK;IAClBC,YAAY,CAACF,OAAO,CAAC;IACrBA,OAAO,GAAGG,UAAU,CAAC,MAAML,IAAI,CAAC,GAAGG,IAAI,CAAC,EAAEF,KAAK,CAAC;EAClD,CAAC;AACH,CAAC;AAED,MAAMK,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGnE,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACoE,SAAS,EAAEC,YAAY,CAAC,GAAGrE,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACsE,KAAK,EAAEC,QAAQ,CAAC,GAAGvE,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACwE,cAAc,EAAEC,iBAAiB,CAAC,GAAGzE,QAAQ,CAAC,CAAC,CAAC;;EAEvD;EACA,MAAM,CAAC0E,aAAa,EAAEC,gBAAgB,CAAC,GAAG3E,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAG;EACzD,MAAM,CAAC4E,aAAa,EAAEC,gBAAgB,CAAC,GAAG7E,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAG;;EAEzD;EACA,MAAM,CAAC8E,cAAc,EAAEC,iBAAiB,CAAC,GAAG/E,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACgF,YAAY,EAAEC,eAAe,CAAC,GAAGjF,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACkF,UAAU,EAAEC,aAAa,CAAC,GAAGnF,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACoF,WAAW,EAAEC,cAAc,CAAC,GAAGrF,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACsF,SAAS,EAAEC,YAAY,CAAC,GAAGvF,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACA,MAAM,CAACwF,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzF,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC0F,eAAe,EAAEC,kBAAkB,CAAC,GAAG3F,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC4F,aAAa,EAAEC,gBAAgB,CAAC,GAAG7F,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC8F,WAAW,EAAEC,cAAc,CAAC,GAAG/F,QAAQ,CAAC,EAAE,CAAC;;EAElD;EACA,MAAM,CAACgG,cAAc,EAAEC,iBAAiB,CAAC,GAAGjG,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACkG,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGnG,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAC5E,MAAM,CAACoG,QAAQ,EAAEC,WAAW,CAAC,GAAGrG,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACsG,UAAU,EAAEC,aAAa,CAAC,GAAGvG,QAAQ,CAAC,EAAE,CAAC;;EAEhD;EACA,MAAMwG,iBAAiB,GAAGlD,uBAAuB;EACjD,MAAMmD,gBAAgB,GAAGjD,qBAAqB;;EAE9C;EACA,MAAM,CAACkD,KAAK,EAAEC,QAAQ,CAAC,GAAG3G,QAAQ,CAAC,MAAM,CAAC;EAC1C,MAAM,CAAC4G,OAAO,EAAEC,UAAU,CAAC,GAAG7G,QAAQ,CAAC,YAAY,CAAC;EAGpD,MAAM8G,SAAS,GAAG5G,WAAW,CAAC,OAAO6G,IAAI,EAAEC,cAAc,EAAEC,iBAAiB,KAAK;IAC/E5C,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,EAAE,CAAC;IACZ,IAAI;MACF,MAAM2C,MAAM,GAAG;QACbH,IAAI;QACJI,QAAQ,EAAEpE,cAAc;QACxBqE,iBAAiB,EAAEJ,cAAc,CAACd,uBAAuB,IAAImB,SAAS;QAAE;QACxEC,WAAW,EAAEN,cAAc,CAACZ,QAAQ,IAAIiB,SAAS;QACjDE,UAAU,EAAEP,cAAc,CAACV,UAAU,IAAIe,SAAS;QAClDG,OAAO,EAAEP,iBAAiB,CAACL,OAAO,IAAIS,SAAS;QAC/CI,UAAU,EAAER,iBAAiB,CAACL,OAAO,GAAIK,iBAAiB,CAACP,KAAK,KAAK,MAAM,GAAG,MAAM,GAAG,KAAK,GAAIW;MAClG,CAAC;MACD;MACA,MAAMK,QAAQ,GAAG,MAAMvF,UAAU,CAAC+E,MAAM,CAAC;MACzC;MACA,MAAMS,OAAO,GAAGD,QAAQ,CAACE,IAAI;MAC7B,IAAID,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;QAC1C,MAAME,SAAS,GAAGF,OAAO,CAACzD,MAAM;QAChC,MAAM4D,cAAc,GAAGH,OAAO,CAACI,UAAU;QAEzC,IAAIC,KAAK,CAACC,OAAO,CAACJ,SAAS,CAAC,EAAE;UAC5B1D,SAAS,CAAC0D,SAAS,CAAC;QACtB,CAAC,MAAM;UACLK,OAAO,CAAC5D,KAAK,CAAC,+DAA+D,EAAEuD,SAAS,CAAC;UACzF1D,SAAS,CAAC,EAAE,CAAC;QACf;QAEA,IAAI2D,cAAc,IAAI,OAAOA,cAAc,KAAK,QAAQ,EAAE;UACxDnD,gBAAgB,CAACmD,cAAc,CAACK,WAAW,IAAI,CAAC,CAAC;QACnD,CAAC,MAAM;UACLD,OAAO,CAAC5D,KAAK,CAAC,uFAAuF,EAAEwD,cAAc,CAAC;UACtHnD,gBAAgB,CAAC,CAAC,CAAC;QACrB;MACF,CAAC,MAAM;QACLuD,OAAO,CAAC5D,KAAK,CAAC,4EAA4E,EAAEqD,OAAO,CAAC;QACpGxD,SAAS,CAAC,EAAE,CAAC;QACbQ,gBAAgB,CAAC,CAAC,CAAC;MACrB;IACF,CAAC,CAAC,OAAOyD,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACZJ,OAAO,CAAC5D,KAAK,CAAC,qDAAqD,EAAE8D,GAAG,CAAC;MACzE7D,QAAQ,CAAC,EAAA8D,aAAA,GAAAD,GAAG,CAACV,QAAQ,cAAAW,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcT,IAAI,cAAAU,kBAAA,uBAAlBA,kBAAA,CAAoBC,MAAM,KAAIH,GAAG,CAACI,OAAO,IAAI,yBAAyB,CAAC;MAChFrE,SAAS,CAAC,EAAE,CAAC;MACbQ,gBAAgB,CAAC,CAAC,CAAC;IACrB,CAAC,SAAS;MACRN,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAERpE,SAAS,CAAC,MAAM;IACd;IACA,MAAM+G,cAAc,GAAG;MAAEd,uBAAuB;MAAEE,QAAQ;MAAEE;IAAW,CAAC;IACxE,MAAMW,iBAAiB,GAAG;MAAEL,OAAO;MAAEF;IAAM,CAAC;IAC5CI,SAAS,CAAClC,aAAa,EAAEoC,cAAc,EAAEC,iBAAiB,CAAC;EAC7D,CAAC,EAAE,CAACH,SAAS,EAAEtC,cAAc,EAAEI,aAAa,EAAEsB,uBAAuB,EAAEE,QAAQ,EAAEE,UAAU,EAAEM,OAAO,EAAEF,KAAK,CAAC,CAAC;;EAG7G;EACA;EACA,MAAM+B,6BAA6B,GAAGvI,WAAW,CAC/CuD,QAAQ,CAAEF,KAAK,IAAK;IAClB4C,0BAA0B,CAAC5C,KAAK,CAAC;IACjCsB,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC,EAAE,GAAG,CAAC;EAAE;EACT,EACF,CAAC;EAED,MAAM6D,oBAAoB,GAAIC,KAAK,IAAK;IACtC,MAAM;MAAEpF;IAAM,CAAC,GAAGoF,KAAK,CAACC,MAAM;IAC9B3C,iBAAiB,CAAC1C,KAAK,CAAC,CAAC,CAAC;IAC1BkF,6BAA6B,CAAClF,KAAK,CAAC,CAAC,CAAC;EACxC,CAAC;EAED,MAAMsF,aAAa,GAAGA,CAAA,KAAM;IAC1BpE,iBAAiB,CAACqE,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;EACrC,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAIC,MAAM,IAAML,KAAK,IAAK;IACtDK,MAAM,CAACL,KAAK,CAACC,MAAM,CAACrF,KAAK,CAAC;IAC1BsB,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC;EAGD,MAAMoE,iBAAiB,GAAGA,CAACN,KAAK,EAAEO,QAAQ,KAAK;IAC7C,MAAMC,KAAK,GAAGvC,OAAO,KAAKsC,QAAQ,IAAIxC,KAAK,KAAK,KAAK;IACrDC,QAAQ,CAACwC,KAAK,GAAG,MAAM,GAAG,KAAK,CAAC;IAChCtC,UAAU,CAACqC,QAAQ,CAAC;IACpBrE,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;EACvB,CAAC;;EAED;EACA,MAAMuE,oBAAoB,GAAIC,KAAK,IAAK;IACtCpE,eAAe,CAAC;MAAEhC,EAAE,EAAEoG,KAAK,CAACpG,EAAE;MAAEqG,YAAY,EAAED,KAAK,CAACE;IAAS,CAAC,CAAC;IAC/DpE,aAAa,CAACkE,KAAK,CAACE,QAAQ,IAAI,EAAE,CAAC;IACnChE,YAAY,CAAC,EAAE,CAAC;IAChBR,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;EAED,MAAMyE,qBAAqB,GAAGA,CAAA,KAAM;IAClCzE,iBAAiB,CAAC,KAAK,CAAC;IACxBE,eAAe,CAAC,IAAI,CAAC;IACrBE,aAAa,CAAC,EAAE,CAAC;IACjBI,YAAY,CAAC,EAAE,CAAC;EAClB,CAAC;EAED,MAAMkE,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI,CAACzE,YAAY,IAAI,CAACE,UAAU,CAACwE,IAAI,CAAC,CAAC,EAAE;MACvCnE,YAAY,CAAC,2BAA2B,CAAC;MACzC;IACF;IACAF,cAAc,CAAC,IAAI,CAAC;IACpBE,YAAY,CAAC,EAAE,CAAC;IAChB,IAAI;MACF,MAAMnD,kBAAkB,CAAC4C,YAAY,CAAC/B,EAAE,EAAEiC,UAAU,CAACwE,IAAI,CAAC,CAAC,CAAC;MAC5DF,qBAAqB,CAAC,CAAC;MACvBX,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOT,GAAG,EAAE;MAAA,IAAAuB,cAAA,EAAAC,mBAAA;MACZ1B,OAAO,CAAC5D,KAAK,CAAC,4BAA4B,EAAE8D,GAAG,CAAC;MAChD7C,YAAY,CAAC,EAAAoE,cAAA,GAAAvB,GAAG,CAACV,QAAQ,cAAAiC,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc/B,IAAI,cAAAgC,mBAAA,uBAAlBA,mBAAA,CAAoBrB,MAAM,KAAIH,GAAG,CAACI,OAAO,IAAI,4BAA4B,CAAC;IACzF,CAAC,SAAS;MACRnD,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMwE,sBAAsB,GAAIC,OAAO,IAAK;IAC1C5B,OAAO,CAAC6B,GAAG,CAAC,0CAA0C,EAAED,OAAO,CAAC;IAChEnE,kBAAkB,CAACmE,OAAO,CAAC;IAC3B/D,cAAc,CAAC,EAAE,CAAC;IAClBN,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMuE,uBAAuB,GAAGA,CAAA,KAAM;IACpCvE,mBAAmB,CAAC,KAAK,CAAC;IAC1BE,kBAAkB,CAAC,IAAI,CAAC;IACxBI,cAAc,CAAC,EAAE,CAAC;EACpB,CAAC;EAED,MAAMkE,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC/B,OAAO,CAAC6B,GAAG,CAAC,6CAA6C,EAAErE,eAAe,CAAC;IAC3E,IAAI,CAACA,eAAe,EAAE;IACtBG,gBAAgB,CAAC,IAAI,CAAC;IACtBE,cAAc,CAAC,EAAE,CAAC;IAClB,IAAI;MACFmC,OAAO,CAAC6B,GAAG,CAAC,uCAAuCrE,eAAe,EAAE,CAAC;MACrE,MAAMrD,WAAW,CAACqD,eAAe,CAAC;MAClCwC,OAAO,CAAC6B,GAAG,CAAC,4CAA4CrE,eAAe,EAAE,CAAC;MAC1EsE,uBAAuB,CAAC,CAAC;MACzBnB,aAAa,CAAC,CAAC;IACjB,CAAC,CAAC,OAAOT,GAAG,EAAE;MAAA,IAAA8B,cAAA,EAAAC,mBAAA;MACZjC,OAAO,CAAC5D,KAAK,CAAC,wCAAwCoB,eAAe,EAAE,EAAE0C,GAAG,CAAC;MAC7ErC,cAAc,CAAC,EAAAmE,cAAA,GAAA9B,GAAG,CAACV,QAAQ,cAAAwC,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAActC,IAAI,cAAAuC,mBAAA,uBAAlBA,mBAAA,CAAoB5B,MAAM,KAAIH,GAAG,CAACI,OAAO,IAAI,yBAAyB,CAAC;IACxF,CAAC,SAAS;MACR3C,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC;EAED,MAAMuE,gBAAgB,GAAGA,CAACzB,KAAK,EAAEpF,KAAK,KAAK;IACzCsB,gBAAgB,CAACtB,KAAK,CAAC;EACzB,CAAC;EAED,oBACEhB,OAAA,CAACpC,GAAG;IAACkK,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBACjBhI,OAAA,CAACpC,GAAG;MAACkK,EAAE,EAAE;QAAEG,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACzFhI,OAAA,CAACnC,UAAU;QAACwK,OAAO,EAAC,IAAI;QAAAL,QAAA,EAAC;MAAwB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC9DzI,OAAA,CAACxB,OAAO;QAACkK,KAAK,EAAC,cAAc;QAAAV,QAAA,eAC3BhI,OAAA;UAAAgI,QAAA,eACEhI,OAAA,CAACzB,UAAU;YAACoK,OAAO,EAAErC,aAAc;YAACsC,QAAQ,EAAE/G,SAAU;YAAAmG,QAAA,eACtDhI,OAAA,CAACN,WAAW;cAAA4I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAGNzI,OAAA,CAAC5B,KAAK;MAAC0J,EAAE,EAAE;QAAEe,CAAC,EAAE,CAAC;QAAET,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBACzBhI,OAAA,CAACnC,UAAU;QAACwK,OAAO,EAAC,WAAW;QAACS,YAAY;QAAAd,QAAA,EAAC;MAAO;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACjEzI,OAAA,CAACf,IAAI;QAAC8J,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAhB,QAAA,gBACzBhI,OAAA,CAACf,IAAI;UAACgK,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAnB,QAAA,eACvBhI,OAAA,CAACjB,SAAS;YACRqK,SAAS;YACTvI,KAAK,EAAC,UAAU;YAChBwH,OAAO,EAAC,UAAU;YAClBgB,IAAI,EAAC,OAAO;YACZrI,KAAK,EAAEyC,cAAe,CAAC;YAAA;YACvB6F,QAAQ,EAAEnD,oBAAqB,CAAC;YAAA;YAChCyC,QAAQ,EAAE/G;UAAU;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACPzI,OAAA,CAACf,IAAI;UAACgK,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAnB,QAAA,eACvBhI,OAAA,CAACV,WAAW;YAAC8J,SAAS;YAACC,IAAI,EAAC,OAAO;YAAChB,OAAO,EAAC,UAAU;YAACO,QAAQ,EAAE/G,SAAU;YAACiG,EAAE,EAAE;cAAEyB,QAAQ,EAAE;YAAQ,CAAE;YAAAvB,QAAA,gBACpGhI,OAAA,CAACT,UAAU;cAACmB,EAAE,EAAC,0BAA0B;cAAAsH,QAAA,EAAC;YAAW;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAClEzI,OAAA,CAACZ,MAAM;cACLoK,OAAO,EAAC,0BAA0B;cAClC9I,EAAE,EAAC,oBAAoB;cACvBM,KAAK,EAAE6C,QAAS;cAChBhD,KAAK,EAAC,aAAa;cACnByI,QAAQ,EAAE9C,wBAAwB,CAAC1C,WAAW,CAAE,CAAC;cAAA;cAAAkE,QAAA,EAEhD/D,iBAAiB,CAACwF,GAAG,CAACC,MAAM,iBAC3B1J,OAAA,CAACX,QAAQ;gBAAoB2B,KAAK,EAAE0I,MAAM,CAAC1I,KAAM;gBAAAgH,QAAA,EAC9C0B,MAAM,CAAC7I;cAAK,GADA6I,MAAM,CAAC1I,KAAK;gBAAAsH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEjB,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACPzI,OAAA,CAACf,IAAI;UAACgK,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAnB,QAAA,eACvBhI,OAAA,CAACV,WAAW;YAAC8J,SAAS;YAACC,IAAI,EAAC,OAAO;YAAChB,OAAO,EAAC,UAAU;YAACO,QAAQ,EAAE/G,SAAU;YAACiG,EAAE,EAAE;cAAEyB,QAAQ,EAAE;YAAQ,CAAE;YAAAvB,QAAA,gBACpGhI,OAAA,CAACT,UAAU;cAACmB,EAAE,EAAC,yBAAyB;cAAAsH,QAAA,EAAC;YAAU;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAChEzI,OAAA,CAACZ,MAAM;cACLoK,OAAO,EAAC,yBAAyB;cACjC9I,EAAE,EAAC,mBAAmB;cACtBM,KAAK,EAAE+C,UAAW;cAClBlD,KAAK,EAAC,YAAY;cAClByI,QAAQ,EAAE9C,wBAAwB,CAACxC,aAAa,CAAE,CAAC;cAAA;cAAAgE,QAAA,EAElD9D,gBAAgB,CAACuF,GAAG,CAACC,MAAM,iBAC1B1J,OAAA,CAACX,QAAQ;gBAAoB2B,KAAK,EAAE0I,MAAM,CAAC1I,KAAM;gBAAAgH,QAAA,EAC9C0B,MAAM,CAAC7I;cAAK,GADA6I,MAAM,CAAC1I,KAAK;gBAAAsH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEjB,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAEP1G,KAAK,iBAAI/B,OAAA,CAAC1B,KAAK;MAACqL,QAAQ,EAAC,OAAO;MAAC7B,EAAE,EAAE;QAAEM,EAAE,EAAE;MAAE,CAAE;MAAAJ,QAAA,EAAEjG;IAAK;MAAAuG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,EAE/D5G,SAAS,IAAI,CAACF,MAAM,CAACiI,MAAM;IAAA;IAAK;IAC/B5J,OAAA,CAACpC,GAAG;MAACkK,EAAE,EAAE;QAAEG,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEW,CAAC,EAAE;MAAE,CAAE;MAAAb,QAAA,eAC3DhI,OAAA,CAAC3B,gBAAgB;QAAAiK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CAAC,gBAENzI,OAAA,CAAAE,SAAA;MAAA8H,QAAA,gBACEhI,OAAA,CAAC/B,cAAc;QAAC4L,SAAS,EAAEzL,KAAM;QAAC0J,EAAE,EAAE;UAAEgC,QAAQ,EAAE;QAAW,CAAE;QAAA9B,QAAA,GAC5DnG,SAAS,iBAAI7B,OAAA,CAACb,cAAc;UAAC2I,EAAE,EAAE;YAACgC,QAAQ,EAAE,UAAU;YAAEC,GAAG,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAM;QAAE;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnFzI,OAAA,CAAClC,KAAK;UAACgK,EAAE,EAAE;YAAEyB,QAAQ,EAAE;UAAI,CAAE;UAAC,cAAW,qBAAqB;UAACF,IAAI,EAAC,OAAO;UAAArB,QAAA,gBACzEhI,OAAA,CAAC9B,SAAS;YAAA8J,QAAA,eACRhI,OAAA,CAAC7B,QAAQ;cAAA6J,QAAA,EACNvH,SAAS,CAACgJ,GAAG,CAAEQ,QAAQ,iBACtBjK,OAAA,CAAChC,SAAS;gBAERkM,KAAK,EAAED,QAAQ,CAACtJ,OAAO,GAAG,OAAO,GAAG,MAAO;gBAC3CwJ,OAAO,EAAEF,QAAQ,CAACrJ,cAAc,GAAG,MAAM,GAAG,QAAS;gBACrDwJ,aAAa,EAAE/F,OAAO,KAAK4F,QAAQ,CAACvJ,EAAE,GAAGyD,KAAK,GAAG,KAAM;gBAAA6D,QAAA,EAEtDiC,QAAQ,CAACnJ,QAAQ,gBAChBd,OAAA,CAACd,cAAc;kBACbmL,MAAM,EAAEhG,OAAO,KAAK4F,QAAQ,CAACvJ,EAAG;kBAChC4J,SAAS,EAAEjG,OAAO,KAAK4F,QAAQ,CAACvJ,EAAE,GAAGyD,KAAK,GAAG,KAAM;kBACnDwE,OAAO,EAAGvC,KAAK,IAAKM,iBAAiB,CAACN,KAAK,EAAE6D,QAAQ,CAACvJ,EAAE,CAAE;kBAC1DkI,QAAQ,EAAE/G,SAAU;kBAAAmG,QAAA,GAEnBiC,QAAQ,CAACpJ,KAAK,EACdwD,OAAO,KAAK4F,QAAQ,CAACvJ,EAAE,gBACtBV,OAAA,CAACpC,GAAG;oBAACiM,SAAS,EAAC,MAAM;oBAAC/B,EAAE,EAAEnI,cAAe;oBAAAqI,QAAA,EACtC7D,KAAK,KAAK,MAAM,GAAG,mBAAmB,GAAG;kBAAkB;oBAAAmE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD,CAAC,GACJ,IAAI;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACM,CAAC,GAEjBwB,QAAQ,CAACpJ;cACV,GArBIoJ,QAAQ,CAACvJ,EAAE;gBAAA4H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAsBP,CACZ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACZzI,OAAA,CAACjC,SAAS;YAAAiK,QAAA,GACPrG,MAAM,CAACiI,MAAM,KAAK,CAAC,IAAI,CAAC/H,SAAS,iBAChC7B,OAAA,CAAC7B,QAAQ;cAAA6J,QAAA,eACPhI,OAAA,CAAChC,SAAS;gBAACuM,OAAO,EAAE9J,SAAS,CAACmJ,MAAO;gBAACM,KAAK,EAAC,QAAQ;gBAAAlC,QAAA,EAAC;cAAgB;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE,CACX,EACA9G,MAAM,CAAC8H,GAAG,CAAE3C,KAAK,iBAChB9G,OAAA,CAAC7B,QAAQ;cACPqM,KAAK;cAC4B;cACjC1C,EAAE,EAAE;gBAAE,kCAAkC,EAAE;kBAAE2C,MAAM,EAAE;gBAAE;cAAE,CAAE;cAAAzC,QAAA,gBAE1DhI,OAAA,CAAChC,SAAS;gBAAC6L,SAAS,EAAC,IAAI;gBAACa,KAAK,EAAC,KAAK;gBAACP,OAAO,EAAC,MAAM;gBAACrC,EAAE,EAAE;kBAAC6C,EAAE,EAAE;gBAAK,CAAE;gBAAA3C,QAAA,eACnEhI,OAAA;kBACE4K,GAAG,EAAE,yBAAyB9D,KAAK,CAAC+D,QAAQ,IAAI/D,KAAK,CAACpG,EAAE,EAAG;kBAC3DoK,GAAG,EAAEhE,KAAK,CAACjC,iBAAkB;kBAC7BkG,KAAK,EAAE;oBAAEC,MAAM,EAAE,MAAM;oBAAEhB,KAAK,EAAE,MAAM;oBAAEiB,QAAQ,EAAE,MAAM;oBAAEC,SAAS,EAAE,SAAS;oBAAEC,aAAa,EAAE,QAAQ;oBAAEhB,OAAO,EAAE;kBAAQ,CAAE;kBAC5HiB,OAAO,EAAG7K,CAAC,IAAK;oBAAEA,CAAC,CAAC8F,MAAM,CAAC0E,KAAK,CAAC9C,OAAO,GAAG,MAAM;kBAAE;gBAAE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZzI,OAAA,CAAChC,SAAS;gBAAAgK,QAAA,EAAElB,KAAK,CAACjC;cAAiB;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAChDzI,OAAA,CAAChC,SAAS;gBAAAgK,QAAA,EAAElB,KAAK,CAAC9B,UAAU,IAAI;cAAK;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAClDzI,OAAA,CAAChC,SAAS;gBAAAgK,QAAA,EAAElB,KAAK,CAAC/B,WAAW,IAAI;cAAK;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnDzI,OAAA,CAAChC,SAAS;gBAAAgK,QAAA,EACPlB,KAAK,CAACE,QAAQ,iBAAIhH,OAAA,CAACnC,UAAU;kBAACwK,OAAO,EAAC,SAAS;kBAACgD,KAAK,EAAC,eAAe;kBAAArD,QAAA,EAAC;gBAAS;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpF,CAAC,eACZzI,OAAA,CAAChC,SAAS;gBAAAgK,QAAA,EAAE7H,UAAU,CAAC2G,KAAK,CAACwE,UAAU;cAAC;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACrDzI,OAAA,CAAChC,SAAS;gBAAAgK,QAAA,gBACRhI,OAAA,CAACxB,OAAO;kBAACkK,KAAK,EAAC,eAAe;kBAAAV,QAAA,eAC5BhI,OAAA;oBAAAgI,QAAA,eACEhI,OAAA,CAACzB,UAAU;sBAAC8K,IAAI,EAAC,OAAO;sBAACV,OAAO,EAAEA,CAAA,KAAM9B,oBAAoB,CAACC,KAAK,CAAE;sBAAC8B,QAAQ,EAAE/F,WAAW,IAAIQ,aAAa,IAAIxB,SAAU;sBAAAmG,QAAA,eACvHhI,OAAA,CAACR,QAAQ;wBAAC+L,QAAQ,EAAC;sBAAO;wBAAAjD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC,eACVzI,OAAA,CAACxB,OAAO;kBAACkK,KAAK,EAAC,cAAc;kBAAAV,QAAA,eAC3BhI,OAAA;oBAAAgI,QAAA,eACEhI,OAAA,CAACzB,UAAU;sBAAC8K,IAAI,EAAC,OAAO;sBAACV,OAAO,EAAEA,CAAA,KAAMrB,sBAAsB,CAACR,KAAK,CAAC+D,QAAQ,CAAE;sBAACjC,QAAQ,EAAE/F,WAAW,IAAIQ,aAAa,IAAIxB,SAAU;sBAAAmG,QAAA,eAClIhI,OAAA,CAACP,UAAU;wBAAC8L,QAAQ,EAAC;sBAAO;wBAAAjD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA,GAjCP3B,KAAK,CAAC+D,QAAQ,IAAI/D,KAAK,CAACpG,EAAE;cAAA4H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkCvB,CACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,EAEhBtG,aAAa,GAAG,CAAC,iBAChBnC,OAAA,CAACpC,GAAG;QAACkK,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEE,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE;QAAS,CAAE;QAAAF,QAAA,eAC5DhI,OAAA,CAAChB,UAAU;UACTwM,KAAK,EAAErJ,aAAc;UACrBqC,IAAI,EAAEnC,aAAc;UACpBiH,QAAQ,EAAEzB,gBAAiB;UAC3BwD,KAAK,EAAC,SAAS;UACfI,eAAe;UACfC,cAAc;UACd9C,QAAQ,EAAE/G;QAAU;UAAAyG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA,eACD,CACH,eAGDzI,OAAA,CAACvB,MAAM;MAACkN,IAAI,EAAEpJ,cAAe;MAACqJ,OAAO,EAAE3E,qBAAsB;MAAAe,QAAA,gBAC3DhI,OAAA,CAACnB,WAAW;QAAAmJ,QAAA,EAAC;MAAa;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACxCzI,OAAA,CAACrB,aAAa;QAAAqJ,QAAA,gBACZhI,OAAA,CAACpB,iBAAiB;UAACkJ,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAJ,QAAA,GAAC,oCACE,EAACvF,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE/B,EAAE;QAAA;UAAA4H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,EACnB1F,SAAS,iBAAI/C,OAAA,CAAC1B,KAAK;UAACqL,QAAQ,EAAC,OAAO;UAAC7B,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAJ,QAAA,EAAEjF;QAAS;UAAAuF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACxEzI,OAAA,CAACjB,SAAS;UACR8M,SAAS;UACTC,MAAM,EAAC,OAAO;UACdpL,EAAE,EAAC,UAAU;UACbG,KAAK,EAAC,UAAU;UAChBkL,IAAI,EAAC,MAAM;UACX3C,SAAS;UACTf,OAAO,EAAC,UAAU;UAClBrH,KAAK,EAAE2B,UAAW;UAClB2G,QAAQ,EAAG/I,CAAC,IAAKqC,aAAa,CAACrC,CAAC,CAAC8F,MAAM,CAACrF,KAAK,CAAE;UAC/C4H,QAAQ,EAAE/F;QAAY;UAAAyF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW,CAAC,eAChBzI,OAAA,CAACtB,aAAa;QAAAsJ,QAAA,gBACZhI,OAAA,CAAClB,MAAM;UAAC6J,OAAO,EAAE1B,qBAAsB;UAAC2B,QAAQ,EAAE/F,WAAY;UAAAmF,QAAA,EAAC;QAAM;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC9EzI,OAAA,CAAClB,MAAM;UAAC6J,OAAO,EAAEzB,mBAAoB;UAAC0B,QAAQ,EAAE/F,WAAW,IAAI,CAACF,UAAU,CAACwE,IAAI,CAAC,CAAE;UAAAa,QAAA,EAC/EnF,WAAW,gBAAG7C,OAAA,CAAC3B,gBAAgB;YAACgL,IAAI,EAAE;UAAG;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAM;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGTzI,OAAA,CAACvB,MAAM;MACLkN,IAAI,EAAE1I,gBAAiB;MACvB2I,OAAO,EAAEnE,uBAAwB;MACjC,mBAAgB,oBAAoB;MACpC,oBAAiB,0BAA0B;MAAAO,QAAA,gBAE3ChI,OAAA,CAACnB,WAAW;QAAC6B,EAAE,EAAC,oBAAoB;QAAAsH,QAAA,EAAE;MAAkB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eACvEzI,OAAA,CAACrB,aAAa;QAAAqJ,QAAA,GACXzE,WAAW,iBAAIvD,OAAA,CAAC1B,KAAK;UAACqL,QAAQ,EAAC,OAAO;UAAC7B,EAAE,EAAE;YAAEM,EAAE,EAAE;UAAE,CAAE;UAAAJ,QAAA,EAAEzE;QAAW;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC5EzI,OAAA,CAACpB,iBAAiB;UAAC8B,EAAE,EAAC,0BAA0B;UAAAsH,QAAA,EAAC;QAEjD;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAChBzI,OAAA,CAACtB,aAAa;QAAAsJ,QAAA,gBACZhI,OAAA,CAAClB,MAAM;UAAC6J,OAAO,EAAElB,uBAAwB;UAACmB,QAAQ,EAAEvF,aAAc;UAAA2E,QAAA,EAAC;QAEnE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTzI,OAAA,CAAClB,MAAM;UAAC6J,OAAO,EAAEjB,iBAAkB;UAAC2D,KAAK,EAAC,OAAO;UAACQ,SAAS;UAACjD,QAAQ,EAAEvF,aAAc;UAAA2E,QAAA,EACjF3E,aAAa,gBAAGrD,OAAA,CAAC3B,gBAAgB;YAACgL,IAAI,EAAE;UAAG;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GAAG;QAAQ;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEN,CAAC;AAEV,CAAC;AAAC/G,EAAA,CA5aID,YAAY;AAAAuK,EAAA,GAAZvK,YAAY;AA8alB,eAAeA,YAAY;AAAC,IAAAuK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}