{"ast": null, "code": "import apiClient from './api'; // Assuming api.js exports the configured axios instance\n\n// Initialize a global mock store for pictures if it doesn't exist\nif (!window.mockBbPicturesListGlobal) {\n  window.mockBbPicturesListGlobal = [{\n    id: 'pic1',\n    name: 'Cat_Photo_1.jpg',\n    file_path: 'https://via.placeholder.com/150/92c952',\n    created_at: new Date(Date.now() - 86400000 * 2).toISOString()\n  }, {\n    id: 'pic2',\n    name: 'Urban_Scene_A.png',\n    file_path: 'https://via.placeholder.com/150/771796',\n    created_at: new Date(Date.now() - 86400000).toISOString()\n  }, {\n    id: 'pic3',\n    name: 'Dog_Park_View.jpeg',\n    file_path: 'https://via.placeholder.com/150/24f355',\n    created_at: new Date().toISOString()\n  }, {\n    id: 'pic4',\n    name: 'Another_Cat.jpg',\n    file_path: 'https://via.placeholder.com/150/f66b97',\n    created_at: new Date(Date.now() - 3600000 * 5).toISOString()\n  }, {\n    id: 'pic5',\n    name: 'City_Skyline.png',\n    file_path: 'https://via.placeholder.com/150/56a8c2',\n    created_at: new Date(Date.now() - 3600000 * 10).toISOString()\n  }];\n}\n\n// Initialize global mock store for experiments if it doesn't exist\nif (!window.mockBbExperiments) {\n  window.mockBbExperiments = {\n    'pic1': [{\n      id: 'exp1_pic1',\n      picture_id: 'pic1',\n      prompt: 'A cat sitting on a red mat indoors.',\n      resize_height: 512,\n      resize_width: 512,\n      output_type: 'Bounding Box',\n      created_at: new Date(Date.now() - 3600000).toISOString(),\n      results: [{\n        id: 'res1_exp1',\n        model_id: 'modelA_id',\n        model_name: 'VisionMaster Pro',\n        status: 'success',\n        output_image_path: 'https://via.placeholder.com/200/00FF00/000000?Text=ModelA_CatMat',\n        score: 8,\n        error_message: null\n      }, {\n        id: 'res2_exp1',\n        model_id: 'modelB_id',\n        model_name: 'ObjectNet Advanced',\n        status: 'success',\n        output_image_path: 'https://via.placeholder.com/200/00FF00/000000?Text=ModelB_CatMat',\n        score: 6,\n        error_message: null\n      }, {\n        id: 'res3_exp1',\n        model_id: 'modelC_id',\n        model_name: 'QuickDetect v3',\n        status: 'success',\n        output_image_path: 'https://via.placeholder.com/200/00FF00/000000?Text=ModelC_CatMat',\n        score: 7,\n        error_message: null\n      }]\n    }, {\n      id: 'exp2_pic1',\n      picture_id: 'pic1',\n      prompt: 'Multiple cats playing with toys.',\n      resize_height: 640,\n      resize_width: 640,\n      output_type: 'Bounding Box + Segmentation Mask',\n      created_at: new Date(Date.now() - 7200000).toISOString(),\n      results: [{\n        id: 'res4_exp2',\n        model_id: 'modelA_id',\n        model_name: 'VisionMaster Pro',\n        status: 'success',\n        output_image_path: 'https://via.placeholder.com/200/00AA00/FFFFFF?Text=ModelA_CatsToys',\n        score: 9,\n        error_message: null\n      }, {\n        id: 'res5_exp2',\n        model_id: 'modelC_id',\n        model_name: 'QuickDetect v3',\n        status: 'success',\n        output_image_path: 'https://via.placeholder.com/200/006600/FFFFFF?Text=ModelC_CatsToys',\n        score: 5,\n        error_message: null\n      }]\n    }],\n    'pic2': [{\n      id: 'exp1_pic2',\n      picture_id: 'pic2',\n      prompt: 'Vehicles and pedestrians on a busy street.',\n      resize_height: 768,\n      resize_width: 1024,\n      output_type: 'Bounding Box',\n      created_at: new Date(Date.now() - 10800000).toISOString(),\n      results: [{\n        id: 'res6_exp1',\n        model_id: 'modelB_id',\n        model_name: 'ObjectNet Advanced',\n        status: 'success',\n        output_image_path: 'https://via.placeholder.com/200/FFFF00/000000?Text=ModelB_Urban',\n        score: 7,\n        error_message: null\n      }, {\n        id: 'res7_exp1',\n        model_id: 'modelD_id',\n        model_name: 'UrbanScanner X',\n        status: 'success',\n        output_image_path: 'https://via.placeholder.com/200/FFFF00/000000?Text=ModelD_Urban',\n        score: 9,\n        error_message: null\n      }]\n    }]\n    // Add more mock experiments for other picture IDs if needed\n  };\n}\nexport const getBbPictures = async (page = 1, searchTerm = '', perPage = 5) => {\n  console.log(`API: getBbPictures (mocked) - Page: ${page}, Search: \"${searchTerm}\", PerPage: ${perPage}`);\n  await new Promise(resolve => setTimeout(resolve, 100));\n  const allPictures = window.mockBbPicturesListGlobal || [];\n  const filteredPictures = searchTerm ? allPictures.filter(pic => pic.name.toLowerCase().includes(searchTerm.toLowerCase())) : allPictures;\n  const totalPictures = filteredPictures.length;\n  const totalPages = Math.ceil(totalPictures / perPage);\n  const paginatedPictures = filteredPictures.slice((page - 1) * perPage, page * perPage);\n  return Promise.resolve({\n    pictures: paginatedPictures,\n    totalPages,\n    totalPictures,\n    currentPage: page\n  });\n};\nexport const uploadBbPicture = async formData => {\n  console.log('API: uploadBbPicture (mocked)');\n  await new Promise(resolve => setTimeout(resolve, 500));\n  let mockFileName = 'uploaded.png';\n  if (formData.has('picture')) {\n    const file = formData.get('picture');\n    if (file instanceof File) mockFileName = file.name;\n  }\n  const newPicture = {\n    id: `newPic_${Date.now()}`,\n    name: mockFileName,\n    file_path: `https://via.placeholder.com/150/0000FF/FFFFFF?Text=${mockFileName}`,\n    created_at: new Date().toISOString()\n  };\n  window.mockBbPicturesListGlobal.unshift(newPicture);\n  return Promise.resolve(newPicture);\n};\nexport const updateBbPictureName = async (pictureId, newName) => {\n  console.log(`API: updateBbPictureName (mocked) - ID: ${pictureId}, New Name: ${newName}`);\n  await new Promise(resolve => setTimeout(resolve, 100));\n  const picIndex = window.mockBbPicturesListGlobal.findIndex(p => p.id === pictureId);\n  if (picIndex !== -1) {\n    window.mockBbPicturesListGlobal[picIndex].name = newName;\n    return Promise.resolve({\n      ...window.mockBbPicturesListGlobal[picIndex]\n    });\n  }\n  return Promise.reject(new Error(`Pic ID ${pictureId} not found.`));\n};\nexport const deleteBbPicture = async pictureId => {\n  console.log(`API: deleteBbPicture (mocked) - ID: ${pictureId}`);\n  await new Promise(resolve => setTimeout(resolve, 300));\n  const initialLength = window.mockBbPicturesListGlobal.length;\n  window.mockBbPicturesListGlobal = window.mockBbPicturesListGlobal.filter(p => p.id !== pictureId);\n  if (window.mockBbPicturesListGlobal.length < initialLength) return Promise.resolve({\n    message: `Pic ${pictureId} deleted.`\n  });\n  return Promise.reject(new Error(`Pic ID ${pictureId} not found for deletion.`));\n};\nexport const getBbExperiments = async (pictureId, page = 1) => {\n  console.log(`API: getBbExperiments (mocked) - PicID: ${pictureId}, Page: ${page}`);\n  await new Promise(resolve => setTimeout(resolve, 200));\n  if (!pictureId) return Promise.resolve({\n    experiments: [],\n    totalPages: 0,\n    currentPage: page,\n    totalExperiments: 0\n  });\n  const experimentsForPic = window.mockBbExperiments[pictureId] || [];\n  const perPage = 2;\n  const totalExperiments = experimentsForPic.length;\n  const totalPages = Math.ceil(totalExperiments / perPage);\n  const paginatedExperiments = experimentsForPic.slice((page - 1) * perPage, page * perPage);\n  return Promise.resolve({\n    experiments: paginatedExperiments,\n    totalPages,\n    currentPage: page,\n    totalExperiments\n  });\n};\nexport const updateBbResultScore = async (resultId, score) => {\n  console.log(`API: updateBbResultScore (mocked) - ResID: ${resultId}, Score: ${score}`);\n  await new Promise(resolve => setTimeout(resolve, 100));\n  // Find and update the score in the mock store (optional for mock, but good for consistency)\n  for (const picId in window.mockBbExperiments) {\n    for (const exp of window.mockBbExperiments[picId]) {\n      const result = exp.results.find(r => r.id === resultId);\n      if (result) result.score = score;\n    }\n  }\n  return Promise.resolve({\n    id: resultId,\n    score: score,\n    message: `Score updated.`\n  });\n};\nexport const getBbExperimentConfigurations = async () => {\n  console.log('API: getBbExperimentConfigurations (mocked)');\n  await new Promise(resolve => setTimeout(resolve, 100));\n  return Promise.resolve([{\n    prompt: 'A cat sitting on a red mat indoors.',\n    resize_height: 512,\n    resize_width: 512,\n    output_type: 'Bounding Box'\n  }, {\n    prompt: 'Multiple cats playing with toys.',\n    resize_height: 640,\n    resize_width: 640,\n    output_type: 'Bounding Box + Segmentation Mask'\n  }]);\n};\nexport const createBbExperiment = async experimentData => {\n  console.log('API: createBbExperiment (mocked) with:', experimentData);\n  await new Promise(resolve => setTimeout(resolve, 300));\n  const newId = `exp_${Date.now()}_${experimentData.picture_id}`;\n  const newExperiment = {\n    id: newId,\n    ...experimentData,\n    created_at: new Date().toISOString(),\n    results: [\n    // Mock some \"processing\" results based on available models\n    {\n      id: `res_${newId}_modelA`,\n      model_id: 'modelA_id',\n      model_name: 'VisionMaster Pro',\n      status: 'processing',\n      output_image_path: null,\n      score: null,\n      error_message: null\n    }, {\n      id: `res_${newId}_modelB`,\n      model_id: 'modelB_id',\n      model_name: 'ObjectNet Advanced',\n      status: 'processing',\n      output_image_path: null,\n      score: null,\n      error_message: null\n    }]\n  };\n  if (!window.mockBbExperiments[experimentData.picture_id]) window.mockBbExperiments[experimentData.picture_id] = [];\n  window.mockBbExperiments[experimentData.picture_id].unshift(newExperiment);\n  return Promise.resolve(newExperiment);\n};\nexport const getBbModels = async () => {\n  console.log('API: getBbModels (mocked)');\n  await new Promise(resolve => setTimeout(resolve, 100));\n  return Promise.resolve([{\n    id: 'modelA_id',\n    name: 'VisionMaster Pro',\n    description: 'High accuracy model.',\n    is_active: true,\n    gcp_model_name: 'gcp-visionmaster-pro'\n  }, {\n    id: 'modelB_id',\n    name: 'ObjectNet Advanced',\n    description: 'Complex scenes specialist.',\n    is_active: true,\n    gcp_model_name: 'gcp-objectnet-adv'\n  }, {\n    id: 'modelC_id',\n    name: 'QuickDetect v3',\n    description: 'Fast, real-time.',\n    is_active: false,\n    gcp_model_name: 'gcp-quickdetect-v3'\n  }, {\n    id: 'modelD_id',\n    name: 'UrbanScanner X',\n    description: 'For urban environments.',\n    is_active: true,\n    gcp_model_name: 'gcp-urbanscanner-x'\n  }]);\n};\nexport const updateBbModel = async (modelId, data) => {\n  console.log(`API: updateBbModel (mocked) - ID: ${modelId}, Data:`, data);\n  await new Promise(resolve => setTimeout(resolve, 100));\n  // In a real scenario, you'd also update your global mock models list if using one for getBbModels\n  return Promise.resolve({\n    id: modelId,\n    ...data,\n    message: `Model updated.`\n  });\n};\n\n// New function for RankPage\nconst getAllBbExperimentsWithResults = async () => {\n  console.log('API: getAllBbExperimentsWithResults (mocked)');\n  await new Promise(resolve => setTimeout(resolve, 50));\n  // This flattens all experiments from all pictures in the mock store\n  const allExperimentsList = Object.values(window.mockBbExperiments || {}).flat();\n  return Promise.resolve(allExperimentsList);\n};\nexport const getBbRankData = async () => {\n  console.log('API: getBbRankData (mocked)');\n  await new Promise(resolve => setTimeout(resolve, 150));\n  try {\n    const models = await getBbModels();\n    const experiments = await getAllBbExperimentsWithResults();\n    return Promise.resolve({\n      models,\n      experiments\n    });\n  } catch (error) {\n    console.error(\"Error in getBbRankData (mocked):\", error);\n    return Promise.reject(error);\n  }\n};", "map": {"version": 3, "names": ["apiClient", "window", "mockBbPicturesListGlobal", "id", "name", "file_path", "created_at", "Date", "now", "toISOString", "mockBbExperiments", "picture_id", "prompt", "resize_height", "resize_width", "output_type", "results", "model_id", "model_name", "status", "output_image_path", "score", "error_message", "getBbPictures", "page", "searchTerm", "perPage", "console", "log", "Promise", "resolve", "setTimeout", "allPictures", "filteredPictures", "filter", "pic", "toLowerCase", "includes", "totalPictures", "length", "totalPages", "Math", "ceil", "paginatedPictures", "slice", "pictures", "currentPage", "uploadBbPicture", "formData", "mockFileName", "has", "file", "get", "File", "newPicture", "unshift", "updateBbPictureName", "pictureId", "newName", "picIndex", "findIndex", "p", "reject", "Error", "deleteBbPicture", "initialLength", "message", "getBbExperiments", "experiments", "totalExperiments", "experimentsForPic", "paginatedExperiments", "updateBbResultScore", "resultId", "picId", "exp", "result", "find", "r", "getBbExperimentConfigurations", "createBbExperiment", "experimentData", "newId", "newExperiment", "getBbModels", "description", "is_active", "gcp_model_name", "updateBbModel", "modelId", "data", "getAllBbExperimentsWithResults", "allExperimentsList", "Object", "values", "flat", "getBbRankData", "models", "error"], "sources": ["D:/Documents/Programing/TRO/ModelTestsWorkbench/frontend/src/services/api_bounding_box.js"], "sourcesContent": ["import apiClient from './api'; // Assuming api.js exports the configured axios instance\r\n\r\n// Initialize a global mock store for pictures if it doesn't exist\r\nif (!window.mockBbPicturesListGlobal) {\r\n  window.mockBbPicturesListGlobal = [\r\n    { id: 'pic1', name: 'Cat_Photo_1.jpg', file_path: 'https://via.placeholder.com/150/92c952', created_at: new Date(Date.now() - 86400000 * 2).toISOString() },\r\n    { id: 'pic2', name: 'Urban_Scene_A.png', file_path: 'https://via.placeholder.com/150/771796', created_at: new Date(Date.now() - 86400000).toISOString() },\r\n    { id: 'pic3', name: 'Dog_Park_View.jpeg', file_path: 'https://via.placeholder.com/150/24f355', created_at: new Date().toISOString() },\r\n    { id: 'pic4', name: 'Another_Cat.jpg', file_path: 'https://via.placeholder.com/150/f66b97', created_at: new Date(Date.now() - 3600000 * 5).toISOString() },\r\n    { id: 'pic5', name: 'City_Skyline.png', file_path: 'https://via.placeholder.com/150/56a8c2', created_at: new Date(Date.now() - 3600000 * 10).toISOString() },\r\n  ];\r\n}\r\n\r\n// Initialize global mock store for experiments if it doesn't exist\r\nif (!window.mockBbExperiments) {\r\n  window.mockBbExperiments = {\r\n    'pic1': [\r\n      {\r\n        id: 'exp1_pic1', picture_id: 'pic1', prompt: 'A cat sitting on a red mat indoors.', resize_height: 512, resize_width: 512, output_type: 'Bounding Box', created_at: new Date(Date.now() - 3600000).toISOString(),\r\n        results: [\r\n          { id: 'res1_exp1', model_id: 'modelA_id', model_name: 'VisionMaster Pro', status: 'success', output_image_path: 'https://via.placeholder.com/200/00FF00/000000?Text=ModelA_CatMat', score: 8, error_message: null },\r\n          { id: 'res2_exp1', model_id: 'modelB_id', model_name: 'ObjectNet Advanced', status: 'success', output_image_path: 'https://via.placeholder.com/200/00FF00/000000?Text=ModelB_CatMat', score: 6, error_message: null },\r\n          { id: 'res3_exp1', model_id: 'modelC_id', model_name: 'QuickDetect v3', status: 'success', output_image_path: 'https://via.placeholder.com/200/00FF00/000000?Text=ModelC_CatMat', score: 7, error_message: null }\r\n        ]\r\n      },\r\n      {\r\n        id: 'exp2_pic1', picture_id: 'pic1', prompt: 'Multiple cats playing with toys.', resize_height: 640, resize_width: 640, output_type: 'Bounding Box + Segmentation Mask', created_at: new Date(Date.now() - 7200000).toISOString(),\r\n        results: [\r\n          { id: 'res4_exp2', model_id: 'modelA_id', model_name: 'VisionMaster Pro', status: 'success', output_image_path: 'https://via.placeholder.com/200/00AA00/FFFFFF?Text=ModelA_CatsToys', score: 9, error_message: null },\r\n          { id: 'res5_exp2', model_id: 'modelC_id', model_name: 'QuickDetect v3', status: 'success', output_image_path: 'https://via.placeholder.com/200/006600/FFFFFF?Text=ModelC_CatsToys', score: 5, error_message: null }\r\n        ]\r\n      }\r\n    ],\r\n    'pic2': [\r\n      {\r\n        id: 'exp1_pic2', picture_id: 'pic2', prompt: 'Vehicles and pedestrians on a busy street.', resize_height: 768, resize_width: 1024, output_type: 'Bounding Box', created_at: new Date(Date.now() - 10800000).toISOString(),\r\n        results: [\r\n          { id: 'res6_exp1', model_id: 'modelB_id', model_name: 'ObjectNet Advanced', status: 'success', output_image_path: 'https://via.placeholder.com/200/FFFF00/000000?Text=ModelB_Urban', score: 7, error_message: null },\r\n          { id: 'res7_exp1', model_id: 'modelD_id', model_name: 'UrbanScanner X', status: 'success', output_image_path: 'https://via.placeholder.com/200/FFFF00/000000?Text=ModelD_Urban', score: 9, error_message: null }\r\n        ]\r\n      }\r\n    ],\r\n    // Add more mock experiments for other picture IDs if needed\r\n  };\r\n}\r\n\r\n\r\nexport const getBbPictures = async (page = 1, searchTerm = '', perPage = 5) => {\r\n  console.log(`API: getBbPictures (mocked) - Page: ${page}, Search: \"${searchTerm}\", PerPage: ${perPage}`);\r\n  await new Promise(resolve => setTimeout(resolve, 100));\r\n  const allPictures = window.mockBbPicturesListGlobal || [];\r\n  const filteredPictures = searchTerm ? allPictures.filter(pic => pic.name.toLowerCase().includes(searchTerm.toLowerCase())) : allPictures;\r\n  const totalPictures = filteredPictures.length;\r\n  const totalPages = Math.ceil(totalPictures / perPage);\r\n  const paginatedPictures = filteredPictures.slice((page - 1) * perPage, page * perPage);\r\n  return Promise.resolve({ pictures: paginatedPictures, totalPages, totalPictures, currentPage: page });\r\n};\r\n\r\nexport const uploadBbPicture = async (formData) => {\r\n  console.log('API: uploadBbPicture (mocked)');\r\n  await new Promise(resolve => setTimeout(resolve, 500));\r\n  let mockFileName = 'uploaded.png';\r\n  if (formData.has('picture')) {\r\n    const file = formData.get('picture');\r\n    if (file instanceof File) mockFileName = file.name;\r\n  }\r\n  const newPicture = { id: `newPic_${Date.now()}`, name: mockFileName, file_path: `https://via.placeholder.com/150/0000FF/FFFFFF?Text=${mockFileName}`, created_at: new Date().toISOString() };\r\n  window.mockBbPicturesListGlobal.unshift(newPicture);\r\n  return Promise.resolve(newPicture);\r\n};\r\n\r\nexport const updateBbPictureName = async (pictureId, newName) => {\r\n  console.log(`API: updateBbPictureName (mocked) - ID: ${pictureId}, New Name: ${newName}`);\r\n  await new Promise(resolve => setTimeout(resolve, 100));\r\n  const picIndex = window.mockBbPicturesListGlobal.findIndex(p => p.id === pictureId);\r\n  if (picIndex !== -1) {\r\n    window.mockBbPicturesListGlobal[picIndex].name = newName;\r\n    return Promise.resolve({ ...window.mockBbPicturesListGlobal[picIndex] });\r\n  }\r\n  return Promise.reject(new Error(`Pic ID ${pictureId} not found.`));\r\n};\r\n\r\nexport const deleteBbPicture = async (pictureId) => {\r\n  console.log(`API: deleteBbPicture (mocked) - ID: ${pictureId}`);\r\n  await new Promise(resolve => setTimeout(resolve, 300));\r\n  const initialLength = window.mockBbPicturesListGlobal.length;\r\n  window.mockBbPicturesListGlobal = window.mockBbPicturesListGlobal.filter(p => p.id !== pictureId);\r\n  if (window.mockBbPicturesListGlobal.length < initialLength) return Promise.resolve({ message: `Pic ${pictureId} deleted.` });\r\n  return Promise.reject(new Error(`Pic ID ${pictureId} not found for deletion.`));\r\n};\r\n\r\nexport const getBbExperiments = async (pictureId, page = 1) => {\r\n  console.log(`API: getBbExperiments (mocked) - PicID: ${pictureId}, Page: ${page}`);\r\n  await new Promise(resolve => setTimeout(resolve, 200));\r\n  if (!pictureId) return Promise.resolve({ experiments: [], totalPages: 0, currentPage: page, totalExperiments: 0 });\r\n  const experimentsForPic = window.mockBbExperiments[pictureId] || [];\r\n  const perPage = 2;\r\n  const totalExperiments = experimentsForPic.length;\r\n  const totalPages = Math.ceil(totalExperiments / perPage);\r\n  const paginatedExperiments = experimentsForPic.slice((page - 1) * perPage, page * perPage);\r\n  return Promise.resolve({ experiments: paginatedExperiments, totalPages, currentPage: page, totalExperiments });\r\n};\r\n\r\nexport const updateBbResultScore = async (resultId, score) => {\r\n  console.log(`API: updateBbResultScore (mocked) - ResID: ${resultId}, Score: ${score}`);\r\n  await new Promise(resolve => setTimeout(resolve, 100));\r\n  // Find and update the score in the mock store (optional for mock, but good for consistency)\r\n  for (const picId in window.mockBbExperiments) {\r\n    for (const exp of window.mockBbExperiments[picId]) {\r\n      const result = exp.results.find(r => r.id === resultId);\r\n      if (result) result.score = score;\r\n    }\r\n  }\r\n  return Promise.resolve({ id: resultId, score: score, message: `Score updated.` });\r\n};\r\n\r\nexport const getBbExperimentConfigurations = async () => {\r\n  console.log('API: getBbExperimentConfigurations (mocked)');\r\n  await new Promise(resolve => setTimeout(resolve, 100));\r\n  return Promise.resolve([\r\n    { prompt: 'A cat sitting on a red mat indoors.', resize_height: 512, resize_width: 512, output_type: 'Bounding Box' },\r\n    { prompt: 'Multiple cats playing with toys.', resize_height: 640, resize_width: 640, output_type: 'Bounding Box + Segmentation Mask' },\r\n  ]);\r\n};\r\n\r\nexport const createBbExperiment = async (experimentData) => {\r\n  console.log('API: createBbExperiment (mocked) with:', experimentData);\r\n  await new Promise(resolve => setTimeout(resolve, 300));\r\n  const newId = `exp_${Date.now()}_${experimentData.picture_id}`;\r\n  const newExperiment = {\r\n    id: newId, ...experimentData, created_at: new Date().toISOString(),\r\n    results: [ // Mock some \"processing\" results based on available models\r\n      { id: `res_${newId}_modelA`, model_id: 'modelA_id', model_name: 'VisionMaster Pro', status: 'processing', output_image_path: null, score: null, error_message: null },\r\n      { id: `res_${newId}_modelB`, model_id: 'modelB_id', model_name: 'ObjectNet Advanced', status: 'processing', output_image_path: null, score: null, error_message: null },\r\n    ]\r\n  };\r\n  if (!window.mockBbExperiments[experimentData.picture_id]) window.mockBbExperiments[experimentData.picture_id] = [];\r\n  window.mockBbExperiments[experimentData.picture_id].unshift(newExperiment);\r\n  return Promise.resolve(newExperiment);\r\n};\r\n\r\nexport const getBbModels = async () => {\r\n  console.log('API: getBbModels (mocked)');\r\n  await new Promise(resolve => setTimeout(resolve, 100));\r\n  return Promise.resolve([\r\n    { id: 'modelA_id', name: 'VisionMaster Pro', description: 'High accuracy model.', is_active: true, gcp_model_name: 'gcp-visionmaster-pro' },\r\n    { id: 'modelB_id', name: 'ObjectNet Advanced', description: 'Complex scenes specialist.', is_active: true, gcp_model_name: 'gcp-objectnet-adv' },\r\n    { id: 'modelC_id', name: 'QuickDetect v3', description: 'Fast, real-time.', is_active: false, gcp_model_name: 'gcp-quickdetect-v3' },\r\n    { id: 'modelD_id', name: 'UrbanScanner X', description: 'For urban environments.', is_active: true, gcp_model_name: 'gcp-urbanscanner-x' },\r\n  ]);\r\n};\r\n\r\nexport const updateBbModel = async (modelId, data) => {\r\n  console.log(`API: updateBbModel (mocked) - ID: ${modelId}, Data:`, data);\r\n  await new Promise(resolve => setTimeout(resolve, 100));\r\n  // In a real scenario, you'd also update your global mock models list if using one for getBbModels\r\n  return Promise.resolve({ id: modelId, ...data, message: `Model updated.` });\r\n};\r\n\r\n// New function for RankPage\r\nconst getAllBbExperimentsWithResults = async () => {\r\n  console.log('API: getAllBbExperimentsWithResults (mocked)');\r\n  await new Promise(resolve => setTimeout(resolve, 50));\r\n  // This flattens all experiments from all pictures in the mock store\r\n  const allExperimentsList = Object.values(window.mockBbExperiments || {}).flat();\r\n  return Promise.resolve(allExperimentsList);\r\n};\r\n\r\nexport const getBbRankData = async () => {\r\n  console.log('API: getBbRankData (mocked)');\r\n  await new Promise(resolve => setTimeout(resolve, 150));\r\n  try {\r\n    const models = await getBbModels();\r\n    const experiments = await getAllBbExperimentsWithResults();\r\n    return Promise.resolve({ models, experiments });\r\n  } catch (error) {\r\n    console.error(\"Error in getBbRankData (mocked):\", error);\r\n    return Promise.reject(error);\r\n  }\r\n};\r\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,OAAO,CAAC,CAAC;;AAE/B;AACA,IAAI,CAACC,MAAM,CAACC,wBAAwB,EAAE;EACpCD,MAAM,CAACC,wBAAwB,GAAG,CAChC;IAAEC,EAAE,EAAE,MAAM;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,SAAS,EAAE,wCAAwC;IAAEC,UAAU,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,QAAQ,GAAG,CAAC,CAAC,CAACC,WAAW,CAAC;EAAE,CAAC,EAC3J;IAAEN,EAAE,EAAE,MAAM;IAAEC,IAAI,EAAE,mBAAmB;IAAEC,SAAS,EAAE,wCAAwC;IAAEC,UAAU,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAACC,WAAW,CAAC;EAAE,CAAC,EACzJ;IAAEN,EAAE,EAAE,MAAM;IAAEC,IAAI,EAAE,oBAAoB;IAAEC,SAAS,EAAE,wCAAwC;IAAEC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC;EAAE,CAAC,EACrI;IAAEN,EAAE,EAAE,MAAM;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,SAAS,EAAE,wCAAwC;IAAEC,UAAU,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,OAAO,GAAG,CAAC,CAAC,CAACC,WAAW,CAAC;EAAE,CAAC,EAC1J;IAAEN,EAAE,EAAE,MAAM;IAAEC,IAAI,EAAE,kBAAkB;IAAEC,SAAS,EAAE,wCAAwC;IAAEC,UAAU,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,OAAO,GAAG,EAAE,CAAC,CAACC,WAAW,CAAC;EAAE,CAAC,CAC7J;AACH;;AAEA;AACA,IAAI,CAACR,MAAM,CAACS,iBAAiB,EAAE;EAC7BT,MAAM,CAACS,iBAAiB,GAAG;IACzB,MAAM,EAAE,CACN;MACEP,EAAE,EAAE,WAAW;MAAEQ,UAAU,EAAE,MAAM;MAAEC,MAAM,EAAE,qCAAqC;MAAEC,aAAa,EAAE,GAAG;MAAEC,YAAY,EAAE,GAAG;MAAEC,WAAW,EAAE,cAAc;MAAET,UAAU,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAACC,WAAW,CAAC,CAAC;MAChNO,OAAO,EAAE,CACP;QAAEb,EAAE,EAAE,WAAW;QAAEc,QAAQ,EAAE,WAAW;QAAEC,UAAU,EAAE,kBAAkB;QAAEC,MAAM,EAAE,SAAS;QAAEC,iBAAiB,EAAE,kEAAkE;QAAEC,KAAK,EAAE,CAAC;QAAEC,aAAa,EAAE;MAAK,CAAC,EACnN;QAAEnB,EAAE,EAAE,WAAW;QAAEc,QAAQ,EAAE,WAAW;QAAEC,UAAU,EAAE,oBAAoB;QAAEC,MAAM,EAAE,SAAS;QAAEC,iBAAiB,EAAE,kEAAkE;QAAEC,KAAK,EAAE,CAAC;QAAEC,aAAa,EAAE;MAAK,CAAC,EACrN;QAAEnB,EAAE,EAAE,WAAW;QAAEc,QAAQ,EAAE,WAAW;QAAEC,UAAU,EAAE,gBAAgB;QAAEC,MAAM,EAAE,SAAS;QAAEC,iBAAiB,EAAE,kEAAkE;QAAEC,KAAK,EAAE,CAAC;QAAEC,aAAa,EAAE;MAAK,CAAC;IAErN,CAAC,EACD;MACEnB,EAAE,EAAE,WAAW;MAAEQ,UAAU,EAAE,MAAM;MAAEC,MAAM,EAAE,kCAAkC;MAAEC,aAAa,EAAE,GAAG;MAAEC,YAAY,EAAE,GAAG;MAAEC,WAAW,EAAE,kCAAkC;MAAET,UAAU,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC,CAACC,WAAW,CAAC,CAAC;MACjOO,OAAO,EAAE,CACP;QAAEb,EAAE,EAAE,WAAW;QAAEc,QAAQ,EAAE,WAAW;QAAEC,UAAU,EAAE,kBAAkB;QAAEC,MAAM,EAAE,SAAS;QAAEC,iBAAiB,EAAE,oEAAoE;QAAEC,KAAK,EAAE,CAAC;QAAEC,aAAa,EAAE;MAAK,CAAC,EACrN;QAAEnB,EAAE,EAAE,WAAW;QAAEc,QAAQ,EAAE,WAAW;QAAEC,UAAU,EAAE,gBAAgB;QAAEC,MAAM,EAAE,SAAS;QAAEC,iBAAiB,EAAE,oEAAoE;QAAEC,KAAK,EAAE,CAAC;QAAEC,aAAa,EAAE;MAAK,CAAC;IAEvN,CAAC,CACF;IACD,MAAM,EAAE,CACN;MACEnB,EAAE,EAAE,WAAW;MAAEQ,UAAU,EAAE,MAAM;MAAEC,MAAM,EAAE,4CAA4C;MAAEC,aAAa,EAAE,GAAG;MAAEC,YAAY,EAAE,IAAI;MAAEC,WAAW,EAAE,cAAc;MAAET,UAAU,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAACC,WAAW,CAAC,CAAC;MACzNO,OAAO,EAAE,CACP;QAAEb,EAAE,EAAE,WAAW;QAAEc,QAAQ,EAAE,WAAW;QAAEC,UAAU,EAAE,oBAAoB;QAAEC,MAAM,EAAE,SAAS;QAAEC,iBAAiB,EAAE,iEAAiE;QAAEC,KAAK,EAAE,CAAC;QAAEC,aAAa,EAAE;MAAK,CAAC,EACpN;QAAEnB,EAAE,EAAE,WAAW;QAAEc,QAAQ,EAAE,WAAW;QAAEC,UAAU,EAAE,gBAAgB;QAAEC,MAAM,EAAE,SAAS;QAAEC,iBAAiB,EAAE,iEAAiE;QAAEC,KAAK,EAAE,CAAC;QAAEC,aAAa,EAAE;MAAK,CAAC;IAEpN,CAAC;IAEH;EACF,CAAC;AACH;AAGA,OAAO,MAAMC,aAAa,GAAG,MAAAA,CAAOC,IAAI,GAAG,CAAC,EAAEC,UAAU,GAAG,EAAE,EAAEC,OAAO,GAAG,CAAC,KAAK;EAC7EC,OAAO,CAACC,GAAG,CAAC,uCAAuCJ,IAAI,cAAcC,UAAU,eAAeC,OAAO,EAAE,CAAC;EACxG,MAAM,IAAIG,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;EACtD,MAAME,WAAW,GAAG/B,MAAM,CAACC,wBAAwB,IAAI,EAAE;EACzD,MAAM+B,gBAAgB,GAAGR,UAAU,GAAGO,WAAW,CAACE,MAAM,CAACC,GAAG,IAAIA,GAAG,CAAC/B,IAAI,CAACgC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACZ,UAAU,CAACW,WAAW,CAAC,CAAC,CAAC,CAAC,GAAGJ,WAAW;EACxI,MAAMM,aAAa,GAAGL,gBAAgB,CAACM,MAAM;EAC7C,MAAMC,UAAU,GAAGC,IAAI,CAACC,IAAI,CAACJ,aAAa,GAAGZ,OAAO,CAAC;EACrD,MAAMiB,iBAAiB,GAAGV,gBAAgB,CAACW,KAAK,CAAC,CAACpB,IAAI,GAAG,CAAC,IAAIE,OAAO,EAAEF,IAAI,GAAGE,OAAO,CAAC;EACtF,OAAOG,OAAO,CAACC,OAAO,CAAC;IAAEe,QAAQ,EAAEF,iBAAiB;IAAEH,UAAU;IAAEF,aAAa;IAAEQ,WAAW,EAAEtB;EAAK,CAAC,CAAC;AACvG,CAAC;AAED,OAAO,MAAMuB,eAAe,GAAG,MAAOC,QAAQ,IAAK;EACjDrB,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;EAC5C,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;EACtD,IAAImB,YAAY,GAAG,cAAc;EACjC,IAAID,QAAQ,CAACE,GAAG,CAAC,SAAS,CAAC,EAAE;IAC3B,MAAMC,IAAI,GAAGH,QAAQ,CAACI,GAAG,CAAC,SAAS,CAAC;IACpC,IAAID,IAAI,YAAYE,IAAI,EAAEJ,YAAY,GAAGE,IAAI,CAAC/C,IAAI;EACpD;EACA,MAAMkD,UAAU,GAAG;IAAEnD,EAAE,EAAE,UAAUI,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;IAAEJ,IAAI,EAAE6C,YAAY;IAAE5C,SAAS,EAAE,sDAAsD4C,YAAY,EAAE;IAAE3C,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC;EAAE,CAAC;EAC5LR,MAAM,CAACC,wBAAwB,CAACqD,OAAO,CAACD,UAAU,CAAC;EACnD,OAAOzB,OAAO,CAACC,OAAO,CAACwB,UAAU,CAAC;AACpC,CAAC;AAED,OAAO,MAAME,mBAAmB,GAAG,MAAAA,CAAOC,SAAS,EAAEC,OAAO,KAAK;EAC/D/B,OAAO,CAACC,GAAG,CAAC,2CAA2C6B,SAAS,eAAeC,OAAO,EAAE,CAAC;EACzF,MAAM,IAAI7B,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;EACtD,MAAM6B,QAAQ,GAAG1D,MAAM,CAACC,wBAAwB,CAAC0D,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAC1D,EAAE,KAAKsD,SAAS,CAAC;EACnF,IAAIE,QAAQ,KAAK,CAAC,CAAC,EAAE;IACnB1D,MAAM,CAACC,wBAAwB,CAACyD,QAAQ,CAAC,CAACvD,IAAI,GAAGsD,OAAO;IACxD,OAAO7B,OAAO,CAACC,OAAO,CAAC;MAAE,GAAG7B,MAAM,CAACC,wBAAwB,CAACyD,QAAQ;IAAE,CAAC,CAAC;EAC1E;EACA,OAAO9B,OAAO,CAACiC,MAAM,CAAC,IAAIC,KAAK,CAAC,UAAUN,SAAS,aAAa,CAAC,CAAC;AACpE,CAAC;AAED,OAAO,MAAMO,eAAe,GAAG,MAAOP,SAAS,IAAK;EAClD9B,OAAO,CAACC,GAAG,CAAC,uCAAuC6B,SAAS,EAAE,CAAC;EAC/D,MAAM,IAAI5B,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;EACtD,MAAMmC,aAAa,GAAGhE,MAAM,CAACC,wBAAwB,CAACqC,MAAM;EAC5DtC,MAAM,CAACC,wBAAwB,GAAGD,MAAM,CAACC,wBAAwB,CAACgC,MAAM,CAAC2B,CAAC,IAAIA,CAAC,CAAC1D,EAAE,KAAKsD,SAAS,CAAC;EACjG,IAAIxD,MAAM,CAACC,wBAAwB,CAACqC,MAAM,GAAG0B,aAAa,EAAE,OAAOpC,OAAO,CAACC,OAAO,CAAC;IAAEoC,OAAO,EAAE,OAAOT,SAAS;EAAY,CAAC,CAAC;EAC5H,OAAO5B,OAAO,CAACiC,MAAM,CAAC,IAAIC,KAAK,CAAC,UAAUN,SAAS,0BAA0B,CAAC,CAAC;AACjF,CAAC;AAED,OAAO,MAAMU,gBAAgB,GAAG,MAAAA,CAAOV,SAAS,EAAEjC,IAAI,GAAG,CAAC,KAAK;EAC7DG,OAAO,CAACC,GAAG,CAAC,2CAA2C6B,SAAS,WAAWjC,IAAI,EAAE,CAAC;EAClF,MAAM,IAAIK,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;EACtD,IAAI,CAAC2B,SAAS,EAAE,OAAO5B,OAAO,CAACC,OAAO,CAAC;IAAEsC,WAAW,EAAE,EAAE;IAAE5B,UAAU,EAAE,CAAC;IAAEM,WAAW,EAAEtB,IAAI;IAAE6C,gBAAgB,EAAE;EAAE,CAAC,CAAC;EAClH,MAAMC,iBAAiB,GAAGrE,MAAM,CAACS,iBAAiB,CAAC+C,SAAS,CAAC,IAAI,EAAE;EACnE,MAAM/B,OAAO,GAAG,CAAC;EACjB,MAAM2C,gBAAgB,GAAGC,iBAAiB,CAAC/B,MAAM;EACjD,MAAMC,UAAU,GAAGC,IAAI,CAACC,IAAI,CAAC2B,gBAAgB,GAAG3C,OAAO,CAAC;EACxD,MAAM6C,oBAAoB,GAAGD,iBAAiB,CAAC1B,KAAK,CAAC,CAACpB,IAAI,GAAG,CAAC,IAAIE,OAAO,EAAEF,IAAI,GAAGE,OAAO,CAAC;EAC1F,OAAOG,OAAO,CAACC,OAAO,CAAC;IAAEsC,WAAW,EAAEG,oBAAoB;IAAE/B,UAAU;IAAEM,WAAW,EAAEtB,IAAI;IAAE6C;EAAiB,CAAC,CAAC;AAChH,CAAC;AAED,OAAO,MAAMG,mBAAmB,GAAG,MAAAA,CAAOC,QAAQ,EAAEpD,KAAK,KAAK;EAC5DM,OAAO,CAACC,GAAG,CAAC,8CAA8C6C,QAAQ,YAAYpD,KAAK,EAAE,CAAC;EACtF,MAAM,IAAIQ,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;EACtD;EACA,KAAK,MAAM4C,KAAK,IAAIzE,MAAM,CAACS,iBAAiB,EAAE;IAC5C,KAAK,MAAMiE,GAAG,IAAI1E,MAAM,CAACS,iBAAiB,CAACgE,KAAK,CAAC,EAAE;MACjD,MAAME,MAAM,GAAGD,GAAG,CAAC3D,OAAO,CAAC6D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC3E,EAAE,KAAKsE,QAAQ,CAAC;MACvD,IAAIG,MAAM,EAAEA,MAAM,CAACvD,KAAK,GAAGA,KAAK;IAClC;EACF;EACA,OAAOQ,OAAO,CAACC,OAAO,CAAC;IAAE3B,EAAE,EAAEsE,QAAQ;IAAEpD,KAAK,EAAEA,KAAK;IAAE6C,OAAO,EAAE;EAAiB,CAAC,CAAC;AACnF,CAAC;AAED,OAAO,MAAMa,6BAA6B,GAAG,MAAAA,CAAA,KAAY;EACvDpD,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;EAC1D,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;EACtD,OAAOD,OAAO,CAACC,OAAO,CAAC,CACrB;IAAElB,MAAM,EAAE,qCAAqC;IAAEC,aAAa,EAAE,GAAG;IAAEC,YAAY,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAe,CAAC,EACrH;IAAEH,MAAM,EAAE,kCAAkC;IAAEC,aAAa,EAAE,GAAG;IAAEC,YAAY,EAAE,GAAG;IAAEC,WAAW,EAAE;EAAmC,CAAC,CACvI,CAAC;AACJ,CAAC;AAED,OAAO,MAAMiE,kBAAkB,GAAG,MAAOC,cAAc,IAAK;EAC1DtD,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEqD,cAAc,CAAC;EACrE,MAAM,IAAIpD,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;EACtD,MAAMoD,KAAK,GAAG,OAAO3E,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIyE,cAAc,CAACtE,UAAU,EAAE;EAC9D,MAAMwE,aAAa,GAAG;IACpBhF,EAAE,EAAE+E,KAAK;IAAE,GAAGD,cAAc;IAAE3E,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC;IAClEO,OAAO,EAAE;IAAE;IACT;MAAEb,EAAE,EAAE,OAAO+E,KAAK,SAAS;MAAEjE,QAAQ,EAAE,WAAW;MAAEC,UAAU,EAAE,kBAAkB;MAAEC,MAAM,EAAE,YAAY;MAAEC,iBAAiB,EAAE,IAAI;MAAEC,KAAK,EAAE,IAAI;MAAEC,aAAa,EAAE;IAAK,CAAC,EACrK;MAAEnB,EAAE,EAAE,OAAO+E,KAAK,SAAS;MAAEjE,QAAQ,EAAE,WAAW;MAAEC,UAAU,EAAE,oBAAoB;MAAEC,MAAM,EAAE,YAAY;MAAEC,iBAAiB,EAAE,IAAI;MAAEC,KAAK,EAAE,IAAI;MAAEC,aAAa,EAAE;IAAK,CAAC;EAE3K,CAAC;EACD,IAAI,CAACrB,MAAM,CAACS,iBAAiB,CAACuE,cAAc,CAACtE,UAAU,CAAC,EAAEV,MAAM,CAACS,iBAAiB,CAACuE,cAAc,CAACtE,UAAU,CAAC,GAAG,EAAE;EAClHV,MAAM,CAACS,iBAAiB,CAACuE,cAAc,CAACtE,UAAU,CAAC,CAAC4C,OAAO,CAAC4B,aAAa,CAAC;EAC1E,OAAOtD,OAAO,CAACC,OAAO,CAACqD,aAAa,CAAC;AACvC,CAAC;AAED,OAAO,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;EACrCzD,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;EACxC,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;EACtD,OAAOD,OAAO,CAACC,OAAO,CAAC,CACrB;IAAE3B,EAAE,EAAE,WAAW;IAAEC,IAAI,EAAE,kBAAkB;IAAEiF,WAAW,EAAE,sBAAsB;IAAEC,SAAS,EAAE,IAAI;IAAEC,cAAc,EAAE;EAAuB,CAAC,EAC3I;IAAEpF,EAAE,EAAE,WAAW;IAAEC,IAAI,EAAE,oBAAoB;IAAEiF,WAAW,EAAE,4BAA4B;IAAEC,SAAS,EAAE,IAAI;IAAEC,cAAc,EAAE;EAAoB,CAAC,EAChJ;IAAEpF,EAAE,EAAE,WAAW;IAAEC,IAAI,EAAE,gBAAgB;IAAEiF,WAAW,EAAE,kBAAkB;IAAEC,SAAS,EAAE,KAAK;IAAEC,cAAc,EAAE;EAAqB,CAAC,EACpI;IAAEpF,EAAE,EAAE,WAAW;IAAEC,IAAI,EAAE,gBAAgB;IAAEiF,WAAW,EAAE,yBAAyB;IAAEC,SAAS,EAAE,IAAI;IAAEC,cAAc,EAAE;EAAqB,CAAC,CAC3I,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,aAAa,GAAG,MAAAA,CAAOC,OAAO,EAAEC,IAAI,KAAK;EACpD/D,OAAO,CAACC,GAAG,CAAC,qCAAqC6D,OAAO,SAAS,EAAEC,IAAI,CAAC;EACxE,MAAM,IAAI7D,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;EACtD;EACA,OAAOD,OAAO,CAACC,OAAO,CAAC;IAAE3B,EAAE,EAAEsF,OAAO;IAAE,GAAGC,IAAI;IAAExB,OAAO,EAAE;EAAiB,CAAC,CAAC;AAC7E,CAAC;;AAED;AACA,MAAMyB,8BAA8B,GAAG,MAAAA,CAAA,KAAY;EACjDhE,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;EAC3D,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,EAAE,CAAC,CAAC;EACrD;EACA,MAAM8D,kBAAkB,GAAGC,MAAM,CAACC,MAAM,CAAC7F,MAAM,CAACS,iBAAiB,IAAI,CAAC,CAAC,CAAC,CAACqF,IAAI,CAAC,CAAC;EAC/E,OAAOlE,OAAO,CAACC,OAAO,CAAC8D,kBAAkB,CAAC;AAC5C,CAAC;AAED,OAAO,MAAMI,aAAa,GAAG,MAAAA,CAAA,KAAY;EACvCrE,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;EAC1C,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;EACtD,IAAI;IACF,MAAMmE,MAAM,GAAG,MAAMb,WAAW,CAAC,CAAC;IAClC,MAAMhB,WAAW,GAAG,MAAMuB,8BAA8B,CAAC,CAAC;IAC1D,OAAO9D,OAAO,CAACC,OAAO,CAAC;MAAEmE,MAAM;MAAE7B;IAAY,CAAC,CAAC;EACjD,CAAC,CAAC,OAAO8B,KAAK,EAAE;IACdvE,OAAO,CAACuE,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IACxD,OAAOrE,OAAO,CAACiC,MAAM,CAACoC,KAAK,CAAC;EAC9B;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}