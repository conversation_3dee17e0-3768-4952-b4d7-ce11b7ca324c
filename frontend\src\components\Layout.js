import React from 'react';
import { Link, Outlet, useLocation } from 'react-router-dom';
import AppBar from '@mui/material/AppBar';
import Box from '@mui/material/Box';
import Toolbar from '@mui/material/Toolbar';
import Typography from '@mui/material/Typography';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import PlatformSwitcher from './navigation/PlatformSwitcher'; // Import PlatformSwitcher

const modelTestWorkbenchNavItems = [
  { label: 'Trademark', path: '/trademark' },
  { label: 'Copyright', path: '/copyright' },
  { label: 'Patent', path: '/patent' },
  { label: 'Dashboard', path: '/dashboard' },
  { label: 'Settings', path: '/settings' },
];

const patentVizNavItems = [
  { label: 'Dashboard', path: '/patent-viz/dashboard' },
  { label: 'Explorer', path: '/patent-viz/explorer' },
  // Add other patent viz specific items here, e.g., Settings
  // { label: 'Settings', path: '/patent-viz/settings' },
];

function Layout() {
  const location = useLocation();

  const isPatentViz = location.pathname.startsWith('/patent-viz');
  const headerTitle = isPatentViz ? "Patent Visualization Platform" : "ModelTests Workbench";
  const currentNavItems = isPatentViz ? patentVizNavItems : modelTestWorkbenchNavItems;

  // Find the current tab index based on the path
  const currentTabIndex = currentNavItems.findIndex(item => location.pathname.startsWith(item.path));
  const footerText = isPatentViz ? "Patent Visualization Platform" : "ModelTests Workbench";

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      <AppBar position="static">
        <Toolbar>
          <PlatformSwitcher /> {/* Add PlatformSwitcher here */}
          <Typography variant="h6" component="div" sx={{ ml: 2, flexGrow: 1 }}> {/* Added ml for spacing */}
            {headerTitle}
          </Typography>
          <Tabs
            value={currentTabIndex === -1 ? false : currentTabIndex} // Handle cases where no tab matches
            textColor="inherit"
            indicatorColor="secondary"
            aria-label="navigation tabs"
          >
            {currentNavItems.map((item, index) => (
              <Tab
                key={item.label}
                label={item.label}
                component={Link}
                to={item.path}
                value={index} // Use index as value for Tabs component
              />
            ))}
          </Tabs>
        </Toolbar>
      </AppBar>
      <Box component="main" sx={{ flexGrow: 1, p: 3 }}>
        {/* Child routes will render here */}
        <Outlet />
      </Box>
      <Box component="footer" sx={{ p: 2, mt: 'auto', backgroundColor: 'grey.200' }}>
        <Typography variant="body2" color="text.secondary" align="center">
          {'© '}
          {new Date().getFullYear()}
          {` ${footerText}`}
        </Typography>
      </Box>
    </Box>
  );
}

export default Layout;