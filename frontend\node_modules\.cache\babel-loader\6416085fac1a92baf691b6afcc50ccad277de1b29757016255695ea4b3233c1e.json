{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents\\\\Programing\\\\TRO\\\\ModelTestsWorkbench\\\\frontend\\\\src\\\\App.js\";\nimport React from 'react';\nimport { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';\nimport Layout from './components/Layout';\nimport TrademarkPage from './pages/model-test-workbench/TrademarkPage';\nimport CopyrightPage from './pages/model-test-workbench/CopyrightPage';\nimport PatentPage from './pages/model-test-workbench/PatentPage';\nimport DashboardPage from './pages/model-test-workbench/DashboardPage';\nimport SettingsPage from './pages/model-test-workbench/SettingsPage';\nimport PatentPlatformPage from './pages/PatentPlatformPage'; // Import the new Patent Platform Page\nimport ResultsPage from './pages/boundingbox/ResultsPage'; // Import Bounding Box Results Page\nimport ModelPage from './pages/boundingbox/ModelPage'; // Import Bounding Box Model Page\nimport BoundingBoxPictureManagementPage from './pages/boundingbox/PictureManagementPage'; // Import Bounding Box Picture Management Page\nimport RankPage from './pages/boundingbox/RankPage'; // Import Bounding Box Rank Page\nimport './App.css'; // Keep default styles for now\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(BrowserRouter, {\n    children: /*#__PURE__*/_jsxDEV(Routes, {\n      children: /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(Layout, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 34\n        }, this),\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          index: true,\n          element: /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/dashboard\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 33\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"trademark\",\n          element: /*#__PURE__*/_jsxDEV(TrademarkPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 44\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"copyright\",\n          element: /*#__PURE__*/_jsxDEV(CopyrightPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 44\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"patent\",\n          element: /*#__PURE__*/_jsxDEV(PatentPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"dashboard\",\n          element: /*#__PURE__*/_jsxDEV(DashboardPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 44\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"settings\",\n          element: /*#__PURE__*/_jsxDEV(SettingsPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"boundingbox\",\n          element: /*#__PURE__*/_jsxDEV(ResultsPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 46\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"boundingbox/models\",\n          element: /*#__PURE__*/_jsxDEV(ModelPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 53\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"boundingbox/pictures\",\n          element: /*#__PURE__*/_jsxDEV(BoundingBoxPictureManagementPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 55\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"boundingbox/ranking\",\n          element: /*#__PURE__*/_jsxDEV(RankPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 54\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this), \" \", /*#__PURE__*/_jsxDEV(Route, {\n          path: \"patent-viz/*\",\n          element: /*#__PURE__*/_jsxDEV(PatentPlatformPage, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 47\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this), \" \", /*#__PURE__*/_jsxDEV(Route, {\n          path: \"*\",\n          element: /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/dashboard\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Routes", "Route", "Navigate", "Layout", "TrademarkPage", "CopyrightPage", "PatentPage", "DashboardPage", "SettingsPage", "PatentPlatformPage", "ResultsPage", "ModelPage", "BoundingBoxPictureManagementPage", "RankPage", "jsxDEV", "_jsxDEV", "App", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "index", "to", "replace", "_c", "$RefreshReg$"], "sources": ["D:/Documents/Programing/TRO/ModelTestsWorkbench/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\r\nimport { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';\r\nimport Layout from './components/Layout';\r\nimport TrademarkPage from './pages/model-test-workbench/TrademarkPage';\r\nimport CopyrightPage from './pages/model-test-workbench/CopyrightPage';\r\nimport PatentPage from './pages/model-test-workbench/PatentPage';\r\nimport DashboardPage from './pages/model-test-workbench/DashboardPage';\r\nimport SettingsPage from './pages/model-test-workbench/SettingsPage';\r\nimport PatentPlatformPage from './pages/PatentPlatformPage'; // Import the new Patent Platform Page\r\nimport ResultsPage from './pages/boundingbox/ResultsPage'; // Import Bounding Box Results Page\r\nimport ModelPage from './pages/boundingbox/ModelPage'; // Import Bounding Box Model Page\r\nimport BoundingBoxPictureManagementPage from './pages/boundingbox/PictureManagementPage'; // Import Bounding Box Picture Management Page\r\nimport RankPage from './pages/boundingbox/RankPage'; // Import Bounding Box Rank Page\r\nimport './App.css'; // Keep default styles for now\r\n\r\nfunction App() {\r\n  return (\r\n    <BrowserRouter>\r\n      <Routes>\r\n        <Route path=\"/\" element={<Layout />}>\r\n          {/* Default route redirects to Dashboard */}\r\n          <Route index element={<Navigate to=\"/dashboard\" replace />} />\r\n          <Route path=\"trademark\" element={<TrademarkPage />} />\r\n          <Route path=\"copyright\" element={<CopyrightPage />} />\r\n          <Route path=\"patent\" element={<PatentPage />} />\r\n          <Route path=\"dashboard\" element={<DashboardPage />} />\r\n          <Route path=\"settings\" element={<SettingsPage />} />\r\n          <Route path=\"boundingbox\" element={<ResultsPage />} />\r\n          <Route path=\"boundingbox/models\" element={<ModelPage />} />\r\n          <Route path=\"boundingbox/pictures\" element={<BoundingBoxPictureManagementPage />} />\r\n          <Route path=\"boundingbox/ranking\" element={<RankPage />} /> {/* New Bounding Box Ranking Route */}\r\n          <Route path=\"patent-viz/*\" element={<PatentPlatformPage />} /> {/* Route for Patent Visualization Platform */}\r\n          {/* Add other nested routes or a 404 handler here if needed */}\r\n          <Route path=\"*\" element={<Navigate to=\"/dashboard\" replace />} /> {/* Basic fallback */}\r\n        </Route>\r\n      </Routes>\r\n    </BrowserRouter>\r\n  );\r\n}\r\n\r\nexport default App;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACzE,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,aAAa,MAAM,4CAA4C;AACtE,OAAOC,aAAa,MAAM,4CAA4C;AACtE,OAAOC,UAAU,MAAM,yCAAyC;AAChE,OAAOC,aAAa,MAAM,4CAA4C;AACtE,OAAOC,YAAY,MAAM,2CAA2C;AACpE,OAAOC,kBAAkB,MAAM,4BAA4B,CAAC,CAAC;AAC7D,OAAOC,WAAW,MAAM,iCAAiC,CAAC,CAAC;AAC3D,OAAOC,SAAS,MAAM,+BAA+B,CAAC,CAAC;AACvD,OAAOC,gCAAgC,MAAM,2CAA2C,CAAC,CAAC;AAC1F,OAAOC,QAAQ,MAAM,8BAA8B,CAAC,CAAC;AACrD,OAAO,WAAW,CAAC,CAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEpB,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAAChB,aAAa;IAAAkB,QAAA,eACZF,OAAA,CAACf,MAAM;MAAAiB,QAAA,eACLF,OAAA,CAACd,KAAK;QAACiB,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEJ,OAAA,CAACZ,MAAM;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAN,QAAA,gBAElCF,OAAA,CAACd,KAAK;UAACuB,KAAK;UAACL,OAAO,eAAEJ,OAAA,CAACb,QAAQ;YAACuB,EAAE,EAAC,YAAY;YAACC,OAAO;UAAA;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC9DR,OAAA,CAACd,KAAK;UAACiB,IAAI,EAAC,WAAW;UAACC,OAAO,eAAEJ,OAAA,CAACX,aAAa;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtDR,OAAA,CAACd,KAAK;UAACiB,IAAI,EAAC,WAAW;UAACC,OAAO,eAAEJ,OAAA,CAACV,aAAa;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtDR,OAAA,CAACd,KAAK;UAACiB,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAEJ,OAAA,CAACT,UAAU;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAChDR,OAAA,CAACd,KAAK;UAACiB,IAAI,EAAC,WAAW;UAACC,OAAO,eAAEJ,OAAA,CAACR,aAAa;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtDR,OAAA,CAACd,KAAK;UAACiB,IAAI,EAAC,UAAU;UAACC,OAAO,eAAEJ,OAAA,CAACP,YAAY;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpDR,OAAA,CAACd,KAAK;UAACiB,IAAI,EAAC,aAAa;UAACC,OAAO,eAAEJ,OAAA,CAACL,WAAW;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtDR,OAAA,CAACd,KAAK;UAACiB,IAAI,EAAC,oBAAoB;UAACC,OAAO,eAAEJ,OAAA,CAACJ,SAAS;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3DR,OAAA,CAACd,KAAK;UAACiB,IAAI,EAAC,sBAAsB;UAACC,OAAO,eAAEJ,OAAA,CAACH,gCAAgC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpFR,OAAA,CAACd,KAAK;UAACiB,IAAI,EAAC,qBAAqB;UAACC,OAAO,eAAEJ,OAAA,CAACF,QAAQ;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,KAAC,eAC5DR,OAAA,CAACd,KAAK;UAACiB,IAAI,EAAC,cAAc;UAACC,OAAO,eAAEJ,OAAA,CAACN,kBAAkB;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,KAAC,eAE/DR,OAAA,CAACd,KAAK;UAACiB,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEJ,OAAA,CAACb,QAAQ;YAACuB,EAAE,EAAC,YAAY;YAACC,OAAO;UAAA;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,KAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEpB;AAACI,EAAA,GAvBQX,GAAG;AAyBZ,eAAeA,GAAG;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}