import apiClient from './api'; // Assuming api.js will now export apiClient

// --- Patent Visualization Platform ---

// Corresponds to GET /api/v1/patents/dashboard/statistics
export const getPatentDashboardStatistics = (refresh = false) => {
  return apiClient.get(`/v1/patents/dashboard/statistics`, { params: { refresh } });
};

// Corresponds to GET /api/v1/patents/explore
export const getPatentsForExploration = (filters) => {
  // Filters object includes various search and pagination parameters
  const params = { ...filters };
  if (Array.isArray(params.patent_types)) {
    params.patent_types = params.patent_types.join(',');
  }
  if (Array.isArray(params.selected_columns)) {
    params.columns = params.selected_columns.join(','); // API expects 'columns'
    delete params.selected_columns;
  }
  if (params.page) params.page = Number(params.page);
  if (params.per_page) params.per_page = Number(params.per_page);

  return apiClient.get(`/v1/patents/explore`, { params });
};

// Corresponds to GET /api/v1/patents/explore/<patent_id>
export const getPatentDetails = (patentId) => {
  return apiClient.get(`/v1/patents/explore/${patentId}`);
};

// Corresponds to GET /api/v1/patents/explore/<patent_id>/images
export const getPatentImagesInfo = (patentId) => {
  return apiClient.get(`/v1/patents/explore/${patentId}/images`);
};
