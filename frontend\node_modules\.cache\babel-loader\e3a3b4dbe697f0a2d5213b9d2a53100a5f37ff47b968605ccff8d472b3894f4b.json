{"ast": null, "code": "import apiClient from './api'; // Assuming api.js will now export apiClient\n\n// --- Data Management ---\nexport const uploadImages = formData => {\n  return apiClient.post(`/data/images`, formData, {\n    headers: {\n      'Content-Type': 'multipart/form-data'\n    }\n  });\n};\nexport const listImages = params => {\n  const queryParams = {\n    ...params\n  };\n  if (typeof queryParams.missing_ip_owner === 'boolean') {\n    queryParams.missing_ip_owner = String(queryParams.missing_ip_owner);\n  }\n  return apiClient.get(`/data/images`, {\n    params: queryParams\n  });\n};\nexport const getImageFile = imageId => {\n  return apiClient.get(`/data/images/file/${imageId}`, {\n    responseType: 'blob'\n  });\n};\nexport const deleteImage = imageId => {\n  return apiClient.delete(`/data/images/${imageId}`);\n};\nexport const updateImageIpOwner = (imageId, ipOwner) => {\n  return apiClient.put(`/data/images/${imageId}/ip_owner`, {\n    ip_owner: ipOwner\n  });\n};\n\n// --- Model Management ---\nexport const listModels = params => {\n  const queryParams = {\n    ...params\n  };\n  delete queryParams.ip_category; // API does not use ip_category here\n  return apiClient.get(`/models`, {\n    params: queryParams\n  });\n};\nexport const refreshModels = () => {\n  return apiClient.post(`/models/refresh`);\n};\n\n// --- Feature Computation ---\nexport const computeFeatures = ipCategory => {\n  return apiClient.post(`/tasks/compute-features/${ipCategory}`);\n};\n\n// --- Combined Scores ---\nexport const getCombinedScores = params => {\n  const queryParams = {\n    ...params\n  };\n  if (typeof queryParams.is_active === 'boolean') {\n    queryParams.is_active = String(queryParams.is_active);\n  }\n  return apiClient.get(`/combined-scores`, {\n    params: queryParams\n  });\n};\nexport const updateCombinedScoreConfig = (configId, configData) => {\n  const payload = {\n    ...configData\n  };\n  delete payload.ip_category;\n  return apiClient.put(`/combined-scores/${configId}`, payload);\n};\nexport const createCombinedScoreConfig = configData => {\n  return apiClient.post(`/combined-scores`, configData);\n};\nexport const deleteCombinedScoreConfig = configId => {\n  return apiClient.delete(`/combined-scores/${configId}`);\n};\n\n// --- Results ---\nexport const getResultsByModel = params => {\n  return apiClient.get(`/results/by-model`, {\n    params\n  });\n};\nexport const getResultsByProduct = (productImageId, params) => {\n  return apiClient.get(`/results/by-product/${productImageId}`, {\n    params\n  });\n};\nexport const addGroundTruth = (productImageId, correctIpImageId) => {\n  return apiClient.post(`/data/ground_truth`, {\n    product_image_id: productImageId,\n    correct_ip_image_id: correctIpImageId\n  });\n};\nexport const removeGroundTruth = (productImageId, correctIpImageId) => {\n  return apiClient.delete(`/data/ground_truth`, {\n    data: {\n      product_image_id: productImageId,\n      correct_ip_image_id: correctIpImageId\n    }\n  });\n};\n\n// --- Dashboard ---\nexport const getPerformanceSummary = ipCategory => {\n  return apiClient.get(`/dashboard/performance-summary`, {\n    params: {\n      ip_category: ipCategory\n    }\n  });\n};\nexport const getScoreDistribution = (modelId, ipCategory) => {\n  return apiClient.get(`/dashboard/score-distribution`, {\n    params: {\n      model_id: modelId,\n      ip_category: ipCategory\n    }\n  });\n};\nexport const getConfusionMatrix = (modelId, ipCategory, threshold) => {\n  return apiClient.get(`/dashboard/confusion-matrix`, {\n    params: {\n      model_id: modelId,\n      ip_category: ipCategory,\n      threshold: threshold\n    }\n  });\n};\n\n// --- Qdrant Management ---\nexport const getQdrantCollections = async () => {\n  try {\n    const response = await apiClient.get(`/qdrant/collections`);\n    return response.data;\n  } catch (error) {\n    console.error(\"Error fetching Qdrant collections:\", error);\n    throw error;\n  }\n};\nexport const deleteQdrantCollection = async collectionName => {\n  try {\n    const encodedCollectionName = encodeURIComponent(collectionName);\n    const response = await apiClient.delete(`/qdrant/collections/${encodedCollectionName}`);\n    return response.data;\n  } catch (error) {\n    console.error(`Error deleting Qdrant collection ${collectionName}:`, error);\n    throw error;\n  }\n};\n\n// --- Task Status ---\nexport const getTaskStatus = taskId => {\n  return apiClient.get(`/tasks/status/${taskId}`);\n};\nexport const triggerComputeCombinedScores = ipCategory => {\n  return apiClient.post(`/tasks/compute-combined-scores/${ipCategory}`);\n};", "map": {"version": 3, "names": ["apiClient", "uploadImages", "formData", "post", "headers", "listImages", "params", "queryParams", "missing_ip_owner", "String", "get", "getImageFile", "imageId", "responseType", "deleteImage", "delete", "updateImageIpOwner", "ip<PERSON><PERSON><PERSON>", "put", "ip_owner", "listModels", "ip_category", "refreshModels", "computeFeatures", "ipCategory", "getCombinedScores", "is_active", "updateCombinedScoreConfig", "configId", "configData", "payload", "createCombinedScoreConfig", "deleteCombinedScoreConfig", "getResultsByModel", "getResultsByProduct", "productImageId", "addGroundTruth", "correctIpImageId", "product_image_id", "correct_ip_image_id", "removeGroundTruth", "data", "getPerformanceSummary", "getScoreDistribution", "modelId", "model_id", "getConfusionMatrix", "threshold", "getQdrantCollections", "response", "error", "console", "deleteQdrantCollection", "collectionName", "encodedCollectionName", "encodeURIComponent", "getTaskStatus", "taskId", "triggerComputeCombinedScores"], "sources": ["D:/Documents/Programing/TRO/ModelTestsWorkbench/frontend/src/services/api_model_workbench.js"], "sourcesContent": ["import apiClient from './api'; // Assuming api.js will now export apiClient\r\n\r\n// --- Data Management ---\r\nexport const uploadImages = (formData) => {\r\n  return apiClient.post(`/data/images`, formData, {\r\n    headers: { 'Content-Type': 'multipart/form-data' },\r\n  });\r\n};\r\n\r\nexport const listImages = (params) => {\r\n  const queryParams = { ...params };\r\n  if (typeof queryParams.missing_ip_owner === 'boolean') {\r\n    queryParams.missing_ip_owner = String(queryParams.missing_ip_owner);\r\n  }\r\n  return apiClient.get(`/data/images`, { params: queryParams });\r\n};\r\n\r\nexport const getImageFile = (imageId) => {\r\n  return apiClient.get(`/data/images/file/${imageId}`, { responseType: 'blob' });\r\n};\r\n\r\nexport const deleteImage = (imageId) => {\r\n  return apiClient.delete(`/data/images/${imageId}`);\r\n};\r\n\r\nexport const updateImageIpOwner = (imageId, ipOwner) => {\r\n  return apiClient.put(`/data/images/${imageId}/ip_owner`, { ip_owner: ipOwner });\r\n};\r\n\r\n// --- Model Management ---\r\nexport const listModels = (params) => {\r\n  const queryParams = { ...params };\r\n  delete queryParams.ip_category; // API does not use ip_category here\r\n  return apiClient.get(`/models`, { params: queryParams });\r\n};\r\n\r\nexport const refreshModels = () => {\r\n  return apiClient.post(`/models/refresh`);\r\n};\r\n\r\n// --- Feature Computation ---\r\nexport const computeFeatures = (ipCategory) => {\r\n  return apiClient.post(`/tasks/compute-features/${ipCategory}`);\r\n};\r\n\r\n// --- Combined Scores ---\r\nexport const getCombinedScores = (params) => {\r\n  const queryParams = { ...params };\r\n  if (typeof queryParams.is_active === 'boolean') {\r\n    queryParams.is_active = String(queryParams.is_active);\r\n  }\r\n  return apiClient.get(`/combined-scores`, { params: queryParams });\r\n};\r\n\r\nexport const updateCombinedScoreConfig = (configId, configData) => {\r\n  const payload = { ...configData };\r\n  delete payload.ip_category;\r\n  return apiClient.put(`/combined-scores/${configId}`, payload);\r\n};\r\n\r\nexport const createCombinedScoreConfig = (configData) => {\r\n  return apiClient.post(`/combined-scores`, configData);\r\n};\r\n\r\nexport const deleteCombinedScoreConfig = (configId) => {\r\n  return apiClient.delete(`/combined-scores/${configId}`);\r\n};\r\n\r\n// --- Results ---\r\nexport const getResultsByModel = (params) => {\r\n  return apiClient.get(`/results/by-model`, { params });\r\n};\r\n\r\nexport const getResultsByProduct = (productImageId, params) => {\r\n  return apiClient.get(`/results/by-product/${productImageId}`, { params });\r\n};\r\n\r\nexport const addGroundTruth = (productImageId, correctIpImageId) => {\r\n  return apiClient.post(`/data/ground_truth`, {\r\n    product_image_id: productImageId,\r\n    correct_ip_image_id: correctIpImageId\r\n  });\r\n};\r\n\r\nexport const removeGroundTruth = (productImageId, correctIpImageId) => {\r\n  return apiClient.delete(`/data/ground_truth`, {\r\n    data: {\r\n      product_image_id: productImageId,\r\n      correct_ip_image_id: correctIpImageId\r\n    }\r\n  });\r\n};\r\n\r\n// --- Dashboard ---\r\nexport const getPerformanceSummary = (ipCategory) => {\r\n  return apiClient.get(`/dashboard/performance-summary`, { params: { ip_category: ipCategory } });\r\n};\r\n\r\nexport const getScoreDistribution = (modelId, ipCategory) => {\r\n  return apiClient.get(`/dashboard/score-distribution`, { params: { model_id: modelId, ip_category: ipCategory } });\r\n};\r\n\r\nexport const getConfusionMatrix = (modelId, ipCategory, threshold) => {\r\n  return apiClient.get(`/dashboard/confusion-matrix`, { params: { model_id: modelId, ip_category: ipCategory, threshold: threshold } });\r\n};\r\n\r\n// --- Qdrant Management ---\r\nexport const getQdrantCollections = async () => {\r\n  try {\r\n    const response = await apiClient.get(`/qdrant/collections`);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error(\"Error fetching Qdrant collections:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const deleteQdrantCollection = async (collectionName) => {\r\n  try {\r\n    const encodedCollectionName = encodeURIComponent(collectionName);\r\n    const response = await apiClient.delete(`/qdrant/collections/${encodedCollectionName}`);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error(`Error deleting Qdrant collection ${collectionName}:`, error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// --- Task Status ---\r\nexport const getTaskStatus = (taskId) => {\r\n  return apiClient.get(`/tasks/status/${taskId}`);\r\n};\r\n\r\nexport const triggerComputeCombinedScores = (ipCategory) => {\r\n  return apiClient.post(`/tasks/compute-combined-scores/${ipCategory}`);\r\n};\r\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,OAAO,CAAC,CAAC;;AAE/B;AACA,OAAO,MAAMC,YAAY,GAAIC,QAAQ,IAAK;EACxC,OAAOF,SAAS,CAACG,IAAI,CAAC,cAAc,EAAED,QAAQ,EAAE;IAC9CE,OAAO,EAAE;MAAE,cAAc,EAAE;IAAsB;EACnD,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMC,UAAU,GAAIC,MAAM,IAAK;EACpC,MAAMC,WAAW,GAAG;IAAE,GAAGD;EAAO,CAAC;EACjC,IAAI,OAAOC,WAAW,CAACC,gBAAgB,KAAK,SAAS,EAAE;IACrDD,WAAW,CAACC,gBAAgB,GAAGC,MAAM,CAACF,WAAW,CAACC,gBAAgB,CAAC;EACrE;EACA,OAAOR,SAAS,CAACU,GAAG,CAAC,cAAc,EAAE;IAAEJ,MAAM,EAAEC;EAAY,CAAC,CAAC;AAC/D,CAAC;AAED,OAAO,MAAMI,YAAY,GAAIC,OAAO,IAAK;EACvC,OAAOZ,SAAS,CAACU,GAAG,CAAC,qBAAqBE,OAAO,EAAE,EAAE;IAAEC,YAAY,EAAE;EAAO,CAAC,CAAC;AAChF,CAAC;AAED,OAAO,MAAMC,WAAW,GAAIF,OAAO,IAAK;EACtC,OAAOZ,SAAS,CAACe,MAAM,CAAC,gBAAgBH,OAAO,EAAE,CAAC;AACpD,CAAC;AAED,OAAO,MAAMI,kBAAkB,GAAGA,CAACJ,OAAO,EAAEK,OAAO,KAAK;EACtD,OAAOjB,SAAS,CAACkB,GAAG,CAAC,gBAAgBN,OAAO,WAAW,EAAE;IAAEO,QAAQ,EAAEF;EAAQ,CAAC,CAAC;AACjF,CAAC;;AAED;AACA,OAAO,MAAMG,UAAU,GAAId,MAAM,IAAK;EACpC,MAAMC,WAAW,GAAG;IAAE,GAAGD;EAAO,CAAC;EACjC,OAAOC,WAAW,CAACc,WAAW,CAAC,CAAC;EAChC,OAAOrB,SAAS,CAACU,GAAG,CAAC,SAAS,EAAE;IAAEJ,MAAM,EAAEC;EAAY,CAAC,CAAC;AAC1D,CAAC;AAED,OAAO,MAAMe,aAAa,GAAGA,CAAA,KAAM;EACjC,OAAOtB,SAAS,CAACG,IAAI,CAAC,iBAAiB,CAAC;AAC1C,CAAC;;AAED;AACA,OAAO,MAAMoB,eAAe,GAAIC,UAAU,IAAK;EAC7C,OAAOxB,SAAS,CAACG,IAAI,CAAC,2BAA2BqB,UAAU,EAAE,CAAC;AAChE,CAAC;;AAED;AACA,OAAO,MAAMC,iBAAiB,GAAInB,MAAM,IAAK;EAC3C,MAAMC,WAAW,GAAG;IAAE,GAAGD;EAAO,CAAC;EACjC,IAAI,OAAOC,WAAW,CAACmB,SAAS,KAAK,SAAS,EAAE;IAC9CnB,WAAW,CAACmB,SAAS,GAAGjB,MAAM,CAACF,WAAW,CAACmB,SAAS,CAAC;EACvD;EACA,OAAO1B,SAAS,CAACU,GAAG,CAAC,kBAAkB,EAAE;IAAEJ,MAAM,EAAEC;EAAY,CAAC,CAAC;AACnE,CAAC;AAED,OAAO,MAAMoB,yBAAyB,GAAGA,CAACC,QAAQ,EAAEC,UAAU,KAAK;EACjE,MAAMC,OAAO,GAAG;IAAE,GAAGD;EAAW,CAAC;EACjC,OAAOC,OAAO,CAACT,WAAW;EAC1B,OAAOrB,SAAS,CAACkB,GAAG,CAAC,oBAAoBU,QAAQ,EAAE,EAAEE,OAAO,CAAC;AAC/D,CAAC;AAED,OAAO,MAAMC,yBAAyB,GAAIF,UAAU,IAAK;EACvD,OAAO7B,SAAS,CAACG,IAAI,CAAC,kBAAkB,EAAE0B,UAAU,CAAC;AACvD,CAAC;AAED,OAAO,MAAMG,yBAAyB,GAAIJ,QAAQ,IAAK;EACrD,OAAO5B,SAAS,CAACe,MAAM,CAAC,oBAAoBa,QAAQ,EAAE,CAAC;AACzD,CAAC;;AAED;AACA,OAAO,MAAMK,iBAAiB,GAAI3B,MAAM,IAAK;EAC3C,OAAON,SAAS,CAACU,GAAG,CAAC,mBAAmB,EAAE;IAAEJ;EAAO,CAAC,CAAC;AACvD,CAAC;AAED,OAAO,MAAM4B,mBAAmB,GAAGA,CAACC,cAAc,EAAE7B,MAAM,KAAK;EAC7D,OAAON,SAAS,CAACU,GAAG,CAAC,uBAAuByB,cAAc,EAAE,EAAE;IAAE7B;EAAO,CAAC,CAAC;AAC3E,CAAC;AAED,OAAO,MAAM8B,cAAc,GAAGA,CAACD,cAAc,EAAEE,gBAAgB,KAAK;EAClE,OAAOrC,SAAS,CAACG,IAAI,CAAC,oBAAoB,EAAE;IAC1CmC,gBAAgB,EAAEH,cAAc;IAChCI,mBAAmB,EAAEF;EACvB,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,MAAMG,iBAAiB,GAAGA,CAACL,cAAc,EAAEE,gBAAgB,KAAK;EACrE,OAAOrC,SAAS,CAACe,MAAM,CAAC,oBAAoB,EAAE;IAC5C0B,IAAI,EAAE;MACJH,gBAAgB,EAAEH,cAAc;MAChCI,mBAAmB,EAAEF;IACvB;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,OAAO,MAAMK,qBAAqB,GAAIlB,UAAU,IAAK;EACnD,OAAOxB,SAAS,CAACU,GAAG,CAAC,gCAAgC,EAAE;IAAEJ,MAAM,EAAE;MAAEe,WAAW,EAAEG;IAAW;EAAE,CAAC,CAAC;AACjG,CAAC;AAED,OAAO,MAAMmB,oBAAoB,GAAGA,CAACC,OAAO,EAAEpB,UAAU,KAAK;EAC3D,OAAOxB,SAAS,CAACU,GAAG,CAAC,+BAA+B,EAAE;IAAEJ,MAAM,EAAE;MAAEuC,QAAQ,EAAED,OAAO;MAAEvB,WAAW,EAAEG;IAAW;EAAE,CAAC,CAAC;AACnH,CAAC;AAED,OAAO,MAAMsB,kBAAkB,GAAGA,CAACF,OAAO,EAAEpB,UAAU,EAAEuB,SAAS,KAAK;EACpE,OAAO/C,SAAS,CAACU,GAAG,CAAC,6BAA6B,EAAE;IAAEJ,MAAM,EAAE;MAAEuC,QAAQ,EAAED,OAAO;MAAEvB,WAAW,EAAEG,UAAU;MAAEuB,SAAS,EAAEA;IAAU;EAAE,CAAC,CAAC;AACvI,CAAC;;AAED;AACA,OAAO,MAAMC,oBAAoB,GAAG,MAAAA,CAAA,KAAY;EAC9C,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMjD,SAAS,CAACU,GAAG,CAAC,qBAAqB,CAAC;IAC3D,OAAOuC,QAAQ,CAACR,IAAI;EACtB,CAAC,CAAC,OAAOS,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;IAC1D,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAME,sBAAsB,GAAG,MAAOC,cAAc,IAAK;EAC9D,IAAI;IACF,MAAMC,qBAAqB,GAAGC,kBAAkB,CAACF,cAAc,CAAC;IAChE,MAAMJ,QAAQ,GAAG,MAAMjD,SAAS,CAACe,MAAM,CAAC,uBAAuBuC,qBAAqB,EAAE,CAAC;IACvF,OAAOL,QAAQ,CAACR,IAAI;EACtB,CAAC,CAAC,OAAOS,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,oCAAoCG,cAAc,GAAG,EAAEH,KAAK,CAAC;IAC3E,MAAMA,KAAK;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMM,aAAa,GAAIC,MAAM,IAAK;EACvC,OAAOzD,SAAS,CAACU,GAAG,CAAC,iBAAiB+C,MAAM,EAAE,CAAC;AACjD,CAAC;AAED,OAAO,MAAMC,4BAA4B,GAAIlC,UAAU,IAAK;EAC1D,OAAOxB,SAAS,CAACG,IAAI,CAAC,kCAAkCqB,UAAU,EAAE,CAAC;AACvE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}