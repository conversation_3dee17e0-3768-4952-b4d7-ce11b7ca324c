import apiClient from './api'; // Assuming api.js exports the configured axios instance

// Initialize a global mock store for pictures if it doesn't exist
if (!window.mockBbPicturesListGlobal) {
  window.mockBbPicturesListGlobal = [
    { id: 'pic1', name: 'Cat_Photo_1.jpg', file_path: 'https://via.placeholder.com/150/92c952', created_at: new Date(Date.now() - 86400000 * 2).toISOString() },
    { id: 'pic2', name: 'Urban_Scene_A.png', file_path: 'https://via.placeholder.com/150/771796', created_at: new Date(Date.now() - 86400000).toISOString() },
    { id: 'pic3', name: 'Dog_Park_View.jpeg', file_path: 'https://via.placeholder.com/150/24f355', created_at: new Date().toISOString() },
    { id: 'pic4', name: 'Another_Cat.jpg', file_path: 'https://via.placeholder.com/150/f66b97', created_at: new Date(Date.now() - 3600000 * 5).toISOString() },
    { id: 'pic5', name: 'City_Skyline.png', file_path: 'https://via.placeholder.com/150/56a8c2', created_at: new Date(Date.now() - 3600000 * 10).toISOString() },
  ];
}

// Initialize global mock store for experiments if it doesn't exist
if (!window.mockBbExperiments) {
  window.mockBbExperiments = {
    'pic1': [
      {
        id: 'exp1_pic1', picture_id: 'pic1', prompt: 'A cat sitting on a red mat indoors.', resize_height: 512, resize_width: 512, output_type: 'Bounding Box', created_at: new Date(Date.now() - 3600000).toISOString(),
        results: [
          { id: 'res1_exp1', model_id: 'modelA_id', model_name: 'VisionMaster Pro', status: 'success', output_image_path: 'https://via.placeholder.com/200/00FF00/000000?Text=ModelA_CatMat', score: 8, error_message: null },
          { id: 'res2_exp1', model_id: 'modelB_id', model_name: 'ObjectNet Advanced', status: 'success', output_image_path: 'https://via.placeholder.com/200/00FF00/000000?Text=ModelB_CatMat', score: 6, error_message: null },
          { id: 'res3_exp1', model_id: 'modelC_id', model_name: 'QuickDetect v3', status: 'success', output_image_path: 'https://via.placeholder.com/200/00FF00/000000?Text=ModelC_CatMat', score: 7, error_message: null }
        ]
      },
      {
        id: 'exp2_pic1', picture_id: 'pic1', prompt: 'Multiple cats playing with toys.', resize_height: 640, resize_width: 640, output_type: 'Bounding Box + Segmentation Mask', created_at: new Date(Date.now() - 7200000).toISOString(),
        results: [
          { id: 'res4_exp2', model_id: 'modelA_id', model_name: 'VisionMaster Pro', status: 'success', output_image_path: 'https://via.placeholder.com/200/00AA00/FFFFFF?Text=ModelA_CatsToys', score: 9, error_message: null },
          { id: 'res5_exp2', model_id: 'modelC_id', model_name: 'QuickDetect v3', status: 'success', output_image_path: 'https://via.placeholder.com/200/006600/FFFFFF?Text=ModelC_CatsToys', score: 5, error_message: null }
        ]
      }
    ],
    'pic2': [
      {
        id: 'exp1_pic2', picture_id: 'pic2', prompt: 'Vehicles and pedestrians on a busy street.', resize_height: 768, resize_width: 1024, output_type: 'Bounding Box', created_at: new Date(Date.now() - 10800000).toISOString(),
        results: [
          { id: 'res6_exp1', model_id: 'modelB_id', model_name: 'ObjectNet Advanced', status: 'success', output_image_path: 'https://via.placeholder.com/200/FFFF00/000000?Text=ModelB_Urban', score: 7, error_message: null },
          { id: 'res7_exp1', model_id: 'modelD_id', model_name: 'UrbanScanner X', status: 'success', output_image_path: 'https://via.placeholder.com/200/FFFF00/000000?Text=ModelD_Urban', score: 9, error_message: null }
        ]
      }
    ],
    // Add more mock experiments for other picture IDs if needed
  };
}


export const getBbPictures = async (page = 1, searchTerm = '', perPage = 5) => {
  console.log(`API: getBbPictures (mocked) - Page: ${page}, Search: "${searchTerm}", PerPage: ${perPage}`);
  await new Promise(resolve => setTimeout(resolve, 100));
  const allPictures = window.mockBbPicturesListGlobal || [];
  const filteredPictures = searchTerm ? allPictures.filter(pic => pic.name.toLowerCase().includes(searchTerm.toLowerCase())) : allPictures;
  const totalPictures = filteredPictures.length;
  const totalPages = Math.ceil(totalPictures / perPage);
  const paginatedPictures = filteredPictures.slice((page - 1) * perPage, page * perPage);
  return Promise.resolve({ pictures: paginatedPictures, totalPages, totalPictures, currentPage: page });
};

export const uploadBbPicture = async (formData) => {
  console.log('API: uploadBbPicture (mocked)');
  await new Promise(resolve => setTimeout(resolve, 500));
  let mockFileName = 'uploaded.png';
  if (formData.has('picture')) {
    const file = formData.get('picture');
    if (file instanceof File) mockFileName = file.name;
  }
  const newPicture = { id: `newPic_${Date.now()}`, name: mockFileName, file_path: `https://via.placeholder.com/150/0000FF/FFFFFF?Text=${mockFileName}`, created_at: new Date().toISOString() };
  window.mockBbPicturesListGlobal.unshift(newPicture);
  return Promise.resolve(newPicture);
};

export const updateBbPictureName = async (pictureId, newName) => {
  console.log(`API: updateBbPictureName (mocked) - ID: ${pictureId}, New Name: ${newName}`);
  await new Promise(resolve => setTimeout(resolve, 100));
  const picIndex = window.mockBbPicturesListGlobal.findIndex(p => p.id === pictureId);
  if (picIndex !== -1) {
    window.mockBbPicturesListGlobal[picIndex].name = newName;
    return Promise.resolve({ ...window.mockBbPicturesListGlobal[picIndex] });
  }
  return Promise.reject(new Error(`Pic ID ${pictureId} not found.`));
};

export const deleteBbPicture = async (pictureId) => {
  console.log(`API: deleteBbPicture (mocked) - ID: ${pictureId}`);
  await new Promise(resolve => setTimeout(resolve, 300));
  const initialLength = window.mockBbPicturesListGlobal.length;
  window.mockBbPicturesListGlobal = window.mockBbPicturesListGlobal.filter(p => p.id !== pictureId);
  if (window.mockBbPicturesListGlobal.length < initialLength) return Promise.resolve({ message: `Pic ${pictureId} deleted.` });
  return Promise.reject(new Error(`Pic ID ${pictureId} not found for deletion.`));
};

export const getBbExperiments = async (pictureId, page = 1) => {
  console.log(`API: getBbExperiments (mocked) - PicID: ${pictureId}, Page: ${page}`);
  await new Promise(resolve => setTimeout(resolve, 200));
  if (!pictureId) return Promise.resolve({ experiments: [], totalPages: 0, currentPage: page, totalExperiments: 0 });
  const experimentsForPic = window.mockBbExperiments[pictureId] || [];
  const perPage = 2;
  const totalExperiments = experimentsForPic.length;
  const totalPages = Math.ceil(totalExperiments / perPage);
  const paginatedExperiments = experimentsForPic.slice((page - 1) * perPage, page * perPage);
  return Promise.resolve({ experiments: paginatedExperiments, totalPages, currentPage: page, totalExperiments });
};

export const updateBbResultScore = async (resultId, score) => {
  console.log(`API: updateBbResultScore (mocked) - ResID: ${resultId}, Score: ${score}`);
  await new Promise(resolve => setTimeout(resolve, 100));
  // Find and update the score in the mock store (optional for mock, but good for consistency)
  for (const picId in window.mockBbExperiments) {
    for (const exp of window.mockBbExperiments[picId]) {
      const result = exp.results.find(r => r.id === resultId);
      if (result) result.score = score;
    }
  }
  return Promise.resolve({ id: resultId, score: score, message: `Score updated.` });
};

export const getBbExperimentConfigurations = async () => {
  console.log('API: getBbExperimentConfigurations (mocked)');
  await new Promise(resolve => setTimeout(resolve, 100));
  return Promise.resolve([
    { prompt: 'A cat sitting on a red mat indoors.', resize_height: 512, resize_width: 512, output_type: 'Bounding Box' },
    { prompt: 'Multiple cats playing with toys.', resize_height: 640, resize_width: 640, output_type: 'Bounding Box + Segmentation Mask' },
  ]);
};

export const createBbExperiment = async (experimentData) => {
  console.log('API: createBbExperiment (mocked) with:', experimentData);
  await new Promise(resolve => setTimeout(resolve, 300));
  const newId = `exp_${Date.now()}_${experimentData.picture_id}`;
  const newExperiment = {
    id: newId, ...experimentData, created_at: new Date().toISOString(),
    results: [ // Mock some "processing" results based on available models
      { id: `res_${newId}_modelA`, model_id: 'modelA_id', model_name: 'VisionMaster Pro', status: 'processing', output_image_path: null, score: null, error_message: null },
      { id: `res_${newId}_modelB`, model_id: 'modelB_id', model_name: 'ObjectNet Advanced', status: 'processing', output_image_path: null, score: null, error_message: null },
    ]
  };
  if (!window.mockBbExperiments[experimentData.picture_id]) window.mockBbExperiments[experimentData.picture_id] = [];
  window.mockBbExperiments[experimentData.picture_id].unshift(newExperiment);
  return Promise.resolve(newExperiment);
};

export const getBbModels = async () => {
  console.log('API: getBbModels (mocked)');
  await new Promise(resolve => setTimeout(resolve, 100));
  return Promise.resolve([
    { id: 'modelA_id', name: 'VisionMaster Pro', description: 'High accuracy model.', is_active: true, gcp_model_name: 'gcp-visionmaster-pro' },
    { id: 'modelB_id', name: 'ObjectNet Advanced', description: 'Complex scenes specialist.', is_active: true, gcp_model_name: 'gcp-objectnet-adv' },
    { id: 'modelC_id', name: 'QuickDetect v3', description: 'Fast, real-time.', is_active: false, gcp_model_name: 'gcp-quickdetect-v3' },
    { id: 'modelD_id', name: 'UrbanScanner X', description: 'For urban environments.', is_active: true, gcp_model_name: 'gcp-urbanscanner-x' },
  ]);
};

export const updateBbModel = async (modelId, data) => {
  console.log(`API: updateBbModel (mocked) - ID: ${modelId}, Data:`, data);
  await new Promise(resolve => setTimeout(resolve, 100));
  // In a real scenario, you'd also update your global mock models list if using one for getBbModels
  return Promise.resolve({ id: modelId, ...data, message: `Model updated.` });
};

// New function for RankPage
const getAllBbExperimentsWithResults = async () => {
  console.log('API: getAllBbExperimentsWithResults (mocked)');
  await new Promise(resolve => setTimeout(resolve, 50));
  // This flattens all experiments from all pictures in the mock store
  const allExperimentsList = Object.values(window.mockBbExperiments || {}).flat();
  return Promise.resolve(allExperimentsList);
};

export const getBbRankData = async () => {
  console.log('API: getBbRankData (mocked)');
  await new Promise(resolve => setTimeout(resolve, 150));
  try {
    const models = await getBbModels();
    const experiments = await getAllBbExperimentsWithResults();
    return Promise.resolve({ models, experiments });
  } catch (error) {
    console.error("Error in getBbRankData (mocked):", error);
    return Promise.reject(error);
  }
};
