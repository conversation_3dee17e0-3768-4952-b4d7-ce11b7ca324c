import React, { useState, useEffect, useMemo } from 'react';
import {
  Container,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Paper,
  CircularProgress,
  Alert,
  Box
} from '@mui/material';
import { getBbRankData } from '../../services/api_bounding_box';

// Helper to format rank number
const RankIcon = ({ rank }) => (
  <Box sx={{
    width: 30, height: 30, borderRadius: '50%', backgroundColor: 'primary.main',
    color: 'white', display: 'flex', alignItems: 'center', justifyContent: 'center',
    fontWeight: 'bold'
  }}>
    {rank}
  </Box>
);

const RankPage = () => {
  const [rankedModels, setRankedModels] = useState([]);
  const [isLoading, setIsLoading] = useState(true); // Start with loading true
  const [error, setError] = useState(null);

  useEffect(() => {
    const calculateAndSetRankings = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const { models: allModels, experiments: allExperiments } = await getBbRankData();

        if (!allModels || !allExperiments) {
          setRankedModels([]);
          setError("Could not retrieve all necessary data for ranking.");
          return;
        }

        const modelScores = {}; // { modelId: { totalScore: X, count: Y, name: Z, id: ID } }

        allModels.forEach(model => {
          modelScores[model.id] = { totalScore: 0, count: 0, name: model.name, id: model.id };
        });

        allExperiments.forEach(experiment => {
          if (experiment.results && Array.isArray(experiment.results)) {
            experiment.results.forEach(result => {
              if (result.model_id && result.score !== null && result.score !== undefined && result.status === 'success') {
                if (modelScores[result.model_id]) {
                  modelScores[result.model_id].totalScore += result.score;
                  modelScores[result.model_id].count += 1;
                }
              }
            });
          }
        });

        const calculatedRankings = Object.values(modelScores)
          .filter(model => model.count > 0) // Only include models with at least one rating
          .map(model => ({
            ...model,
            averageScore: model.totalScore / model.count,
          }))
          .sort((a, b) => b.averageScore - a.averageScore); // Sort descending by average score

        setRankedModels(calculatedRankings);

      } catch (err) {
        setError('Failed to load or process ranking data.');
        console.error("Error in ranking calculation:", err);
        setRankedModels([]);
      } finally {
        setIsLoading(false);
      }
    };

    calculateAndSetRankings();
  }, []);


  if (isLoading) {
    return (
      <Container maxWidth="md" sx={{ mt: 4, mb: 4, textAlign: 'center' }}>
        <CircularProgress />
        <Typography sx={{ mt: 1 }}>Loading model ranking...</Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" gutterBottom component="h1">
        Model Ranking (Bounding Box)
      </Typography>

      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

      {!isLoading && !error && rankedModels.length === 0 && (
        <Alert severity="info">No models have been rated yet or no data available for ranking.</Alert>
      )}

      {rankedModels.length > 0 && (
        <Paper elevation={2}>
          <List>
            {rankedModels.map((model, index) => (
              <ListItem key={model.id} divider>
                <ListItemIcon>
                  <RankIcon rank={index + 1} />
                </ListItemIcon>
                <ListItemText
                  primary={model.name}
                  secondary={`Average Score: ${model.averageScore ? model.averageScore.toFixed(2) : 'N/A'}`}
                />
              </ListItem>
            ))}
          </List>
        </Paper>
      )}
    </Container>
  );
};

export default RankPage;
