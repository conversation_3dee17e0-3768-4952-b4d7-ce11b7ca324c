import React, { useState, useEffect, useCallback } from 'react';
import {
  Container,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Switch,
  CircularProgress,
  Alert,
  Box
} from '@mui/material';
import { getBbModels, updateBbModel } from '../../services/api_bounding_box';

const ModelPage = () => {
  const [models, setModels] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchModels = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await getBbModels();
      setModels(data || []); // Ensure models is an array
    } catch (err) {
      setError('Failed to load models.');
      console.error(err);
      setModels([]); // Clear models on error
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchModels();
  }, [fetchModels]);

  const handleToggleActive = async (modelId, isActive) => {
    // Optimistic UI update
    setModels(prevModels =>
      prevModels.map(model =>
        model.id === modelId ? { ...model, is_active: isActive } : model
      )
    );
    setError(null); // Clear previous errors

    try {
      await updateBbModel(modelId, { is_active: isActive });
      // Optionally: show a success snackbar or log
      console.log(`Model ${modelId} active status updated to ${isActive}`);
      // If API returns updated model, you could update state with it:
      // setModels(prevModels => prevModels.map(m => m.id === modelId ? updatedModelFromApi : m));
    } catch (err) {
      setError(`Failed to update model ${modelId}. Reverting change.`);
      console.error(err);
      // Revert optimistic update on error
      setModels(prevModels =>
        prevModels.map(model =>
          model.id === modelId ? { ...model, is_active: !isActive } : model
        )
      );
    }
  };

  if (isLoading) {
    return (
      <Container maxWidth="md" sx={{ mt: 4, mb: 4, textAlign: 'center' }}>
        <CircularProgress />
        <Typography sx={{ mt: 1 }}>Loading models...</Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" gutterBottom component="h1">
        Model Management (Bounding Box)
      </Typography>

      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

      <TableContainer component={Paper}>
        <Table aria-label="model management table">
          <TableHead>
            <TableRow>
              <TableCell>Model Name</TableCell>
              <TableCell>Description</TableCell>
              <TableCell align="center">GCP Model Name</TableCell>
              <TableCell align="center">Active</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {models.length === 0 && !isLoading && (
              <TableRow>
                <TableCell colSpan={4} align="center">
                  No models found.
                </TableCell>
              </TableRow>
            )}
            {models.map((model) => (
              <TableRow key={model.id}>
                <TableCell component="th" scope="row">
                  {model.name}
                </TableCell>
                <TableCell>{model.description}</TableCell>
                <TableCell align="center">{model.gcp_model_name || 'N/A'}</TableCell>
                <TableCell align="center">
                  <Switch
                    checked={model.is_active}
                    onChange={(e) => handleToggleActive(model.id, e.target.checked)}
                    inputProps={{ 'aria-label': `toggle ${model.name} active status` }}
                  />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Container>
  );
};

export default ModelPage;
