{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents\\\\Programing\\\\TRO\\\\ModelTestsWorkbench\\\\frontend\\\\src\\\\components\\\\model-test-workbench\\\\ImageUpload.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Button, Select, MenuItem, FormControl, InputLabel, Typography, Alert, CircularProgress, Input,\n// Use Input for the file input styling\nList, ListItem, ListItemText, Paper // For displaying errors\n} from '@mui/material';\nimport { uploadImages } from '../../services/api_model_workbench'; // Corrected import path\n\n// Define separate constants\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst IP_CATEGORIES = ['trademark', 'copyright', 'patent'];\nconst IMAGE_TYPES = ['IP', 'Product'];\nconst ImageUpload = () => {\n  _s();\n  const [selectedFiles, setSelectedFiles] = useState([]);\n  const [selectedIpCategory, setSelectedIpCategory] = useState(''); // New state for IP Category\n  const [imageType, setImageType] = useState(''); // Renamed state for Image Type\n  const [isLoading, setIsLoading] = useState(false);\n  const [feedback, setFeedback] = useState({\n    type: '',\n    message: '',\n    errors: []\n  }); // 'success' or 'error', and detailed errors\n\n  const handleFileChange = event => {\n    console.log(\"File change event:\", event.target.files); // Add logging\n    setSelectedFiles(Array.from(event.target.files));\n    setFeedback({\n      type: '',\n      message: '',\n      errors: []\n    }); // Clear feedback on new selection\n  };\n\n  // Handler for the new IP Category dropdown\n  const handleIpCategoryChange = event => {\n    setSelectedIpCategory(event.target.value);\n    setFeedback({\n      type: '',\n      message: '',\n      errors: []\n    }); // Clear feedback\n  };\n\n  // Renamed handler for Image Type dropdown\n  const handleImageTypeChange = event => {\n    setImageType(event.target.value);\n    setFeedback({\n      type: '',\n      message: '',\n      errors: []\n    }); // Clear feedback\n  };\n  const handleSubmit = async event => {\n    event.preventDefault();\n    // Updated validation to check both new fields\n    if (selectedFiles.length === 0 || !selectedIpCategory || !imageType) {\n      setFeedback({\n        type: 'error',\n        message: 'Please select files, an IP category, and an image type.',\n        errors: []\n      });\n      return;\n    }\n    setIsLoading(true);\n    setFeedback({\n      type: '',\n      message: '',\n      errors: []\n    });\n    const formData = new FormData();\n    selectedFiles.forEach(file => {\n      formData.append('files[]', file); // Ensure key matches backend expectation 'files[]'\n    });\n\n    // Use state variables directly\n    formData.append('ip_category', selectedIpCategory); // Send selected IP category\n    formData.append('image_type', imageType.toLowerCase()); // Send selected image type\n\n    try {\n      var _response$data, _response$data2, _response$data3;\n      const response = await uploadImages(formData);\n      const successes = ((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.success) || [];\n      const errors = ((_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.errors) || [];\n      const successCount = successes.length;\n      let message = '';\n      let messageType = 'info'; // Default to info for mixed results\n\n      if (successCount > 0 && errors.length === 0) {\n        message = `${successCount} image(s) uploaded successfully as ${selectedIpCategory} / ${imageType}.`;\n        messageType = 'success';\n        setSelectedFiles([]); // Clear selection on full success\n        setSelectedIpCategory(''); // Clear IP category on full success\n        setImageType(''); // Clear image type on full success\n        document.getElementById('image-upload-input').value = null; // Clear file input\n      } else if (successCount > 0 && errors.length > 0) {\n        message = `${successCount} image(s) uploaded successfully. ${errors.length} image(s) failed. See details below.`;\n        messageType = 'warning';\n        // Partially clear or keep files? For now, keep them to allow re-attempt or correction.\n      } else if (successCount === 0 && errors.length > 0) {\n        message = `All ${errors.length} image(s) failed to upload. See details below.`;\n        messageType = 'error';\n      } else if ((_response$data3 = response.data) !== null && _response$data3 !== void 0 && _response$data3.message) {\n        // General success message from backend\n        message = response.data.message;\n        messageType = 'success';\n        setSelectedFiles([]);\n        setSelectedIpCategory('');\n        setImageType('');\n        document.getElementById('image-upload-input').value = null;\n      } else {\n        // Fallback for unexpected response\n        message = 'Upload process completed. Check server logs for details if anything is amiss.';\n        messageType = 'info';\n      }\n      setFeedback({\n        type: messageType,\n        message: message,\n        errors: errors\n      });\n    } catch (error) {\n      var _error$response, _error$response$data, _error$response2, _error$response2$data, _error$response3, _error$response3$data;\n      console.error(\"Image upload failed:\", error);\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.error) || error.message || 'Image upload failed. Check console for details.';\n      const responseErrors = ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.errors) || []; // From interceptor or direct\n      setFeedback({\n        type: 'error',\n        message: errorMessage,\n        errors: responseErrors\n      });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    component: \"form\",\n    onSubmit: handleSubmit,\n    sx: {\n      mt: 2,\n      p: 2,\n      border: '1px dashed grey',\n      borderRadius: 1\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      gutterBottom: true,\n      children: \"Upload Images\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this), feedback.message && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: feedback.type,\n      sx: {\n        mb: 2\n      },\n      children: feedback.message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 9\n    }, this), feedback.errors && feedback.errors.length > 0 && /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 2,\n      sx: {\n        p: 2,\n        mb: 2,\n        maxHeight: 200,\n        overflow: 'auto',\n        backgroundColor: theme => theme.palette.mode === 'dark' ? '#333' : '#fce4e4'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle2\",\n        color: \"error\",\n        children: \"Upload Errors:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(List, {\n        dense: true,\n        children: feedback.errors.map((err, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n          children: /*#__PURE__*/_jsxDEV(ListItemText, {\n            primary: err.filename || `File ${index + 1}`,\n            secondary: err.error || 'Unknown error'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 17\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n      fullWidth: true,\n      margin: \"normal\",\n      required: true,\n      children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n        id: \"ip-category-select-label\",\n        children: \"IP Category\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Select, {\n        labelId: \"ip-category-select-label\",\n        id: \"ip-category-select\",\n        value: selectedIpCategory,\n        label: \"IP Category\",\n        onChange: handleIpCategoryChange,\n        disabled: isLoading,\n        children: IP_CATEGORIES.map(cat => /*#__PURE__*/_jsxDEV(MenuItem, {\n          value: cat,\n          sx: {\n            textTransform: 'capitalize'\n          },\n          children: cat\n        }, cat, false, {\n          fileName: _jsxFileName,\n          lineNumber: 156,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n      fullWidth: true,\n      margin: \"normal\",\n      required: true,\n      children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n        id: \"image-type-select-label\",\n        children: \"Image Type\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Select, {\n        labelId: \"image-type-select-label\",\n        id: \"image-type-select\",\n        value: imageType,\n        label: \"Image Type\",\n        onChange: handleImageTypeChange,\n        disabled: isLoading,\n        children: IMAGE_TYPES.map(type => /*#__PURE__*/_jsxDEV(MenuItem, {\n          value: type,\n          children: type\n        }, type, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n      fullWidth: true,\n      margin: \"normal\",\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        component: \"label\" // Makes the button act like a label for the input\n        ,\n        disabled: isLoading,\n        children: [\"Select Images\", /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"file\",\n          id: \"image-upload-input\" // Added ID for clearing\n          ,\n          multiple: true // Directly apply the multiple attribute\n          ,\n          hidden: true // Hide the default input appearance\n          ,\n          onChange: handleFileChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 10\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 7\n    }, this), selectedFiles.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        my: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle1\",\n        children: \"Selected Files:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(List, {\n        dense: true,\n        children: selectedFiles.map((file, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n          children: /*#__PURE__*/_jsxDEV(ListItemText, {\n            primary: file.name,\n            secondary: `${(file.size / 1024).toFixed(2)} KB`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 17\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      type: \"submit\",\n      variant: \"contained\",\n      color: \"primary\",\n      disabled: isLoading || selectedFiles.length === 0 || !selectedIpCategory || !imageType,\n      sx: {\n        mt: 2\n      },\n      children: isLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 24\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 22\n      }, this) : 'Upload'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 119,\n    columnNumber: 5\n  }, this);\n};\n_s(ImageUpload, \"KJ3Hi70kWsfobgHBG4BDE1Yz7EA=\");\n_c = ImageUpload;\nexport default ImageUpload;\nvar _c;\n$RefreshReg$(_c, \"ImageUpload\");", "map": {"version": 3, "names": ["React", "useState", "Box", "<PERSON><PERSON>", "Select", "MenuItem", "FormControl", "InputLabel", "Typography", "<PERSON><PERSON>", "CircularProgress", "Input", "List", "ListItem", "ListItemText", "Paper", "uploadImages", "jsxDEV", "_jsxDEV", "IP_CATEGORIES", "IMAGE_TYPES", "ImageUpload", "_s", "selectedFiles", "setSelectedFiles", "selectedIpCategory", "setSelectedIpCategory", "imageType", "setImageType", "isLoading", "setIsLoading", "feedback", "setFeedback", "type", "message", "errors", "handleFileChange", "event", "console", "log", "target", "files", "Array", "from", "handleIpCategoryChange", "value", "handleImageTypeChange", "handleSubmit", "preventDefault", "length", "formData", "FormData", "for<PERSON>ach", "file", "append", "toLowerCase", "_response$data", "_response$data2", "_response$data3", "response", "successes", "data", "success", "successCount", "messageType", "document", "getElementById", "error", "_error$response", "_error$response$data", "_error$response2", "_error$response2$data", "_error$response3", "_error$response3$data", "errorMessage", "detail", "responseErrors", "component", "onSubmit", "sx", "mt", "p", "border", "borderRadius", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "mb", "elevation", "maxHeight", "overflow", "backgroundColor", "theme", "palette", "mode", "color", "dense", "map", "err", "index", "primary", "filename", "secondary", "fullWidth", "margin", "required", "id", "labelId", "label", "onChange", "disabled", "cat", "textTransform", "multiple", "hidden", "my", "name", "size", "toFixed", "_c", "$RefreshReg$"], "sources": ["D:/Documents/Programing/TRO/ModelTestsWorkbench/frontend/src/components/model-test-workbench/ImageUpload.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport {\r\n  Box,\r\n  Button,\r\n  Select,\r\n  MenuItem,\r\n  FormControl,\r\n  InputLabel,\r\n  Typography,\r\n  Alert,\r\n  CircularProgress,\r\n  Input, // Use Input for the file input styling\r\n  List,\r\n  ListItem,\r\n  ListItemText,\r\n  Paper // For displaying errors\r\n} from '@mui/material';\r\nimport { uploadImages } from '../../services/api_model_workbench'; // Corrected import path\r\n\r\n// Define separate constants\r\nconst IP_CATEGORIES = ['trademark', 'copyright', 'patent'];\r\nconst IMAGE_TYPES = ['IP', 'Product'];\r\n\r\n\r\nconst ImageUpload = () => {\r\n  const [selectedFiles, setSelectedFiles] = useState([]);\r\n  const [selectedIpCategory, setSelectedIpCategory] = useState(''); // New state for IP Category\r\n  const [imageType, setImageType] = useState(''); // Renamed state for Image Type\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [feedback, setFeedback] = useState({ type: '', message: '', errors: [] }); // 'success' or 'error', and detailed errors\r\n\r\n  const handleFileChange = (event) => {\r\n    console.log(\"File change event:\", event.target.files); // Add logging\r\n    setSelectedFiles(Array.from(event.target.files));\r\n    setFeedback({ type: '', message: '', errors: [] }); // Clear feedback on new selection\r\n  };\r\n\r\n  // Handler for the new IP Category dropdown\r\n  const handleIpCategoryChange = (event) => {\r\n    setSelectedIpCategory(event.target.value);\r\n    setFeedback({ type: '', message: '', errors: [] }); // Clear feedback\r\n  };\r\n\r\n  // Renamed handler for Image Type dropdown\r\n  const handleImageTypeChange = (event) => {\r\n    setImageType(event.target.value);\r\n    setFeedback({ type: '', message: '', errors: [] }); // Clear feedback\r\n  };\r\n\r\n\r\n  const handleSubmit = async (event) => {\r\n    event.preventDefault();\r\n    // Updated validation to check both new fields\r\n    if (selectedFiles.length === 0 || !selectedIpCategory || !imageType) {\r\n      setFeedback({ type: 'error', message: 'Please select files, an IP category, and an image type.', errors: [] });\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n    setFeedback({ type: '', message: '', errors: [] });\r\n\r\n    const formData = new FormData();\r\n    selectedFiles.forEach(file => {\r\n      formData.append('files[]', file); // Ensure key matches backend expectation 'files[]'\r\n    });\r\n\r\n    // Use state variables directly\r\n    formData.append('ip_category', selectedIpCategory); // Send selected IP category\r\n    formData.append('image_type', imageType.toLowerCase()); // Send selected image type\r\n\r\n    try {\r\n      const response = await uploadImages(formData);\r\n      const successes = response.data?.success || [];\r\n      const errors = response.data?.errors || [];\r\n      const successCount = successes.length;\r\n\r\n      let message = '';\r\n      let messageType = 'info'; // Default to info for mixed results\r\n\r\n      if (successCount > 0 && errors.length === 0) {\r\n        message = `${successCount} image(s) uploaded successfully as ${selectedIpCategory} / ${imageType}.`;\r\n        messageType = 'success';\r\n        setSelectedFiles([]); // Clear selection on full success\r\n        setSelectedIpCategory(''); // Clear IP category on full success\r\n        setImageType(''); // Clear image type on full success\r\n        document.getElementById('image-upload-input').value = null; // Clear file input\r\n      } else if (successCount > 0 && errors.length > 0) {\r\n        message = `${successCount} image(s) uploaded successfully. ${errors.length} image(s) failed. See details below.`;\r\n        messageType = 'warning';\r\n         // Partially clear or keep files? For now, keep them to allow re-attempt or correction.\r\n      } else if (successCount === 0 && errors.length > 0) {\r\n        message = `All ${errors.length} image(s) failed to upload. See details below.`;\r\n        messageType = 'error';\r\n      } else if (response.data?.message) { // General success message from backend\r\n        message = response.data.message;\r\n        messageType = 'success';\r\n        setSelectedFiles([]);\r\n        setSelectedIpCategory('');\r\n        setImageType('');\r\n        document.getElementById('image-upload-input').value = null;\r\n      } else { // Fallback for unexpected response\r\n        message = 'Upload process completed. Check server logs for details if anything is amiss.';\r\n        messageType = 'info';\r\n      }\r\n\r\n      setFeedback({ type: messageType, message: message, errors: errors });\r\n\r\n    } catch (error) {\r\n      console.error(\"Image upload failed:\", error);\r\n      const errorMessage = error.response?.data?.detail || error.response?.data?.error || error.message || 'Image upload failed. Check console for details.';\r\n      const responseErrors = error.response?.data?.errors || []; // From interceptor or direct\r\n      setFeedback({ type: 'error', message: errorMessage, errors: responseErrors });\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Box component=\"form\" onSubmit={handleSubmit} sx={{ mt: 2, p: 2, border: '1px dashed grey', borderRadius: 1 }}>\r\n      <Typography variant=\"h6\" gutterBottom>Upload Images</Typography>\r\n\r\n      {feedback.message && (\r\n        <Alert severity={feedback.type} sx={{ mb: 2 }}>\r\n          {feedback.message}\r\n        </Alert>\r\n      )}\r\n\r\n      {feedback.errors && feedback.errors.length > 0 && (\r\n        <Paper elevation={2} sx={{ p: 2, mb: 2, maxHeight: 200, overflow: 'auto', backgroundColor: (theme) => theme.palette.mode === 'dark' ? '#333' : '#fce4e4' }}>\r\n          <Typography variant=\"subtitle2\" color=\"error\">Upload Errors:</Typography>\r\n          <List dense>\r\n            {feedback.errors.map((err, index) => (\r\n              <ListItem key={index}>\r\n                <ListItemText\r\n                  primary={err.filename || `File ${index + 1}`}\r\n                  secondary={err.error || 'Unknown error'}\r\n                />\r\n              </ListItem>\r\n            ))}\r\n          </List>\r\n        </Paper>\r\n      )}\r\n\r\n      {/* New IP Category Dropdown */}\r\n      <FormControl fullWidth margin=\"normal\" required>\r\n        <InputLabel id=\"ip-category-select-label\">IP Category</InputLabel>\r\n        <Select\r\n          labelId=\"ip-category-select-label\"\r\n          id=\"ip-category-select\"\r\n          value={selectedIpCategory}\r\n          label=\"IP Category\"\r\n          onChange={handleIpCategoryChange}\r\n          disabled={isLoading}\r\n        >\r\n          {IP_CATEGORIES.map((cat) => (\r\n            <MenuItem key={cat} value={cat} sx={{ textTransform: 'capitalize' }}>\r\n              {cat}\r\n            </MenuItem>\r\n          ))}\r\n        </Select>\r\n      </FormControl>\r\n\r\n      {/* Modified Image Type Dropdown */}\r\n      <FormControl fullWidth margin=\"normal\" required>\r\n        <InputLabel id=\"image-type-select-label\">Image Type</InputLabel>\r\n        <Select\r\n          labelId=\"image-type-select-label\"\r\n          id=\"image-type-select\"\r\n          value={imageType}\r\n          label=\"Image Type\"\r\n          onChange={handleImageTypeChange}\r\n          disabled={isLoading}\r\n        >\r\n          {IMAGE_TYPES.map((type) => (\r\n            <MenuItem key={type} value={type}>{type}</MenuItem>\r\n          ))}\r\n        </Select>\r\n      </FormControl>\r\n\r\n      <FormControl fullWidth margin=\"normal\">\r\n         {/* Use a styled button to trigger the hidden file input */}\r\n         <Button\r\n            variant=\"contained\"\r\n            component=\"label\" // Makes the button act like a label for the input\r\n            disabled={isLoading}\r\n          >\r\n            Select Images\r\n            {/* Use a standard hidden input for reliable multiple file selection */}\r\n            <input\r\n              type=\"file\"\r\n              id=\"image-upload-input\" // Added ID for clearing\r\n              multiple // Directly apply the multiple attribute\r\n              hidden // Hide the default input appearance\r\n              onChange={handleFileChange}\r\n            />\r\n          </Button>\r\n      </FormControl>\r\n\r\n      {selectedFiles.length > 0 && (\r\n        <Box sx={{ my: 2 }}>\r\n          <Typography variant=\"subtitle1\">Selected Files:</Typography>\r\n          <List dense>\r\n            {selectedFiles.map((file, index) => (\r\n              <ListItem key={index}>\r\n                <ListItemText primary={file.name} secondary={`${(file.size / 1024).toFixed(2)} KB`} />\r\n              </ListItem>\r\n            ))}\r\n          </List>\r\n        </Box>\r\n      )}\r\n\r\n\r\n      <Button\r\n        type=\"submit\"\r\n        variant=\"contained\"\r\n        color=\"primary\"\r\n        disabled={isLoading || selectedFiles.length === 0 || !selectedIpCategory || !imageType}\r\n        sx={{ mt: 2 }}\r\n      >\r\n        {isLoading ? <CircularProgress size={24} /> : 'Upload'}\r\n      </Button>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default ImageUpload;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,GAAG,EACHC,MAAM,EACNC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,UAAU,EACVC,UAAU,EACVC,KAAK,EACLC,gBAAgB,EAChBC,KAAK;AAAE;AACPC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,KAAK,CAAC;AAAA,OACD,eAAe;AACtB,SAASC,YAAY,QAAQ,oCAAoC,CAAC,CAAC;;AAEnE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,aAAa,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,QAAQ,CAAC;AAC1D,MAAMC,WAAW,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC;AAGrC,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACwB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAClE,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAChD,MAAM,CAAC4B,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC8B,QAAQ,EAAEC,WAAW,CAAC,GAAG/B,QAAQ,CAAC;IAAEgC,IAAI,EAAE,EAAE;IAAEC,OAAO,EAAE,EAAE;IAAEC,MAAM,EAAE;EAAG,CAAC,CAAC,CAAC,CAAC;;EAEjF,MAAMC,gBAAgB,GAAIC,KAAK,IAAK;IAClCC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,KAAK,CAACG,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IACvDjB,gBAAgB,CAACkB,KAAK,CAACC,IAAI,CAACN,KAAK,CAACG,MAAM,CAACC,KAAK,CAAC,CAAC;IAChDT,WAAW,CAAC;MAAEC,IAAI,EAAE,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAEC,MAAM,EAAE;IAAG,CAAC,CAAC,CAAC,CAAC;EACtD,CAAC;;EAED;EACA,MAAMS,sBAAsB,GAAIP,KAAK,IAAK;IACxCX,qBAAqB,CAACW,KAAK,CAACG,MAAM,CAACK,KAAK,CAAC;IACzCb,WAAW,CAAC;MAAEC,IAAI,EAAE,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAEC,MAAM,EAAE;IAAG,CAAC,CAAC,CAAC,CAAC;EACtD,CAAC;;EAED;EACA,MAAMW,qBAAqB,GAAIT,KAAK,IAAK;IACvCT,YAAY,CAACS,KAAK,CAACG,MAAM,CAACK,KAAK,CAAC;IAChCb,WAAW,CAAC;MAAEC,IAAI,EAAE,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAEC,MAAM,EAAE;IAAG,CAAC,CAAC,CAAC,CAAC;EACtD,CAAC;EAGD,MAAMY,YAAY,GAAG,MAAOV,KAAK,IAAK;IACpCA,KAAK,CAACW,cAAc,CAAC,CAAC;IACtB;IACA,IAAIzB,aAAa,CAAC0B,MAAM,KAAK,CAAC,IAAI,CAACxB,kBAAkB,IAAI,CAACE,SAAS,EAAE;MACnEK,WAAW,CAAC;QAAEC,IAAI,EAAE,OAAO;QAAEC,OAAO,EAAE,yDAAyD;QAAEC,MAAM,EAAE;MAAG,CAAC,CAAC;MAC9G;IACF;IAEAL,YAAY,CAAC,IAAI,CAAC;IAClBE,WAAW,CAAC;MAAEC,IAAI,EAAE,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAEC,MAAM,EAAE;IAAG,CAAC,CAAC;IAElD,MAAMe,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/B5B,aAAa,CAAC6B,OAAO,CAACC,IAAI,IAAI;MAC5BH,QAAQ,CAACI,MAAM,CAAC,SAAS,EAAED,IAAI,CAAC,CAAC,CAAC;IACpC,CAAC,CAAC;;IAEF;IACAH,QAAQ,CAACI,MAAM,CAAC,aAAa,EAAE7B,kBAAkB,CAAC,CAAC,CAAC;IACpDyB,QAAQ,CAACI,MAAM,CAAC,YAAY,EAAE3B,SAAS,CAAC4B,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;;IAExD,IAAI;MAAA,IAAAC,cAAA,EAAAC,eAAA,EAAAC,eAAA;MACF,MAAMC,QAAQ,GAAG,MAAM3C,YAAY,CAACkC,QAAQ,CAAC;MAC7C,MAAMU,SAAS,GAAG,EAAAJ,cAAA,GAAAG,QAAQ,CAACE,IAAI,cAAAL,cAAA,uBAAbA,cAAA,CAAeM,OAAO,KAAI,EAAE;MAC9C,MAAM3B,MAAM,GAAG,EAAAsB,eAAA,GAAAE,QAAQ,CAACE,IAAI,cAAAJ,eAAA,uBAAbA,eAAA,CAAetB,MAAM,KAAI,EAAE;MAC1C,MAAM4B,YAAY,GAAGH,SAAS,CAACX,MAAM;MAErC,IAAIf,OAAO,GAAG,EAAE;MAChB,IAAI8B,WAAW,GAAG,MAAM,CAAC,CAAC;;MAE1B,IAAID,YAAY,GAAG,CAAC,IAAI5B,MAAM,CAACc,MAAM,KAAK,CAAC,EAAE;QAC3Cf,OAAO,GAAG,GAAG6B,YAAY,sCAAsCtC,kBAAkB,MAAME,SAAS,GAAG;QACnGqC,WAAW,GAAG,SAAS;QACvBxC,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC;QACtBE,qBAAqB,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3BE,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;QAClBqC,QAAQ,CAACC,cAAc,CAAC,oBAAoB,CAAC,CAACrB,KAAK,GAAG,IAAI,CAAC,CAAC;MAC9D,CAAC,MAAM,IAAIkB,YAAY,GAAG,CAAC,IAAI5B,MAAM,CAACc,MAAM,GAAG,CAAC,EAAE;QAChDf,OAAO,GAAG,GAAG6B,YAAY,oCAAoC5B,MAAM,CAACc,MAAM,sCAAsC;QAChHe,WAAW,GAAG,SAAS;QACtB;MACH,CAAC,MAAM,IAAID,YAAY,KAAK,CAAC,IAAI5B,MAAM,CAACc,MAAM,GAAG,CAAC,EAAE;QAClDf,OAAO,GAAG,OAAOC,MAAM,CAACc,MAAM,gDAAgD;QAC9Ee,WAAW,GAAG,OAAO;MACvB,CAAC,MAAM,KAAAN,eAAA,GAAIC,QAAQ,CAACE,IAAI,cAAAH,eAAA,eAAbA,eAAA,CAAexB,OAAO,EAAE;QAAE;QACnCA,OAAO,GAAGyB,QAAQ,CAACE,IAAI,CAAC3B,OAAO;QAC/B8B,WAAW,GAAG,SAAS;QACvBxC,gBAAgB,CAAC,EAAE,CAAC;QACpBE,qBAAqB,CAAC,EAAE,CAAC;QACzBE,YAAY,CAAC,EAAE,CAAC;QAChBqC,QAAQ,CAACC,cAAc,CAAC,oBAAoB,CAAC,CAACrB,KAAK,GAAG,IAAI;MAC5D,CAAC,MAAM;QAAE;QACPX,OAAO,GAAG,+EAA+E;QACzF8B,WAAW,GAAG,MAAM;MACtB;MAEAhC,WAAW,CAAC;QAAEC,IAAI,EAAE+B,WAAW;QAAE9B,OAAO,EAAEA,OAAO;QAAEC,MAAM,EAAEA;MAAO,CAAC,CAAC;IAEtE,CAAC,CAAC,OAAOgC,KAAK,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACdnC,OAAO,CAAC6B,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMO,YAAY,GAAG,EAAAN,eAAA,GAAAD,KAAK,CAACR,QAAQ,cAAAS,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBP,IAAI,cAAAQ,oBAAA,uBAApBA,oBAAA,CAAsBM,MAAM,OAAAL,gBAAA,GAAIH,KAAK,CAACR,QAAQ,cAAAW,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBT,IAAI,cAAAU,qBAAA,uBAApBA,qBAAA,CAAsBJ,KAAK,KAAIA,KAAK,CAACjC,OAAO,IAAI,iDAAiD;MACtJ,MAAM0C,cAAc,GAAG,EAAAJ,gBAAA,GAAAL,KAAK,CAACR,QAAQ,cAAAa,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBX,IAAI,cAAAY,qBAAA,uBAApBA,qBAAA,CAAsBtC,MAAM,KAAI,EAAE,CAAC,CAAC;MAC3DH,WAAW,CAAC;QAAEC,IAAI,EAAE,OAAO;QAAEC,OAAO,EAAEwC,YAAY;QAAEvC,MAAM,EAAEyC;MAAe,CAAC,CAAC;IAC/E,CAAC,SAAS;MACR9C,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,oBACEZ,OAAA,CAAChB,GAAG;IAAC2E,SAAS,EAAC,MAAM;IAACC,QAAQ,EAAE/B,YAAa;IAACgC,EAAE,EAAE;MAAEC,EAAE,EAAE,CAAC;MAAEC,CAAC,EAAE,CAAC;MAAEC,MAAM,EAAE,iBAAiB;MAAEC,YAAY,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAC5GlE,OAAA,CAACV,UAAU;MAAC6E,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAAa;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,EAE/D3D,QAAQ,CAACG,OAAO,iBACfhB,OAAA,CAACT,KAAK;MAACkF,QAAQ,EAAE5D,QAAQ,CAACE,IAAK;MAAC8C,EAAE,EAAE;QAAEa,EAAE,EAAE;MAAE,CAAE;MAAAR,QAAA,EAC3CrD,QAAQ,CAACG;IAAO;MAAAqD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CACR,EAEA3D,QAAQ,CAACI,MAAM,IAAIJ,QAAQ,CAACI,MAAM,CAACc,MAAM,GAAG,CAAC,iBAC5C/B,OAAA,CAACH,KAAK;MAAC8E,SAAS,EAAE,CAAE;MAACd,EAAE,EAAE;QAAEE,CAAC,EAAE,CAAC;QAAEW,EAAE,EAAE,CAAC;QAAEE,SAAS,EAAE,GAAG;QAAEC,QAAQ,EAAE,MAAM;QAAEC,eAAe,EAAGC,KAAK,IAAKA,KAAK,CAACC,OAAO,CAACC,IAAI,KAAK,MAAM,GAAG,MAAM,GAAG;MAAU,CAAE;MAAAf,QAAA,gBACzJlE,OAAA,CAACV,UAAU;QAAC6E,OAAO,EAAC,WAAW;QAACe,KAAK,EAAC,OAAO;QAAAhB,QAAA,EAAC;MAAc;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACzExE,OAAA,CAACN,IAAI;QAACyF,KAAK;QAAAjB,QAAA,EACRrD,QAAQ,CAACI,MAAM,CAACmE,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBAC9BtF,OAAA,CAACL,QAAQ;UAAAuE,QAAA,eACPlE,OAAA,CAACJ,YAAY;YACX2F,OAAO,EAAEF,GAAG,CAACG,QAAQ,IAAI,QAAQF,KAAK,GAAG,CAAC,EAAG;YAC7CG,SAAS,EAAEJ,GAAG,CAACpC,KAAK,IAAI;UAAgB;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC;QAAC,GAJWc,KAAK;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKV,CACX;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACR,eAGDxE,OAAA,CAACZ,WAAW;MAACsG,SAAS;MAACC,MAAM,EAAC,QAAQ;MAACC,QAAQ;MAAA1B,QAAA,gBAC7ClE,OAAA,CAACX,UAAU;QAACwG,EAAE,EAAC,0BAA0B;QAAA3B,QAAA,EAAC;MAAW;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAClExE,OAAA,CAACd,MAAM;QACL4G,OAAO,EAAC,0BAA0B;QAClCD,EAAE,EAAC,oBAAoB;QACvBlE,KAAK,EAAEpB,kBAAmB;QAC1BwF,KAAK,EAAC,aAAa;QACnBC,QAAQ,EAAEtE,sBAAuB;QACjCuE,QAAQ,EAAEtF,SAAU;QAAAuD,QAAA,EAEnBjE,aAAa,CAACmF,GAAG,CAAEc,GAAG,iBACrBlG,OAAA,CAACb,QAAQ;UAAWwC,KAAK,EAAEuE,GAAI;UAACrC,EAAE,EAAE;YAAEsC,aAAa,EAAE;UAAa,CAAE;UAAAjC,QAAA,EACjEgC;QAAG,GADSA,GAAG;UAAA7B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAER,CACX;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGdxE,OAAA,CAACZ,WAAW;MAACsG,SAAS;MAACC,MAAM,EAAC,QAAQ;MAACC,QAAQ;MAAA1B,QAAA,gBAC7ClE,OAAA,CAACX,UAAU;QAACwG,EAAE,EAAC,yBAAyB;QAAA3B,QAAA,EAAC;MAAU;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAChExE,OAAA,CAACd,MAAM;QACL4G,OAAO,EAAC,yBAAyB;QACjCD,EAAE,EAAC,mBAAmB;QACtBlE,KAAK,EAAElB,SAAU;QACjBsF,KAAK,EAAC,YAAY;QAClBC,QAAQ,EAAEpE,qBAAsB;QAChCqE,QAAQ,EAAEtF,SAAU;QAAAuD,QAAA,EAEnBhE,WAAW,CAACkF,GAAG,CAAErE,IAAI,iBACpBf,OAAA,CAACb,QAAQ;UAAYwC,KAAK,EAAEZ,IAAK;UAAAmD,QAAA,EAAEnD;QAAI,GAAxBA,IAAI;UAAAsD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAA+B,CACnD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEdxE,OAAA,CAACZ,WAAW;MAACsG,SAAS;MAACC,MAAM,EAAC,QAAQ;MAAAzB,QAAA,eAEnClE,OAAA,CAACf,MAAM;QACJkF,OAAO,EAAC,WAAW;QACnBR,SAAS,EAAC,OAAO,CAAC;QAAA;QAClBsC,QAAQ,EAAEtF,SAAU;QAAAuD,QAAA,GACrB,eAEC,eACAlE,OAAA;UACEe,IAAI,EAAC,MAAM;UACX8E,EAAE,EAAC,oBAAoB,CAAC;UAAA;UACxBO,QAAQ,OAAC;UAAA;UACTC,MAAM,OAAC;UAAA;UACPL,QAAQ,EAAE9E;QAAiB;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAEbnE,aAAa,CAAC0B,MAAM,GAAG,CAAC,iBACvB/B,OAAA,CAAChB,GAAG;MAAC6E,EAAE,EAAE;QAAEyC,EAAE,EAAE;MAAE,CAAE;MAAApC,QAAA,gBACjBlE,OAAA,CAACV,UAAU;QAAC6E,OAAO,EAAC,WAAW;QAAAD,QAAA,EAAC;MAAe;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAC5DxE,OAAA,CAACN,IAAI;QAACyF,KAAK;QAAAjB,QAAA,EACR7D,aAAa,CAAC+E,GAAG,CAAC,CAACjD,IAAI,EAAEmD,KAAK,kBAC7BtF,OAAA,CAACL,QAAQ;UAAAuE,QAAA,eACPlE,OAAA,CAACJ,YAAY;YAAC2F,OAAO,EAAEpD,IAAI,CAACoE,IAAK;YAACd,SAAS,EAAE,GAAG,CAACtD,IAAI,CAACqE,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC;UAAM;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC,GADzEc,KAAK;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CACX;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACN,eAGDxE,OAAA,CAACf,MAAM;MACL8B,IAAI,EAAC,QAAQ;MACboD,OAAO,EAAC,WAAW;MACnBe,KAAK,EAAC,SAAS;MACfe,QAAQ,EAAEtF,SAAS,IAAIN,aAAa,CAAC0B,MAAM,KAAK,CAAC,IAAI,CAACxB,kBAAkB,IAAI,CAACE,SAAU;MACvFoD,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAI,QAAA,EAEbvD,SAAS,gBAAGX,OAAA,CAACR,gBAAgB;QAACgH,IAAI,EAAE;MAAG;QAAAnC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,GAAG;IAAQ;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACpE,EAAA,CAvMID,WAAW;AAAAuG,EAAA,GAAXvG,WAAW;AAyMjB,eAAeA,WAAW;AAAC,IAAAuG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}