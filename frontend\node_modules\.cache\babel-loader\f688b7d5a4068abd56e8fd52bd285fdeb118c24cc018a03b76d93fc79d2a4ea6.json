{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents\\\\Programing\\\\TRO\\\\ModelTestsWorkbench\\\\frontend\\\\src\\\\components\\\\model-test-workbench\\\\FeatureComputation.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef, useCallback } from 'react';\nimport { Box, Typography, Button, CircularProgress, Alert, Paper, Grid, Chip,\n// To display status\nLinearProgress // For progress indication\n} from '@mui/material';\nimport PlayArrowIcon from '@mui/icons-material/PlayArrow';\nimport { computeFeatures, getTaskStatus } from '../../services/api_model_workbench';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst IP_CATEGORIES = [\"trademark\", \"copyright\", \"patent\"]; // Use lowercase as likely expected by API path\n\nconst FeatureComputation = () => {\n  _s();\n  // State to hold task info: { taskId: string | null, status: string, error: string, progress: number | null }\n  const [tasks, setTasks] = useState(IP_CATEGORIES.reduce((acc, category) => {\n    acc[category] = {\n      taskId: null,\n      status: 'Idle',\n      error: '',\n      progress: null\n    };\n    return acc;\n  }, {}));\n  const [loading, setLoading] = useState(IP_CATEGORIES.reduce((acc, category) => {\n    acc[category] = false; // Loading state for triggering the task\n    return acc;\n  }, {}));\n  const intervalRefs = useRef({}); // Store interval IDs for polling\n\n  // Function to update task state\n  const updateTaskState = (category, newState) => {\n    setTasks(prevTasks => ({\n      ...prevTasks,\n      [category]: {\n        ...prevTasks[category],\n        ...newState\n      }\n    }));\n  };\n\n  // Function to start polling for a task\n  const startPolling = useCallback((category, taskId) => {\n    // Clear existing interval if any\n    if (intervalRefs.current[category]) {\n      clearInterval(intervalRefs.current[category]);\n    }\n    updateTaskState(category, {\n      taskId: taskId,\n      status: 'Pending',\n      error: '',\n      progress: null\n    });\n\n    // Poll immediately first time\n    checkStatus(category, taskId);\n\n    // Set up interval for subsequent polls\n    intervalRefs.current[category] = setInterval(() => {\n      checkStatus(category, taskId);\n    }, 5000); // Poll every 5 seconds\n  }, []); // No dependencies needed for useCallback here\n\n  // Function to check task status\n  const checkStatus = async (category, taskId) => {\n    try {\n      const response = await getTaskStatus(taskId);\n      const taskData = response.data; // { task_id, status, result, error }\n      const status = taskData.status;\n      const result = taskData.result; // Can be a dict like { 'current': x, 'total': y } or a percentage, or other info\n      const taskError = taskData.error; // Specific error message from the task\n\n      let displayStatus = status;\n      let progressValue = null;\n      let errorMessage = taskError || '';\n      if (status === 'PROGRESS') {\n        if (result && typeof result === 'object' && result.hasOwnProperty('current') && result.hasOwnProperty('total') && result.total > 0) {\n          progressValue = result.current / result.total * 100;\n          displayStatus = `Computing: ${result.current}/${result.total}`;\n        } else if (typeof result === 'number' && result >= 0 && result <= 100) {\n          // If result is a percentage\n          progressValue = result;\n          displayStatus = `Computing: ${result.toFixed(0)}%`;\n        } else if (typeof result === 'string') {\n          // If result is a descriptive string\n          displayStatus = `Computing: ${result}`;\n        } else {\n          displayStatus = 'Computing...'; // Generic progress\n        }\n      } else if (status === 'SUCCESS') {\n        displayStatus = 'Done';\n        if (result && typeof result === 'string') {\n          displayStatus = `Done: ${result}`; // e.g., \"Done: 1500 features computed.\"\n        } else if (result && result.message) {\n          displayStatus = `Done: ${result.message}`;\n        }\n      } else if (status === 'FAILURE') {\n        displayStatus = 'Error';\n        // errorMessage is already set from taskData.error\n        if (!errorMessage && result && typeof result === 'string') {\n          errorMessage = result; // Use result as error message if taskData.error is empty\n        } else if (!errorMessage && result && result.error) {\n          errorMessage = result.error;\n        }\n        if (!errorMessage) errorMessage = 'Task failed with no specific details.';\n      }\n      updateTaskState(category, {\n        status: displayStatus,\n        progress: progressValue,\n        error: errorMessage\n      });\n\n      // Stop polling if task is completed or failed\n      if (status === 'SUCCESS' || status === 'FAILURE') {\n        if (intervalRefs.current[category]) {\n          clearInterval(intervalRefs.current[category]);\n          intervalRefs.current[category] = null;\n        }\n      }\n    } catch (error) {\n      var _error$response, _error$response$data, _error$response2, _error$response2$data;\n      // This catch is for network errors or issues with getTaskStatus itself\n      console.error(`Error fetching status for task ${taskId} (${category}):`, error);\n      const apiErrorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.detail) || error.message;\n      updateTaskState(category, {\n        status: 'Error',\n        error: `Failed to fetch task status: ${apiErrorMessage}`\n      });\n      if (intervalRefs.current[category]) {\n        clearInterval(intervalRefs.current[category]);\n        intervalRefs.current[category] = null;\n      }\n    }\n  };\n\n  // Function to handle button click\n  const handleComputeClick = async category => {\n    setLoading(prevLoading => ({\n      ...prevLoading,\n      [category]: true\n    }));\n    updateTaskState(category, {\n      status: 'Starting...',\n      error: '',\n      progress: null\n    }); // Reset state\n\n    try {\n      const response = await computeFeatures(category);\n      const taskData = response.data; // { task_id, status, result, error }\n\n      if (taskData !== null && taskData !== void 0 && taskData.task_id) {\n        // Asynchronous task started, begin polling\n        startPolling(category, taskData.task_id);\n      } else if ((taskData === null || taskData === void 0 ? void 0 : taskData.status) === 'SUCCESS') {\n        // Synchronous task completed successfully\n        updateTaskState(category, {\n          status: 'Done',\n          error: '',\n          progress: 100\n        });\n        // Optionally show a success message via snackbar if needed\n        // showSnackbar(`Feature computation for ${category} completed successfully (synchronous).`, 'success');\n      } else {\n        // Handle cases where task_id is null and status is not SUCCESS (e.g., backend error before task dispatch)\n        throw new Error((taskData === null || taskData === void 0 ? void 0 : taskData.error) || \"No task ID received and task did not complete synchronously.\");\n      }\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      console.error(`Error starting computation for ${category}:`, error);\n      updateTaskState(category, {\n        status: 'Error',\n        error: `Failed to start task: ${((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.detail) || error.message}`,\n        taskId: null\n      });\n    } finally {\n      setLoading(prevLoading => ({\n        ...prevLoading,\n        [category]: false\n      }));\n    }\n  };\n\n  // Clear intervals on component unmount\n  useEffect(() => {\n    return () => {\n      Object.values(intervalRefs.current).forEach(clearInterval);\n    };\n  }, []);\n  const getStatusColor = status => {\n    if (status === 'Done') return 'success';\n    if (status === 'Error') return 'error';\n    if (status === 'Idle') return 'default';\n    return 'info'; // Pending, Starting, Computing\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      mt: 2\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      gutterBottom: true,\n      children: \"Trigger Feature Computation\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body2\",\n      color: \"textSecondary\",\n      sx: {\n        mb: 3\n      },\n      children: \"Recompute feature vectors for all images within a specific category. This may take some time depending on the number of images and models.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      children: IP_CATEGORIES.map(category =>\n      /*#__PURE__*/\n      // Remove 'item' prop, use responsive props directly\n      _jsxDEV(Grid, {\n        xs: 12,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Paper, {\n          elevation: 2,\n          sx: {\n            p: 2,\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              textTransform: 'capitalize',\n              mb: 1\n            },\n            children: category\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              mb: 2,\n              height: 60\n            },\n            children: [\" \", tasks[category].error ? /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"error\",\n              sx: {\n                textAlign: 'left',\n                fontSize: '0.8rem'\n              },\n              children: tasks[category].error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(Chip, {\n                label: tasks[category].status,\n                color: getStatusColor(tasks[category].status),\n                sx: {\n                  mb: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 21\n              }, this), tasks[category].status.startsWith('Computing') && tasks[category].progress !== null && /*#__PURE__*/_jsxDEV(LinearProgress, {\n                variant: \"determinate\",\n                value: tasks[category].progress,\n                sx: {\n                  mt: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 24\n              }, this), tasks[category].status.startsWith('Computing') && tasks[category].progress === null && /*#__PURE__*/_jsxDEV(LinearProgress, {\n                sx: {\n                  mt: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 24\n              }, this) // Indeterminate if no progress value\n              ]\n            }, void 0, true)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: loading[category] ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20,\n              color: \"inherit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 48\n            }, this) : /*#__PURE__*/_jsxDEV(PlayArrowIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 97\n            }, this),\n            onClick: () => handleComputeClick(category),\n            disabled: loading[category] || tasks[category].status.startsWith('Computing') || tasks[category].status === 'Pending' || tasks[category].status === 'Starting...',\n            fullWidth: true,\n            children: [\"Recompute \", category.charAt(0).toUpperCase() + category.slice(1), \" Features\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 13\n        }, this)\n      }, category, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 181,\n    columnNumber: 5\n  }, this);\n};\n_s(FeatureComputation, \"C97pYR3SzHvQ0sPj/53WkVUm+JQ=\");\n_c = FeatureComputation;\nexport default FeatureComputation;\nvar _c;\n$RefreshReg$(_c, \"FeatureComputation\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "Box", "Typography", "<PERSON><PERSON>", "CircularProgress", "<PERSON><PERSON>", "Paper", "Grid", "Chip", "LinearProgress", "PlayArrowIcon", "computeFeatures", "getTaskStatus", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "IP_CATEGORIES", "FeatureComputation", "_s", "tasks", "setTasks", "reduce", "acc", "category", "taskId", "status", "error", "progress", "loading", "setLoading", "intervalRefs", "updateTaskState", "newState", "prevTasks", "startPolling", "current", "clearInterval", "checkStatus", "setInterval", "response", "taskData", "data", "result", "taskError", "displayStatus", "progressValue", "errorMessage", "hasOwnProperty", "total", "toFixed", "message", "_error$response", "_error$response$data", "_error$response2", "_error$response2$data", "console", "apiErrorMessage", "detail", "handleComputeClick", "prevLoading", "task_id", "Error", "_error$response3", "_error$response3$data", "Object", "values", "for<PERSON>ach", "getStatusColor", "sx", "mt", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "mb", "container", "spacing", "map", "xs", "md", "elevation", "p", "textAlign", "textTransform", "height", "severity", "fontSize", "label", "startsWith", "value", "startIcon", "size", "onClick", "disabled", "fullWidth", "char<PERSON>t", "toUpperCase", "slice", "_c", "$RefreshReg$"], "sources": ["D:/Documents/Programing/TRO/ModelTestsWorkbench/frontend/src/components/model-test-workbench/FeatureComputation.js"], "sourcesContent": ["import React, { useState, useEffect, useRef, useCallback } from 'react';\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Button,\r\n  CircularProgress,\r\n  Alert,\r\n  Paper,\r\n  Grid,\r\n  Chip, // To display status\r\n  LinearProgress, // For progress indication\r\n} from '@mui/material';\r\nimport PlayArrowIcon from '@mui/icons-material/PlayArrow';\r\nimport { computeFeatures, getTaskStatus } from '../../services/api_model_workbench';\r\n\r\nconst IP_CATEGORIES = [\"trademark\", \"copyright\", \"patent\"]; // Use lowercase as likely expected by API path\r\n\r\nconst FeatureComputation = () => {\r\n  // State to hold task info: { taskId: string | null, status: string, error: string, progress: number | null }\r\n  const [tasks, setTasks] = useState(\r\n    IP_CATEGORIES.reduce((acc, category) => {\r\n      acc[category] = { taskId: null, status: 'Idle', error: '', progress: null };\r\n      return acc;\r\n    }, {})\r\n  );\r\n  const [loading, setLoading] = useState(\r\n    IP_CATEGORIES.reduce((acc, category) => {\r\n      acc[category] = false; // Loading state for triggering the task\r\n      return acc;\r\n    }, {})\r\n  );\r\n\r\n  const intervalRefs = useRef({}); // Store interval IDs for polling\r\n\r\n  // Function to update task state\r\n  const updateTaskState = (category, newState) => {\r\n    setTasks(prevTasks => ({\r\n      ...prevTasks,\r\n      [category]: { ...prevTasks[category], ...newState },\r\n    }));\r\n  };\r\n\r\n  // Function to start polling for a task\r\n  const startPolling = useCallback((category, taskId) => {\r\n    // Clear existing interval if any\r\n    if (intervalRefs.current[category]) {\r\n      clearInterval(intervalRefs.current[category]);\r\n    }\r\n\r\n    updateTaskState(category, { taskId: taskId, status: 'Pending', error: '', progress: null });\r\n\r\n    // Poll immediately first time\r\n    checkStatus(category, taskId);\r\n\r\n    // Set up interval for subsequent polls\r\n    intervalRefs.current[category] = setInterval(() => {\r\n      checkStatus(category, taskId);\r\n    }, 5000); // Poll every 5 seconds\r\n  }, []); // No dependencies needed for useCallback here\r\n\r\n  // Function to check task status\r\n  const checkStatus = async (category, taskId) => {\r\n    try {\r\n      const response = await getTaskStatus(taskId);\r\n      const taskData = response.data; // { task_id, status, result, error }\r\n      const status = taskData.status;\r\n      const result = taskData.result; // Can be a dict like { 'current': x, 'total': y } or a percentage, or other info\r\n      const taskError = taskData.error; // Specific error message from the task\r\n\r\n      let displayStatus = status;\r\n      let progressValue = null;\r\n      let errorMessage = taskError || '';\r\n\r\n      if (status === 'PROGRESS') {\r\n        if (result && typeof result === 'object' && result.hasOwnProperty('current') && result.hasOwnProperty('total') && result.total > 0) {\r\n          progressValue = (result.current / result.total) * 100;\r\n          displayStatus = `Computing: ${result.current}/${result.total}`;\r\n        } else if (typeof result === 'number' && result >= 0 && result <= 100) { // If result is a percentage\r\n          progressValue = result;\r\n          displayStatus = `Computing: ${result.toFixed(0)}%`;\r\n        } else if (typeof result === 'string') { // If result is a descriptive string\r\n            displayStatus = `Computing: ${result}`;\r\n        } else {\r\n          displayStatus = 'Computing...'; // Generic progress\r\n        }\r\n      } else if (status === 'SUCCESS') {\r\n        displayStatus = 'Done';\r\n        if (result && typeof result === 'string') {\r\n            displayStatus = `Done: ${result}`; // e.g., \"Done: 1500 features computed.\"\r\n        } else if (result && result.message) {\r\n            displayStatus = `Done: ${result.message}`;\r\n        }\r\n      } else if (status === 'FAILURE') {\r\n        displayStatus = 'Error';\r\n        // errorMessage is already set from taskData.error\r\n        if (!errorMessage && result && typeof result === 'string') {\r\n            errorMessage = result; // Use result as error message if taskData.error is empty\r\n        } else if (!errorMessage && result && result.error) {\r\n            errorMessage = result.error;\r\n        }\r\n        if (!errorMessage) errorMessage = 'Task failed with no specific details.';\r\n      }\r\n\r\n      updateTaskState(category, {\r\n        status: displayStatus,\r\n        progress: progressValue,\r\n        error: errorMessage,\r\n      });\r\n\r\n      // Stop polling if task is completed or failed\r\n      if (status === 'SUCCESS' || status === 'FAILURE') {\r\n        if (intervalRefs.current[category]) {\r\n          clearInterval(intervalRefs.current[category]);\r\n          intervalRefs.current[category] = null;\r\n        }\r\n      }\r\n    } catch (error) { // This catch is for network errors or issues with getTaskStatus itself\r\n      console.error(`Error fetching status for task ${taskId} (${category}):`, error);\r\n      const apiErrorMessage = error.response?.data?.error || error.response?.data?.detail || error.message;\r\n      updateTaskState(category, {\r\n        status: 'Error',\r\n        error: `Failed to fetch task status: ${apiErrorMessage}`,\r\n      });\r\n      if (intervalRefs.current[category]) {\r\n        clearInterval(intervalRefs.current[category]);\r\n        intervalRefs.current[category] = null;\r\n      }\r\n    }\r\n  };\r\n\r\n  // Function to handle button click\r\n  const handleComputeClick = async (category) => {\r\n    setLoading(prevLoading => ({ ...prevLoading, [category]: true }));\r\n    updateTaskState(category, { status: 'Starting...', error: '', progress: null }); // Reset state\r\n\r\n    try {\r\n      const response = await computeFeatures(category);\r\n      const taskData = response.data; // { task_id, status, result, error }\r\n\r\n      if (taskData?.task_id) {\r\n        // Asynchronous task started, begin polling\r\n        startPolling(category, taskData.task_id);\r\n      } else if (taskData?.status === 'SUCCESS') {\r\n        // Synchronous task completed successfully\r\n        updateTaskState(category, { status: 'Done', error: '', progress: 100 });\r\n        // Optionally show a success message via snackbar if needed\r\n        // showSnackbar(`Feature computation for ${category} completed successfully (synchronous).`, 'success');\r\n      }\r\n       else {\r\n        // Handle cases where task_id is null and status is not SUCCESS (e.g., backend error before task dispatch)\r\n        throw new Error(taskData?.error || \"No task ID received and task did not complete synchronously.\");\r\n      }\r\n    } catch (error) {\r\n      console.error(`Error starting computation for ${category}:`, error);\r\n      updateTaskState(category, {\r\n          status: 'Error',\r\n          error: `Failed to start task: ${error.response?.data?.detail || error.message}`,\r\n          taskId: null,\r\n      });\r\n    } finally {\r\n      setLoading(prevLoading => ({ ...prevLoading, [category]: false }));\r\n    }\r\n  };\r\n\r\n  // Clear intervals on component unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      Object.values(intervalRefs.current).forEach(clearInterval);\r\n    };\r\n  }, []);\r\n\r\n  const getStatusColor = (status) => {\r\n    if (status === 'Done') return 'success';\r\n    if (status === 'Error') return 'error';\r\n    if (status === 'Idle') return 'default';\r\n    return 'info'; // Pending, Starting, Computing\r\n  };\r\n\r\n\r\n  return (\r\n    <Box sx={{ mt: 2 }}>\r\n      <Typography variant=\"h6\" gutterBottom>Trigger Feature Computation</Typography>\r\n      <Typography variant=\"body2\" color=\"textSecondary\" sx={{ mb: 3 }}>\r\n        Recompute feature vectors for all images within a specific category. This may take some time depending on the number of images and models.\r\n      </Typography>\r\n\r\n      <Grid container spacing={3}>\r\n        {IP_CATEGORIES.map((category) => (\r\n          // Remove 'item' prop, use responsive props directly\r\n          <Grid xs={12} md={4} key={category}>\r\n            <Paper elevation={2} sx={{ p: 2, textAlign: 'center' }}>\r\n              <Typography variant=\"h6\" sx={{ textTransform: 'capitalize', mb: 1 }}>\r\n                {category}\r\n              </Typography>\r\n              <Box sx={{ mb: 2, height: 60 }}> {/* Fixed height for status/error */}\r\n                {tasks[category].error ? (\r\n                  <Alert severity=\"error\" sx={{ textAlign: 'left', fontSize: '0.8rem' }}>{tasks[category].error}</Alert>\r\n                ) : (\r\n                  <>\r\n                    <Chip\r\n                      label={tasks[category].status}\r\n                      color={getStatusColor(tasks[category].status)}\r\n                      sx={{ mb: 1 }}\r\n                    />\r\n                    {tasks[category].status.startsWith('Computing') && tasks[category].progress !== null && (\r\n                       <LinearProgress variant=\"determinate\" value={tasks[category].progress} sx={{ mt: 1 }}/>\r\n                    )}\r\n                     {tasks[category].status.startsWith('Computing') && tasks[category].progress === null && (\r\n                       <LinearProgress sx={{ mt: 1 }}/> // Indeterminate if no progress value\r\n                    )}\r\n                  </>\r\n                )}\r\n              </Box>\r\n              <Button\r\n                variant=\"contained\"\r\n                startIcon={loading[category] ? <CircularProgress size={20} color=\"inherit\" /> : <PlayArrowIcon />}\r\n                onClick={() => handleComputeClick(category)}\r\n                disabled={loading[category] || tasks[category].status.startsWith('Computing') || tasks[category].status === 'Pending' || tasks[category].status === 'Starting...'}\r\n                fullWidth\r\n              >\r\n                Recompute {category.charAt(0).toUpperCase() + category.slice(1)} Features\r\n              </Button>\r\n            </Paper>\r\n          </Grid>\r\n        ))}\r\n      </Grid>\r\n       {/* Optional: Section to list images with missing features - requires backend support */}\r\n       {/* <Box sx={{ mt: 4 }}>\r\n         <Typography variant=\"h6\">Images Missing Features</Typography>\r\n         <Typography>List of images missing features could go here if backend endpoint exists.</Typography>\r\n       </Box> */}\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default FeatureComputation;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AACvE,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,gBAAgB,EAChBC,KAAK,EACLC,KAAK,EACLC,IAAI,EACJC,IAAI;AAAE;AACNC,cAAc,CAAE;AAAA,OACX,eAAe;AACtB,OAAOC,aAAa,MAAM,+BAA+B;AACzD,SAASC,eAAe,EAAEC,aAAa,QAAQ,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpF,MAAMC,aAAa,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC;;AAE5D,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B;EACA,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAChCoB,aAAa,CAACK,MAAM,CAAC,CAACC,GAAG,EAAEC,QAAQ,KAAK;IACtCD,GAAG,CAACC,QAAQ,CAAC,GAAG;MAAEC,MAAM,EAAE,IAAI;MAAEC,MAAM,EAAE,MAAM;MAAEC,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAK,CAAC;IAC3E,OAAOL,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CACP,CAAC;EACD,MAAM,CAACM,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CACpCoB,aAAa,CAACK,MAAM,CAAC,CAACC,GAAG,EAAEC,QAAQ,KAAK;IACtCD,GAAG,CAACC,QAAQ,CAAC,GAAG,KAAK,CAAC,CAAC;IACvB,OAAOD,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CACP,CAAC;EAED,MAAMQ,YAAY,GAAGhC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEjC;EACA,MAAMiC,eAAe,GAAGA,CAACR,QAAQ,EAAES,QAAQ,KAAK;IAC9CZ,QAAQ,CAACa,SAAS,KAAK;MACrB,GAAGA,SAAS;MACZ,CAACV,QAAQ,GAAG;QAAE,GAAGU,SAAS,CAACV,QAAQ,CAAC;QAAE,GAAGS;MAAS;IACpD,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAME,YAAY,GAAGnC,WAAW,CAAC,CAACwB,QAAQ,EAAEC,MAAM,KAAK;IACrD;IACA,IAAIM,YAAY,CAACK,OAAO,CAACZ,QAAQ,CAAC,EAAE;MAClCa,aAAa,CAACN,YAAY,CAACK,OAAO,CAACZ,QAAQ,CAAC,CAAC;IAC/C;IAEAQ,eAAe,CAACR,QAAQ,EAAE;MAAEC,MAAM,EAAEA,MAAM;MAAEC,MAAM,EAAE,SAAS;MAAEC,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;;IAE3F;IACAU,WAAW,CAACd,QAAQ,EAAEC,MAAM,CAAC;;IAE7B;IACAM,YAAY,CAACK,OAAO,CAACZ,QAAQ,CAAC,GAAGe,WAAW,CAAC,MAAM;MACjDD,WAAW,CAACd,QAAQ,EAAEC,MAAM,CAAC;IAC/B,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACA,MAAMa,WAAW,GAAG,MAAAA,CAAOd,QAAQ,EAAEC,MAAM,KAAK;IAC9C,IAAI;MACF,MAAMe,QAAQ,GAAG,MAAM5B,aAAa,CAACa,MAAM,CAAC;MAC5C,MAAMgB,QAAQ,GAAGD,QAAQ,CAACE,IAAI,CAAC,CAAC;MAChC,MAAMhB,MAAM,GAAGe,QAAQ,CAACf,MAAM;MAC9B,MAAMiB,MAAM,GAAGF,QAAQ,CAACE,MAAM,CAAC,CAAC;MAChC,MAAMC,SAAS,GAAGH,QAAQ,CAACd,KAAK,CAAC,CAAC;;MAElC,IAAIkB,aAAa,GAAGnB,MAAM;MAC1B,IAAIoB,aAAa,GAAG,IAAI;MACxB,IAAIC,YAAY,GAAGH,SAAS,IAAI,EAAE;MAElC,IAAIlB,MAAM,KAAK,UAAU,EAAE;QACzB,IAAIiB,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACK,cAAc,CAAC,SAAS,CAAC,IAAIL,MAAM,CAACK,cAAc,CAAC,OAAO,CAAC,IAAIL,MAAM,CAACM,KAAK,GAAG,CAAC,EAAE;UAClIH,aAAa,GAAIH,MAAM,CAACP,OAAO,GAAGO,MAAM,CAACM,KAAK,GAAI,GAAG;UACrDJ,aAAa,GAAG,cAAcF,MAAM,CAACP,OAAO,IAAIO,MAAM,CAACM,KAAK,EAAE;QAChE,CAAC,MAAM,IAAI,OAAON,MAAM,KAAK,QAAQ,IAAIA,MAAM,IAAI,CAAC,IAAIA,MAAM,IAAI,GAAG,EAAE;UAAE;UACvEG,aAAa,GAAGH,MAAM;UACtBE,aAAa,GAAG,cAAcF,MAAM,CAACO,OAAO,CAAC,CAAC,CAAC,GAAG;QACpD,CAAC,MAAM,IAAI,OAAOP,MAAM,KAAK,QAAQ,EAAE;UAAE;UACrCE,aAAa,GAAG,cAAcF,MAAM,EAAE;QAC1C,CAAC,MAAM;UACLE,aAAa,GAAG,cAAc,CAAC,CAAC;QAClC;MACF,CAAC,MAAM,IAAInB,MAAM,KAAK,SAAS,EAAE;QAC/BmB,aAAa,GAAG,MAAM;QACtB,IAAIF,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;UACtCE,aAAa,GAAG,SAASF,MAAM,EAAE,CAAC,CAAC;QACvC,CAAC,MAAM,IAAIA,MAAM,IAAIA,MAAM,CAACQ,OAAO,EAAE;UACjCN,aAAa,GAAG,SAASF,MAAM,CAACQ,OAAO,EAAE;QAC7C;MACF,CAAC,MAAM,IAAIzB,MAAM,KAAK,SAAS,EAAE;QAC/BmB,aAAa,GAAG,OAAO;QACvB;QACA,IAAI,CAACE,YAAY,IAAIJ,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;UACvDI,YAAY,GAAGJ,MAAM,CAAC,CAAC;QAC3B,CAAC,MAAM,IAAI,CAACI,YAAY,IAAIJ,MAAM,IAAIA,MAAM,CAAChB,KAAK,EAAE;UAChDoB,YAAY,GAAGJ,MAAM,CAAChB,KAAK;QAC/B;QACA,IAAI,CAACoB,YAAY,EAAEA,YAAY,GAAG,uCAAuC;MAC3E;MAEAf,eAAe,CAACR,QAAQ,EAAE;QACxBE,MAAM,EAAEmB,aAAa;QACrBjB,QAAQ,EAAEkB,aAAa;QACvBnB,KAAK,EAAEoB;MACT,CAAC,CAAC;;MAEF;MACA,IAAIrB,MAAM,KAAK,SAAS,IAAIA,MAAM,KAAK,SAAS,EAAE;QAChD,IAAIK,YAAY,CAACK,OAAO,CAACZ,QAAQ,CAAC,EAAE;UAClCa,aAAa,CAACN,YAAY,CAACK,OAAO,CAACZ,QAAQ,CAAC,CAAC;UAC7CO,YAAY,CAACK,OAAO,CAACZ,QAAQ,CAAC,GAAG,IAAI;QACvC;MACF;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MAAA,IAAAyB,eAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MAAE;MAChBC,OAAO,CAAC7B,KAAK,CAAC,kCAAkCF,MAAM,KAAKD,QAAQ,IAAI,EAAEG,KAAK,CAAC;MAC/E,MAAM8B,eAAe,GAAG,EAAAL,eAAA,GAAAzB,KAAK,CAACa,QAAQ,cAAAY,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBV,IAAI,cAAAW,oBAAA,uBAApBA,oBAAA,CAAsB1B,KAAK,OAAA2B,gBAAA,GAAI3B,KAAK,CAACa,QAAQ,cAAAc,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBZ,IAAI,cAAAa,qBAAA,uBAApBA,qBAAA,CAAsBG,MAAM,KAAI/B,KAAK,CAACwB,OAAO;MACpGnB,eAAe,CAACR,QAAQ,EAAE;QACxBE,MAAM,EAAE,OAAO;QACfC,KAAK,EAAE,gCAAgC8B,eAAe;MACxD,CAAC,CAAC;MACF,IAAI1B,YAAY,CAACK,OAAO,CAACZ,QAAQ,CAAC,EAAE;QAClCa,aAAa,CAACN,YAAY,CAACK,OAAO,CAACZ,QAAQ,CAAC,CAAC;QAC7CO,YAAY,CAACK,OAAO,CAACZ,QAAQ,CAAC,GAAG,IAAI;MACvC;IACF;EACF,CAAC;;EAED;EACA,MAAMmC,kBAAkB,GAAG,MAAOnC,QAAQ,IAAK;IAC7CM,UAAU,CAAC8B,WAAW,KAAK;MAAE,GAAGA,WAAW;MAAE,CAACpC,QAAQ,GAAG;IAAK,CAAC,CAAC,CAAC;IACjEQ,eAAe,CAACR,QAAQ,EAAE;MAAEE,MAAM,EAAE,aAAa;MAAEC,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC,CAAC;;IAEjF,IAAI;MACF,MAAMY,QAAQ,GAAG,MAAM7B,eAAe,CAACa,QAAQ,CAAC;MAChD,MAAMiB,QAAQ,GAAGD,QAAQ,CAACE,IAAI,CAAC,CAAC;;MAEhC,IAAID,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEoB,OAAO,EAAE;QACrB;QACA1B,YAAY,CAACX,QAAQ,EAAEiB,QAAQ,CAACoB,OAAO,CAAC;MAC1C,CAAC,MAAM,IAAI,CAAApB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEf,MAAM,MAAK,SAAS,EAAE;QACzC;QACAM,eAAe,CAACR,QAAQ,EAAE;UAAEE,MAAM,EAAE,MAAM;UAAEC,KAAK,EAAE,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAC,CAAC;QACvE;QACA;MACF,CAAC,MACK;QACJ;QACA,MAAM,IAAIkC,KAAK,CAAC,CAAArB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEd,KAAK,KAAI,8DAA8D,CAAC;MACpG;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MAAA,IAAAoC,gBAAA,EAAAC,qBAAA;MACdR,OAAO,CAAC7B,KAAK,CAAC,kCAAkCH,QAAQ,GAAG,EAAEG,KAAK,CAAC;MACnEK,eAAe,CAACR,QAAQ,EAAE;QACtBE,MAAM,EAAE,OAAO;QACfC,KAAK,EAAE,yBAAyB,EAAAoC,gBAAA,GAAApC,KAAK,CAACa,QAAQ,cAAAuB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrB,IAAI,cAAAsB,qBAAA,uBAApBA,qBAAA,CAAsBN,MAAM,KAAI/B,KAAK,CAACwB,OAAO,EAAE;QAC/E1B,MAAM,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,SAAS;MACRK,UAAU,CAAC8B,WAAW,KAAK;QAAE,GAAGA,WAAW;QAAE,CAACpC,QAAQ,GAAG;MAAM,CAAC,CAAC,CAAC;IACpE;EACF,CAAC;;EAED;EACA1B,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXmE,MAAM,CAACC,MAAM,CAACnC,YAAY,CAACK,OAAO,CAAC,CAAC+B,OAAO,CAAC9B,aAAa,CAAC;IAC5D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM+B,cAAc,GAAI1C,MAAM,IAAK;IACjC,IAAIA,MAAM,KAAK,MAAM,EAAE,OAAO,SAAS;IACvC,IAAIA,MAAM,KAAK,OAAO,EAAE,OAAO,OAAO;IACtC,IAAIA,MAAM,KAAK,MAAM,EAAE,OAAO,SAAS;IACvC,OAAO,MAAM,CAAC,CAAC;EACjB,CAAC;EAGD,oBACEZ,OAAA,CAACb,GAAG;IAACoE,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBACjBzD,OAAA,CAACZ,UAAU;MAACsE,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAA2B;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAC9E/D,OAAA,CAACZ,UAAU;MAACsE,OAAO,EAAC,OAAO;MAACM,KAAK,EAAC,eAAe;MAACT,EAAE,EAAE;QAAEU,EAAE,EAAE;MAAE,CAAE;MAAAR,QAAA,EAAC;IAEjE;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEb/D,OAAA,CAACP,IAAI;MAACyE,SAAS;MAACC,OAAO,EAAE,CAAE;MAAAV,QAAA,EACxBtD,aAAa,CAACiE,GAAG,CAAE1D,QAAQ;MAAA;MAC1B;MACAV,OAAA,CAACP,IAAI;QAAC4E,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAb,QAAA,eAClBzD,OAAA,CAACR,KAAK;UAAC+E,SAAS,EAAE,CAAE;UAAChB,EAAE,EAAE;YAAEiB,CAAC,EAAE,CAAC;YAAEC,SAAS,EAAE;UAAS,CAAE;UAAAhB,QAAA,gBACrDzD,OAAA,CAACZ,UAAU;YAACsE,OAAO,EAAC,IAAI;YAACH,EAAE,EAAE;cAAEmB,aAAa,EAAE,YAAY;cAAET,EAAE,EAAE;YAAE,CAAE;YAAAR,QAAA,EACjE/C;UAAQ;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACb/D,OAAA,CAACb,GAAG;YAACoE,EAAE,EAAE;cAAEU,EAAE,EAAE,CAAC;cAAEU,MAAM,EAAE;YAAG,CAAE;YAAAlB,QAAA,GAAC,GAAC,EAC9BnD,KAAK,CAACI,QAAQ,CAAC,CAACG,KAAK,gBACpBb,OAAA,CAACT,KAAK;cAACqF,QAAQ,EAAC,OAAO;cAACrB,EAAE,EAAE;gBAAEkB,SAAS,EAAE,MAAM;gBAAEI,QAAQ,EAAE;cAAS,CAAE;cAAApB,QAAA,EAAEnD,KAAK,CAACI,QAAQ,CAAC,CAACG;YAAK;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,gBAEtG/D,OAAA,CAAAE,SAAA;cAAAuD,QAAA,gBACEzD,OAAA,CAACN,IAAI;gBACHoF,KAAK,EAAExE,KAAK,CAACI,QAAQ,CAAC,CAACE,MAAO;gBAC9BoD,KAAK,EAAEV,cAAc,CAAChD,KAAK,CAACI,QAAQ,CAAC,CAACE,MAAM,CAAE;gBAC9C2C,EAAE,EAAE;kBAAEU,EAAE,EAAE;gBAAE;cAAE;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,EACDzD,KAAK,CAACI,QAAQ,CAAC,CAACE,MAAM,CAACmE,UAAU,CAAC,WAAW,CAAC,IAAIzE,KAAK,CAACI,QAAQ,CAAC,CAACI,QAAQ,KAAK,IAAI,iBACjFd,OAAA,CAACL,cAAc;gBAAC+D,OAAO,EAAC,aAAa;gBAACsB,KAAK,EAAE1E,KAAK,CAACI,QAAQ,CAAC,CAACI,QAAS;gBAACyC,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE;cAAE;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CACxF,EACCzD,KAAK,CAACI,QAAQ,CAAC,CAACE,MAAM,CAACmE,UAAU,CAAC,WAAW,CAAC,IAAIzE,KAAK,CAACI,QAAQ,CAAC,CAACI,QAAQ,KAAK,IAAI,iBAClFd,OAAA,CAACL,cAAc;gBAAC4D,EAAE,EAAE;kBAAEC,EAAE,EAAE;gBAAE;cAAE;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC,CAAC,CAAC;cACnC;YAAA,eACD,CACH;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACN/D,OAAA,CAACX,MAAM;YACLqE,OAAO,EAAC,WAAW;YACnBuB,SAAS,EAAElE,OAAO,CAACL,QAAQ,CAAC,gBAAGV,OAAA,CAACV,gBAAgB;cAAC4F,IAAI,EAAE,EAAG;cAAClB,KAAK,EAAC;YAAS;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG/D,OAAA,CAACJ,aAAa;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAClGoB,OAAO,EAAEA,CAAA,KAAMtC,kBAAkB,CAACnC,QAAQ,CAAE;YAC5C0E,QAAQ,EAAErE,OAAO,CAACL,QAAQ,CAAC,IAAIJ,KAAK,CAACI,QAAQ,CAAC,CAACE,MAAM,CAACmE,UAAU,CAAC,WAAW,CAAC,IAAIzE,KAAK,CAACI,QAAQ,CAAC,CAACE,MAAM,KAAK,SAAS,IAAIN,KAAK,CAACI,QAAQ,CAAC,CAACE,MAAM,KAAK,aAAc;YAClKyE,SAAS;YAAA5B,QAAA,GACV,YACW,EAAC/C,QAAQ,CAAC4E,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG7E,QAAQ,CAAC8E,KAAK,CAAC,CAAC,CAAC,EAAC,WAClE;UAAA;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC,GAjCgBrD,QAAQ;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAkC5B,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAMJ,CAAC;AAEV,CAAC;AAAC1D,EAAA,CAxNID,kBAAkB;AAAAqF,EAAA,GAAlBrF,kBAAkB;AA0NxB,eAAeA,kBAAkB;AAAC,IAAAqF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}