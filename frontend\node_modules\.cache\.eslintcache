[{"D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\index.js": "1", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\App.js": "2", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\reportWebVitals.js": "3", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\Layout.js": "4", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\services\\api.js": "5", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\PatentPlatformPage.js": "6", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\patent-viz\\PatentExplorerPage.js": "7", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\patent-viz\\PatentDashboardPage.js": "8", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\navigation\\PlatformSwitcher.js": "9", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\FilterControls.js": "10", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\PatentTable.js": "11", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\QuickStatsDisplay.js": "12", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\PaginationControls.js": "13", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\PatentDetailModal.js": "14", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\PatentImageModal.js": "15", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\StatisticsSection.js": "16", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\SingleStatisticDisplay.js": "17", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\MissingDataDisplay.js": "18", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\PieChartDisplay.js": "19", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\model-test-workbench\\TrademarkPage.js": "20", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\model-test-workbench\\PatentPage.js": "21", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\model-test-workbench\\CopyrightPage.js": "22", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\model-test-workbench\\SettingsPage.js": "23", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\model-test-workbench\\DashboardPage.js": "24", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\ImageBrowser.js": "25", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\ByProductView.js": "26", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\FeatureComputation.js": "27", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\ByModelView.js": "28", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\ImageUpload.js": "29", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\MetricsDisplay.js": "30", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\ModelManagement.js": "31", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\CombinedScoresConfig.js": "32", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\CollectionManagement.js": "33", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\boundingbox\\ResultsPage.js": "34", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\boundingbox\\RankPage.js": "35", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\boundingbox\\PictureManagementPage.js": "36", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\boundingbox\\ModelPage.js": "37", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\services\\api_model_workbench.js": "38", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\services\\api_patent_viz.js": "39", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\services\\api_bounding_box.js": "40"}, {"size": 535, "mtime": 1746189119581, "results": "41", "hashOfConfig": "42"}, {"size": 2365, "mtime": 1749667229423, "results": "43", "hashOfConfig": "42"}, {"size": 362, "mtime": 1746189119762, "results": "44", "hashOfConfig": "42"}, {"size": 3013, "mtime": 1749667907793, "results": "45", "hashOfConfig": "42"}, {"size": 2236, "mtime": 1749667229456, "results": "46", "hashOfConfig": "42"}, {"size": 1504, "mtime": 1747118012267, "results": "47", "hashOfConfig": "42"}, {"size": 11006, "mtime": 1749667229455, "results": "48", "hashOfConfig": "42"}, {"size": 4077, "mtime": 1749667229453, "results": "49", "hashOfConfig": "42"}, {"size": 3042, "mtime": 1749667919431, "results": "50", "hashOfConfig": "42"}, {"size": 11874, "mtime": 1747313751026, "results": "51", "hashOfConfig": "42"}, {"size": 5071, "mtime": 1747117955797, "results": "52", "hashOfConfig": "42"}, {"size": 1209, "mtime": 1747117942276, "results": "53", "hashOfConfig": "42"}, {"size": 1499, "mtime": 1747117964199, "results": "54", "hashOfConfig": "42"}, {"size": 7088, "mtime": 1747117979725, "results": "55", "hashOfConfig": "42"}, {"size": 3725, "mtime": 1747117991227, "results": "56", "hashOfConfig": "42"}, {"size": 3647, "mtime": 1747930472886, "results": "57", "hashOfConfig": "42"}, {"size": 957, "mtime": 1747925526951, "results": "58", "hashOfConfig": "42"}, {"size": 1801, "mtime": 1747117784720, "results": "59", "hashOfConfig": "42"}, {"size": 3711, "mtime": 1747930450370, "results": "60", "hashOfConfig": "42"}, {"size": 2265, "mtime": 1747120927926, "results": "61", "hashOfConfig": "42"}, {"size": 2108, "mtime": 1747120944082, "results": "62", "hashOfConfig": "42"}, {"size": 2144, "mtime": 1747120959457, "results": "63", "hashOfConfig": "42"}, {"size": 3388, "mtime": 1747122415445, "results": "64", "hashOfConfig": "42"}, {"size": 2192, "mtime": 1747120951693, "results": "65", "hashOfConfig": "42"}, {"size": 20241, "mtime": 1749667229435, "results": "66", "hashOfConfig": "42"}, {"size": 23251, "mtime": 1749667229429, "results": "67", "hashOfConfig": "42"}, {"size": 10106, "mtime": 1749667229433, "results": "68", "hashOfConfig": "42"}, {"size": 30255, "mtime": 1749667229427, "results": "69", "hashOfConfig": "42"}, {"size": 8742, "mtime": 1749667229436, "results": "70", "hashOfConfig": "42"}, {"size": 16824, "mtime": 1749667229438, "results": "71", "hashOfConfig": "42"}, {"size": 5769, "mtime": 1749667229439, "results": "72", "hashOfConfig": "42"}, {"size": 18740, "mtime": 1749667229432, "results": "73", "hashOfConfig": "42"}, {"size": 6213, "mtime": 1749667229430, "results": "74", "hashOfConfig": "42"}, {"size": 18466, "mtime": 1749667987892, "results": "75", "hashOfConfig": "42"}, {"size": 4100, "mtime": 1749667229445, "results": "76", "hashOfConfig": "42"}, {"size": 12670, "mtime": 1749667229442, "results": "77", "hashOfConfig": "42"}, {"size": 4056, "mtime": 1749667229440, "results": "78", "hashOfConfig": "42"}, {"size": 4504, "mtime": 1749667229458, "results": "79", "hashOfConfig": "42"}, {"size": 1385, "mtime": 1749667229460, "results": "80", "hashOfConfig": "42"}, {"size": 11148, "mtime": 1749667229457, "results": "81", "hashOfConfig": "42"}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1te5mbn", {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\index.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\App.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\reportWebVitals.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\Layout.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\services\\api.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\PatentPlatformPage.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\patent-viz\\PatentExplorerPage.js", ["202"], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\patent-viz\\PatentDashboardPage.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\navigation\\PlatformSwitcher.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\FilterControls.js", ["203", "204"], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\PatentTable.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\QuickStatsDisplay.js", ["205"], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\PaginationControls.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\PatentDetailModal.js", ["206"], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\PatentImageModal.js", ["207"], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\StatisticsSection.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\SingleStatisticDisplay.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\MissingDataDisplay.js", ["208"], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\PieChartDisplay.js", ["209", "210"], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\model-test-workbench\\TrademarkPage.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\model-test-workbench\\PatentPage.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\model-test-workbench\\CopyrightPage.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\model-test-workbench\\SettingsPage.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\model-test-workbench\\DashboardPage.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\ImageBrowser.js", [], ["211"], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\ByProductView.js", ["212", "213", "214"], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\FeatureComputation.js", ["215", "216"], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\ByModelView.js", ["217", "218"], ["219"], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\ImageUpload.js", ["220"], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\MetricsDisplay.js", ["221"], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\ModelManagement.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\CombinedScoresConfig.js", ["222", "223", "224"], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\CollectionManagement.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\boundingbox\\ResultsPage.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\boundingbox\\RankPage.js", ["225"], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\boundingbox\\PictureManagementPage.js", ["226", "227", "228", "229"], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\boundingbox\\ModelPage.js", ["230"], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\services\\api_model_workbench.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\services\\api_patent_viz.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\services\\api_bounding_box.js", ["231"], [], {"ruleId": "232", "severity": 1, "message": "233", "line": 13, "column": 10, "nodeType": "234", "messageId": "235", "endLine": 13, "endColumn": 19}, {"ruleId": "232", "severity": 1, "message": "236", "line": 13, "column": 3, "nodeType": "234", "messageId": "235", "endLine": 13, "endColumn": 6}, {"ruleId": "237", "severity": 1, "message": "238", "line": 41, "column": 33, "nodeType": "234", "endLine": 41, "endColumn": 44}, {"ruleId": "232", "severity": 1, "message": "236", "line": 2, "column": 10, "nodeType": "234", "messageId": "235", "endLine": 2, "endColumn": 13}, {"ruleId": "232", "severity": 1, "message": "239", "line": 17, "column": 3, "nodeType": "234", "messageId": "235", "endLine": 17, "endColumn": 10}, {"ruleId": "232", "severity": 1, "message": "240", "line": 13, "column": 3, "nodeType": "234", "messageId": "235", "endLine": 13, "endColumn": 13}, {"ruleId": "232", "severity": 1, "message": "236", "line": 2, "column": 71, "nodeType": "234", "messageId": "235", "endLine": 2, "endColumn": 74}, {"ruleId": "232", "severity": 1, "message": "241", "line": 2, "column": 46, "nodeType": "234", "messageId": "235", "endLine": 2, "endColumn": 53}, {"ruleId": "232", "severity": 1, "message": "242", "line": 45, "column": 5, "nodeType": "234", "messageId": "235", "endLine": 45, "endColumn": 16}, {"ruleId": "237", "severity": 1, "message": "238", "line": 180, "column": 41, "nodeType": "234", "endLine": 180, "endColumn": 52, "suppressions": "243"}, {"ruleId": "232", "severity": 1, "message": "244", "line": 23, "column": 8, "nodeType": "234", "messageId": "235", "endLine": 23, "endColumn": 18}, {"ruleId": "237", "severity": 1, "message": "245", "line": 162, "column": 8, "nodeType": "246", "endLine": 162, "endColumn": 81, "suggestions": "247"}, {"ruleId": "237", "severity": 1, "message": "248", "line": 183, "column": 8, "nodeType": "246", "endLine": 183, "endColumn": 52, "suggestions": "249"}, {"ruleId": "237", "severity": 1, "message": "250", "line": 59, "column": 6, "nodeType": "246", "endLine": 59, "endColumn": 8, "suggestions": "251"}, {"ruleId": "237", "severity": 1, "message": "252", "line": 168, "column": 34, "nodeType": "234", "endLine": 168, "endColumn": 41}, {"ruleId": "232", "severity": 1, "message": "253", "line": 1, "column": 51, "nodeType": "234", "messageId": "235", "endLine": 1, "endColumn": 57}, {"ruleId": "232", "severity": 1, "message": "244", "line": 24, "column": 8, "nodeType": "234", "messageId": "235", "endLine": 24, "endColumn": 18}, {"ruleId": "237", "severity": 1, "message": "254", "line": 172, "column": 8, "nodeType": "246", "endLine": 172, "endColumn": 64, "suggestions": "255", "suppressions": "256"}, {"ruleId": "232", "severity": 1, "message": "257", "line": 12, "column": 3, "nodeType": "234", "messageId": "235", "endLine": 12, "endColumn": 8}, {"ruleId": "237", "severity": 1, "message": "258", "line": 89, "column": 8, "nodeType": "246", "endLine": 89, "endColumn": 35, "suggestions": "259"}, {"ruleId": "232", "severity": 1, "message": "260", "line": 1, "column": 51, "nodeType": "234", "messageId": "235", "endLine": 1, "endColumn": 58}, {"ruleId": "237", "severity": 1, "message": "261", "line": 62, "column": 6, "nodeType": "246", "endLine": 62, "endColumn": 8, "suggestions": "262"}, {"ruleId": "237", "severity": 1, "message": "261", "line": 73, "column": 6, "nodeType": "246", "endLine": 73, "endColumn": 8, "suggestions": "263"}, {"ruleId": "232", "severity": 1, "message": "260", "line": 1, "column": 38, "nodeType": "234", "messageId": "235", "endLine": 1, "endColumn": 45}, {"ruleId": "232", "severity": 1, "message": "264", "line": 4, "column": 3, "nodeType": "234", "messageId": "235", "endLine": 4, "endColumn": 7}, {"ruleId": "232", "severity": 1, "message": "265", "line": 4, "column": 9, "nodeType": "234", "messageId": "235", "endLine": 4, "endColumn": 17}, {"ruleId": "232", "severity": 1, "message": "266", "line": 4, "column": 19, "nodeType": "234", "messageId": "235", "endLine": 4, "endColumn": 31}, {"ruleId": "232", "severity": 1, "message": "267", "line": 7, "column": 15, "nodeType": "234", "messageId": "235", "endLine": 7, "endColumn": 19}, {"ruleId": "232", "severity": 1, "message": "236", "line": 15, "column": 3, "nodeType": "234", "messageId": "235", "endLine": 15, "endColumn": 6}, {"ruleId": "232", "severity": 1, "message": "268", "line": 1, "column": 8, "nodeType": "234", "messageId": "235", "endLine": 1, "endColumn": 17}, "no-unused-vars", "'DayPicker' is defined but never used.", "Identifier", "unusedVar", "'Box' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead.", "'Divider' is defined but never used.", "'Typography' is defined but never used.", "'Tooltip' is defined but never used.", "'hoverLabels' is assigned a value but never used.", ["269"], "'ZoomInIcon' is defined but never used.", "React Hook useEffect has unnecessary dependencies: 'DEFAULT_VISIBLE_SUGGESTIONS' and 'INITIAL_SUGGESTIONS_LIMIT'. Either exclude them or remove the dependency array. Outer scope values like 'INITIAL_SUGGESTIONS_LIMIT' aren't valid dependencies because mutating them doesn't re-render the component.", "ArrayExpression", ["270"], "React Hook useCallback has an unnecessary dependency: 'INITIAL_SUGGESTIONS_LIMIT'. Either exclude it or remove the dependency array. Outer scope values like 'INITIAL_SUGGESTIONS_LIMIT' aren't valid dependencies because mutating them doesn't re-render the component.", ["271"], "React Hook useCallback has a missing dependency: 'checkStatus'. Either include it or remove the dependency array.", ["272"], "The ref value 'intervalRefs.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'intervalRefs.current' to a variable inside the effect, and use that variable in the cleanup function.", "'useRef' is defined but never used.", "React Hook useEffect has a missing dependency: 'results'. Either include it or remove the dependency array.", ["273"], ["274"], "'Input' is defined but never used.", "React Hook useCallback has a missing dependency: 'loadingDetails'. Either include it or remove the dependency array.", ["275"], "'useMemo' is defined but never used.", "React Hook useCallback has a missing dependency: 'handleApiError'. Either include it or remove the dependency array.", ["276"], ["277"], "'List' is defined but never used.", "'ListItem' is defined but never used.", "'ListItemText' is defined but never used.", "'Grid' is defined but never used.", "'apiClient' is defined but never used.", {"kind": "278", "justification": "279"}, {"desc": "280", "fix": "281"}, {"desc": "280", "fix": "282"}, {"desc": "283", "fix": "284"}, {"desc": "285", "fix": "286"}, {"kind": "278", "justification": "279"}, {"desc": "287", "fix": "288"}, {"desc": "289", "fix": "290"}, {"desc": "289", "fix": "291"}, "directive", "", "Update the dependencies array to be: [selectedProduct]", {"range": "292", "text": "293"}, {"range": "294", "text": "293"}, "Update the dependencies array to be: [checkStatus]", {"range": "295", "text": "296"}, "Update the dependencies array to be: [selectedModelId, ipCategory, currentPage, fetchResults, results]", {"range": "297", "text": "298"}, "Update the dependencies array to be: [selectedModel, loadingDetails, ipCategory]", {"range": "299", "text": "300"}, "Update the dependencies array to be: [handleApiError]", {"range": "301", "text": "302"}, {"range": "303", "text": "302"}, [8132, 8205], "[selectedProduct]", [9404, 9448], [1981, 1983], "[checkStatus]", [7580, 7636], "[selectedModelId, ipCategory, currentPage, fetchResults, results]", [4626, 4653], "[selectedModel, loadingDetails, ipCategory]", [3049, 3051], "[handleApiError]", [3410, 3412]]