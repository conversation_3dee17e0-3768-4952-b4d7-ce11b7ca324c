{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents\\\\Programing\\\\TRO\\\\ModelTestsWorkbench\\\\frontend\\\\src\\\\components\\\\Layout.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Link, Outlet, useLocation } from 'react-router-dom';\nimport AppBar from '@mui/material/AppBar';\nimport Box from '@mui/material/Box';\nimport Toolbar from '@mui/material/Toolbar';\nimport Typography from '@mui/material/Typography';\nimport Tabs from '@mui/material/Tabs';\nimport Tab from '@mui/material/Tab';\nimport PlatformSwitcher from './navigation/PlatformSwitcher'; // Import PlatformSwitcher\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst modelTestWorkbenchNavItems = [{\n  label: 'Trademark',\n  path: '/trademark'\n}, {\n  label: 'Copyright',\n  path: '/copyright'\n}, {\n  label: 'Patent',\n  path: '/patent'\n}, {\n  label: 'Dashboard',\n  path: '/dashboard'\n}, {\n  label: 'Bounding Box',\n  path: '/boundingbox'\n},\n// New Bounding Box link\n{\n  label: 'Settings',\n  path: '/settings'\n}];\nconst patentVizNavItems = [{\n  label: 'Dashboard',\n  path: '/patent-viz/dashboard'\n}, {\n  label: 'Explorer',\n  path: '/patent-viz/explorer'\n}\n// Add other patent viz specific items here, e.g., Settings\n// { label: 'Settings', path: '/patent-viz/settings' },\n];\nfunction Layout() {\n  _s();\n  const location = useLocation();\n  const isPatentViz = location.pathname.startsWith('/patent-viz');\n  const headerTitle = isPatentViz ? \"Patent Visualization Platform\" : \"ModelTests Workbench\";\n  const currentNavItems = isPatentViz ? patentVizNavItems : modelTestWorkbenchNavItems;\n\n  // Find the current tab index based on the path\n  const currentTabIndex = currentNavItems.findIndex(item => location.pathname.startsWith(item.path));\n  const footerText = isPatentViz ? \"Patent Visualization Platform\" : \"ModelTests Workbench\";\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex',\n      flexDirection: 'column',\n      minHeight: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(AppBar, {\n      position: \"static\",\n      children: /*#__PURE__*/_jsxDEV(Toolbar, {\n        children: [/*#__PURE__*/_jsxDEV(PlatformSwitcher, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this), \" \", /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          component: \"div\",\n          sx: {\n            ml: 2,\n            flexGrow: 1\n          },\n          children: [\" \", headerTitle]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tabs, {\n          value: currentTabIndex === -1 ? false : currentTabIndex // Handle cases where no tab matches\n          ,\n          textColor: \"inherit\",\n          indicatorColor: \"secondary\",\n          \"aria-label\": \"navigation tabs\",\n          children: currentNavItems.map((item, index) => /*#__PURE__*/_jsxDEV(Tab, {\n            label: item.label,\n            component: Link,\n            to: item.path,\n            value: index // Use index as value for Tabs component\n          }, item.label, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      component: \"main\",\n      sx: {\n        flexGrow: 1,\n        p: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      component: \"footer\",\n      sx: {\n        p: 2,\n        mt: 'auto',\n        backgroundColor: 'grey.200'\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        color: \"text.secondary\",\n        align: \"center\",\n        children: ['© ', new Date().getFullYear(), ` ${footerText}`]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n}\n_s(Layout, \"pkHmaVRPskBaU4tMJuJJpV42k1I=\", false, function () {\n  return [useLocation];\n});\n_c = Layout;\nexport default Layout;\nvar _c;\n$RefreshReg$(_c, \"Layout\");", "map": {"version": 3, "names": ["React", "Link", "Outlet", "useLocation", "AppBar", "Box", "<PERSON><PERSON><PERSON>", "Typography", "Tabs", "Tab", "PlatformSwitcher", "jsxDEV", "_jsxDEV", "modelTestWorkbenchNavItems", "label", "path", "patentVizNavItems", "Layout", "_s", "location", "isPatentViz", "pathname", "startsWith", "headerTitle", "currentNavItems", "currentTabIndex", "findIndex", "item", "footerText", "sx", "display", "flexDirection", "minHeight", "children", "position", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "component", "ml", "flexGrow", "value", "textColor", "indicatorColor", "map", "index", "to", "p", "mt", "backgroundColor", "color", "align", "Date", "getFullYear", "_c", "$RefreshReg$"], "sources": ["D:/Documents/Programing/TRO/ModelTestsWorkbench/frontend/src/components/Layout.js"], "sourcesContent": ["import React from 'react';\r\nimport { Link, Outlet, useLocation } from 'react-router-dom';\r\nimport AppBar from '@mui/material/AppBar';\r\nimport Box from '@mui/material/Box';\r\nimport Toolbar from '@mui/material/Toolbar';\r\nimport Typography from '@mui/material/Typography';\r\nimport Tabs from '@mui/material/Tabs';\r\nimport Tab from '@mui/material/Tab';\r\nimport PlatformSwitcher from './navigation/PlatformSwitcher'; // Import PlatformSwitcher\r\n\r\nconst modelTestWorkbenchNavItems = [\r\n  { label: 'Trademark', path: '/trademark' },\r\n  { label: 'Copyright', path: '/copyright' },\r\n  { label: 'Patent', path: '/patent' },\r\n  { label: 'Dashboard', path: '/dashboard' },\r\n  { label: 'Bounding Box', path: '/boundingbox' }, // New Bounding Box link\r\n  { label: 'Settings', path: '/settings' },\r\n];\r\n\r\nconst patentVizNavItems = [\r\n  { label: 'Dashboard', path: '/patent-viz/dashboard' },\r\n  { label: 'Explorer', path: '/patent-viz/explorer' },\r\n  // Add other patent viz specific items here, e.g., Settings\r\n  // { label: 'Settings', path: '/patent-viz/settings' },\r\n];\r\n\r\nfunction Layout() {\r\n  const location = useLocation();\r\n\r\n  const isPatentViz = location.pathname.startsWith('/patent-viz');\r\n  const headerTitle = isPatentViz ? \"Patent Visualization Platform\" : \"ModelTests Workbench\";\r\n  const currentNavItems = isPatentViz ? patentVizNavItems : modelTestWorkbenchNavItems;\r\n\r\n  // Find the current tab index based on the path\r\n  const currentTabIndex = currentNavItems.findIndex(item => location.pathname.startsWith(item.path));\r\n  const footerText = isPatentViz ? \"Patent Visualization Platform\" : \"ModelTests Workbench\";\r\n\r\n  return (\r\n    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>\r\n      <AppBar position=\"static\">\r\n        <Toolbar>\r\n          <PlatformSwitcher /> {/* Add PlatformSwitcher here */}\r\n          <Typography variant=\"h6\" component=\"div\" sx={{ ml: 2, flexGrow: 1 }}> {/* Added ml for spacing */}\r\n            {headerTitle}\r\n          </Typography>\r\n          <Tabs\r\n            value={currentTabIndex === -1 ? false : currentTabIndex} // Handle cases where no tab matches\r\n            textColor=\"inherit\"\r\n            indicatorColor=\"secondary\"\r\n            aria-label=\"navigation tabs\"\r\n          >\r\n            {currentNavItems.map((item, index) => (\r\n              <Tab\r\n                key={item.label}\r\n                label={item.label}\r\n                component={Link}\r\n                to={item.path}\r\n                value={index} // Use index as value for Tabs component\r\n              />\r\n            ))}\r\n          </Tabs>\r\n        </Toolbar>\r\n      </AppBar>\r\n      <Box component=\"main\" sx={{ flexGrow: 1, p: 3 }}>\r\n        {/* Child routes will render here */}\r\n        <Outlet />\r\n      </Box>\r\n      <Box component=\"footer\" sx={{ p: 2, mt: 'auto', backgroundColor: 'grey.200' }}>\r\n        <Typography variant=\"body2\" color=\"text.secondary\" align=\"center\">\r\n          {'© '}\r\n          {new Date().getFullYear()}\r\n          {` ${footerText}`}\r\n        </Typography>\r\n      </Box>\r\n    </Box>\r\n  );\r\n}\r\n\r\nexport default Layout;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,MAAM,EAAEC,WAAW,QAAQ,kBAAkB;AAC5D,OAAOC,MAAM,MAAM,sBAAsB;AACzC,OAAOC,GAAG,MAAM,mBAAmB;AACnC,OAAOC,OAAO,MAAM,uBAAuB;AAC3C,OAAOC,UAAU,MAAM,0BAA0B;AACjD,OAAOC,IAAI,MAAM,oBAAoB;AACrC,OAAOC,GAAG,MAAM,mBAAmB;AACnC,OAAOC,gBAAgB,MAAM,+BAA+B,CAAC,CAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAE9D,MAAMC,0BAA0B,GAAG,CACjC;EAAEC,KAAK,EAAE,WAAW;EAAEC,IAAI,EAAE;AAAa,CAAC,EAC1C;EAAED,KAAK,EAAE,WAAW;EAAEC,IAAI,EAAE;AAAa,CAAC,EAC1C;EAAED,KAAK,EAAE,QAAQ;EAAEC,IAAI,EAAE;AAAU,CAAC,EACpC;EAAED,KAAK,EAAE,WAAW;EAAEC,IAAI,EAAE;AAAa,CAAC,EAC1C;EAAED,KAAK,EAAE,cAAc;EAAEC,IAAI,EAAE;AAAe,CAAC;AAAE;AACjD;EAAED,KAAK,EAAE,UAAU;EAAEC,IAAI,EAAE;AAAY,CAAC,CACzC;AAED,MAAMC,iBAAiB,GAAG,CACxB;EAAEF,KAAK,EAAE,WAAW;EAAEC,IAAI,EAAE;AAAwB,CAAC,EACrD;EAAED,KAAK,EAAE,UAAU;EAAEC,IAAI,EAAE;AAAuB;AAClD;AACA;AAAA,CACD;AAED,SAASE,MAAMA,CAAA,EAAG;EAAAC,EAAA;EAChB,MAAMC,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAE9B,MAAMiB,WAAW,GAAGD,QAAQ,CAACE,QAAQ,CAACC,UAAU,CAAC,aAAa,CAAC;EAC/D,MAAMC,WAAW,GAAGH,WAAW,GAAG,+BAA+B,GAAG,sBAAsB;EAC1F,MAAMI,eAAe,GAAGJ,WAAW,GAAGJ,iBAAiB,GAAGH,0BAA0B;;EAEpF;EACA,MAAMY,eAAe,GAAGD,eAAe,CAACE,SAAS,CAACC,IAAI,IAAIR,QAAQ,CAACE,QAAQ,CAACC,UAAU,CAACK,IAAI,CAACZ,IAAI,CAAC,CAAC;EAClG,MAAMa,UAAU,GAAGR,WAAW,GAAG,+BAA+B,GAAG,sBAAsB;EAEzF,oBACER,OAAA,CAACP,GAAG;IAACwB,EAAE,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE,QAAQ;MAAEC,SAAS,EAAE;IAAQ,CAAE;IAAAC,QAAA,gBACxErB,OAAA,CAACR,MAAM;MAAC8B,QAAQ,EAAC,QAAQ;MAAAD,QAAA,eACvBrB,OAAA,CAACN,OAAO;QAAA2B,QAAA,gBACNrB,OAAA,CAACF,gBAAgB;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,KAAC,eACrB1B,OAAA,CAACL,UAAU;UAACgC,OAAO,EAAC,IAAI;UAACC,SAAS,EAAC,KAAK;UAACX,EAAE,EAAE;YAAEY,EAAE,EAAE,CAAC;YAAEC,QAAQ,EAAE;UAAE,CAAE;UAAAT,QAAA,GAAC,GAAC,EACnEV,WAAW;QAAA;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACb1B,OAAA,CAACJ,IAAI;UACHmC,KAAK,EAAElB,eAAe,KAAK,CAAC,CAAC,GAAG,KAAK,GAAGA,eAAgB,CAAC;UAAA;UACzDmB,SAAS,EAAC,SAAS;UACnBC,cAAc,EAAC,WAAW;UAC1B,cAAW,iBAAiB;UAAAZ,QAAA,EAE3BT,eAAe,CAACsB,GAAG,CAAC,CAACnB,IAAI,EAAEoB,KAAK,kBAC/BnC,OAAA,CAACH,GAAG;YAEFK,KAAK,EAAEa,IAAI,CAACb,KAAM;YAClB0B,SAAS,EAAEvC,IAAK;YAChB+C,EAAE,EAAErB,IAAI,CAACZ,IAAK;YACd4B,KAAK,EAAEI,KAAM,CAAC;UAAA,GAJTpB,IAAI,CAACb,KAAK;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKhB,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eACT1B,OAAA,CAACP,GAAG;MAACmC,SAAS,EAAC,MAAM;MAACX,EAAE,EAAE;QAAEa,QAAQ,EAAE,CAAC;QAAEO,CAAC,EAAE;MAAE,CAAE;MAAAhB,QAAA,eAE9CrB,OAAA,CAACV,MAAM;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eACN1B,OAAA,CAACP,GAAG;MAACmC,SAAS,EAAC,QAAQ;MAACX,EAAE,EAAE;QAAEoB,CAAC,EAAE,CAAC;QAAEC,EAAE,EAAE,MAAM;QAAEC,eAAe,EAAE;MAAW,CAAE;MAAAlB,QAAA,eAC5ErB,OAAA,CAACL,UAAU;QAACgC,OAAO,EAAC,OAAO;QAACa,KAAK,EAAC,gBAAgB;QAACC,KAAK,EAAC,QAAQ;QAAApB,QAAA,GAC9D,IAAI,EACJ,IAAIqB,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,EACxB,IAAI3B,UAAU,EAAE;MAAA;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACpB,EAAA,CAlDQD,MAAM;EAAA,QACId,WAAW;AAAA;AAAAqD,EAAA,GADrBvC,MAAM;AAoDf,eAAeA,MAAM;AAAC,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}