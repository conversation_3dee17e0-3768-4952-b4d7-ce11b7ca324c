import React, { useState, useEffect, useCallback } from 'react';
import {
  Container,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Button,
  CircularProgress,
  Alert,
  Grid,
  Card,
  CardContent,
  TextField,
  Pagination,
  Tooltip,
  IconButton,
  Modal, // Added for New Experiment Modal
  Paper, // Added for Modal content styling
  Stack // Added for Modal form layout
} from '@mui/material';
import { getBbPictures, getBbExperiments, updateBbResultScore, getBbExperimentConfigurations, createBbExperiment } from '../../services/api_bounding_box';
import SaveIcon from '@mui/icons-material/Save';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline'; // For New Experiment button

// Modal style
const modalStyle = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: '90%',
  maxWidth: 600,
  bgcolor: 'background.paper',
  border: '2px solid #000',
  boxShadow: 24,
  p: 4,
};

const OUTPUT_TYPES = ["Bounding Box", "Bounding Box + Segmentation Mask"];


const ResultsPage = () => {
  const [pictures, setPictures] = useState([]);
  const [selectedPictureId, setSelectedPictureId] = useState('');
  const [experiments, setExperiments] = useState([]);
  const [isLoadingPictures, setIsLoadingPictures] = useState(false);
  const [isLoadingExperiments, setIsLoadingExperiments] = useState(false);
  const [error, setError] = useState(null);

  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [totalExperiments, setTotalExperiments] = useState(0);

  const [experimentScores, setExperimentScores] = useState({});
  const [scoreErrors, setScoreErrors] = useState({});

  // State for New Experiment Modal
  const [isNewExperimentModalOpen, setIsNewExperimentModalOpen] = useState(false);
  const [experimentConfigurations, setExperimentConfigurations] = useState([]);
  const [selectedConfiguration, setSelectedConfiguration] = useState(null); // Store the whole config object

  const [newExperimentPrompt, setNewExperimentPrompt] = useState('');
  const [newExperimentResizeHeight, setNewExperimentResizeHeight] = useState(512);
  const [newExperimentResizeWidth, setNewExperimentResizeWidth] = useState(512);
  const [newExperimentOutputType, setNewExperimentOutputType] = useState(OUTPUT_TYPES[0]);
  const [isCreatingExperiment, setIsCreatingExperiment] = useState(false);
  const [newExperimentError, setNewExperimentError] = useState('');


  const fetchPictures = useCallback(() => {
    setIsLoadingPictures(true);
    setError(null);
    getBbPictures()
      .then(data => {
        // getBbPictures returns an object with a 'pictures' property
        if (data && Array.isArray(data.pictures)) {
          setPictures(data.pictures);
        } else if (Array.isArray(data)) {
          // Fallback in case the API returns a direct array
          setPictures(data);
        } else {
          console.warn('getBbPictures returned unexpected data format:', data);
          setPictures([]);
        }
      })
      .catch(err => {
        console.error('Failed to load pictures:', err);
        setError('Failed to load pictures.');
        setPictures([]); // Ensure pictures is always an array
      })
      .finally(() => setIsLoadingPictures(false));
  }, []);

  useEffect(() => {
    fetchPictures();
  }, [fetchPictures]);

  const fetchExperiments = useCallback((pictureId, page) => {
    if (!pictureId) {
        setExperiments([]); setTotalPages(0); setTotalExperiments(0); return;
    };
    setIsLoadingExperiments(true); setError(null);
    getBbExperiments(pictureId, page)
      .then(data => {
        setExperiments(data.experiments || []);
        setTotalPages(data.totalPages || 0);
        setCurrentPage(data.currentPage || 1);
        setTotalExperiments(data.totalExperiments || 0);
        const initialScores = {};
        (data.experiments || []).forEach(exp => (exp.results || []).forEach(res => {
          if (res.score !== null && res.score !== undefined) initialScores[res.id] = res.score;
        }));
        setExperimentScores(prev => ({...prev, ...initialScores}));
      })
      .catch(err => {
        setError(`Failed to load experiments.`);
        setExperiments([]); setTotalPages(0); setTotalExperiments(0);
      })
      .finally(() => setIsLoadingExperiments(false));
  }, []);

  useEffect(() => {
    if (selectedPictureId) fetchExperiments(selectedPictureId, currentPage);
    else { setExperiments([]); setTotalPages(0); setTotalExperiments(0); }
  }, [selectedPictureId, currentPage, fetchExperiments]);

  const fetchExpConfigs = useCallback(async () => {
    try {
        const configs = await getBbExperimentConfigurations();
        setExperimentConfigurations(configs || []);
    } catch (err) {
        console.error("Failed to fetch experiment configurations:", err);
        // Optionally set an error state for config loading
    }
  }, []);

  useEffect(() => {
    // Fetch configurations when the page loads or modal is about to open
    // For simplicity, fetching on page load for now.
    fetchExpConfigs();
  }, [fetchExpConfigs]);


  const handlePictureChange = (event) => {
    setSelectedPictureId(event.target.value);
    setCurrentPage(1); setError(null); setExperimentScores({}); setScoreErrors({});
  };

  const handlePageChange = (event, value) => setCurrentPage(value);

  const handleScoreChange = (resultId, value) => {
    const numValue = value === '' ? '' : Number(value);
    setExperimentScores(prev => ({ ...prev, [resultId]: numValue }));
    if (numValue === '' || (Number.isInteger(numValue) && numValue >= 0 && numValue <= 10)) {
      setScoreErrors(prev => ({ ...prev, [resultId]: '' }));
    } else {
      setScoreErrors(prev => ({ ...prev, [resultId]: 'Score must be 0-10 (integer)' }));
    }
  };

  const handleSaveScore = async (resultId) => {
    const score = experimentScores[resultId];
    if (score === undefined || score === '' || scoreErrors[resultId]) {
      alert('Invalid score.'); return;
    }
    try {
      await updateBbResultScore(resultId, Number(score));
      alert(`Score for result ${resultId} saved (mocked).`);
      // Consider optimistic update or refetch experiment for this result
    } catch (error) { alert(`Failed to save score for result ${resultId}.`); }
  };

  const formatDate = (isoString) => isoString ? new Date(isoString).toLocaleString() : 'N/A';

  // New Experiment Modal Handlers
  const handleOpenNewExperimentModal = () => {
    setNewExperimentError('');
    setSelectedConfiguration(null); // Reset selected config
    // Reset form fields to default or last selected (if desired)
    setNewExperimentPrompt('');
    setNewExperimentResizeHeight(512);
    setNewExperimentResizeWidth(512);
    setNewExperimentOutputType(OUTPUT_TYPES[0]);
    setIsNewExperimentModalOpen(true);
    if(experimentConfigurations.length === 0) fetchExpConfigs(); // Fetch if not already loaded
  };

  const handleCloseNewExperimentModal = () => setIsNewExperimentModalOpen(false);

  const handleSelectedConfigurationChange = (event) => {
    const configIndex = event.target.value; // This will be the index
    if (configIndex === "" || configIndex === undefined) {
        setSelectedConfiguration(null);
        setNewExperimentPrompt('');
        setNewExperimentResizeHeight(512);
        setNewExperimentResizeWidth(512);
        setNewExperimentOutputType(OUTPUT_TYPES[0]);
    } else {
        const config = experimentConfigurations[configIndex];
        setSelectedConfiguration(config); // Store the whole config object
        setNewExperimentPrompt(config.prompt);
        setNewExperimentResizeHeight(config.resize_height);
        setNewExperimentResizeWidth(config.resize_width);
        setNewExperimentOutputType(config.output_type);
    }
  };

  const handleCreateNewExperiment = async () => {
    if (!newExperimentPrompt.trim()) {
      setNewExperimentError("Prompt cannot be empty."); return;
    }
    if (newExperimentResizeHeight <= 0 || newExperimentResizeWidth <= 0) {
      setNewExperimentError("Resize dimensions must be positive."); return;
    }
    setIsCreatingExperiment(true); setNewExperimentError('');
    try {
      const experimentData = {
        picture_id: selectedPictureId,
        prompt: newExperimentPrompt,
        resize_height: parseInt(newExperimentResizeHeight, 10),
        resize_width: parseInt(newExperimentResizeWidth, 10),
        output_type: newExperimentOutputType,
      };
      const newExp = await createBbExperiment(experimentData);
      // Add to the start of the experiments list for immediate feedback
      setExperiments(prev => [newExp, ...prev]);
      setTotalExperiments(prev => prev + 1);
      // If new experiment pushes list to new page, this won't show it unless we adjust current page.
      // For simplicity, we'll just add it. A full refresh might be better.
      // Or, if on page 1 and list is not full, it appears.
      // fetchExperiments(selectedPictureId, currentPage); // Or refetch current page.
      handleCloseNewExperimentModal();
    } catch (err) {
      setNewExperimentError(`Failed to create experiment: ${err.message || 'Unknown error'}`);
    } finally {
      setIsCreatingExperiment(false);
    }
  };

  return (
    <Container maxWidth="xl" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" gutterBottom component="h1">Results Analysis by Product Pictures</Typography>
      {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
      <FormControl fullWidth sx={{ mb: 3 }}>
        <InputLabel id="product-image-select-label">Select Product Image</InputLabel>
        <Select labelId="product-image-select-label" value={selectedPictureId} label="Select Product Image" onChange={handlePictureChange} disabled={isLoadingPictures || pictures.length === 0}>
          {isLoadingPictures && <MenuItem value="" disabled><em>Loading images...</em></MenuItem>}
          {!isLoadingPictures && pictures.length === 0 && <MenuItem value="" disabled><em>No pictures available.</em></MenuItem>}
          {Array.isArray(pictures) && pictures.map((pic) => (<MenuItem key={pic.id} value={pic.id}>{pic.name} (ID: {pic.id})</MenuItem>))}
        </Select>
      </FormControl>

      {!isLoadingPictures && pictures.length === 0 && !error && (<Typography variant="body1" sx={{ mb: 2, textAlign: 'center' }}>No product pictures found.</Typography>)}

      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
        <Button variant="contained" color="primary" startIcon={<AddCircleOutlineIcon />} onClick={handleOpenNewExperimentModal} disabled={!selectedPictureId || isLoadingPictures || isLoadingExperiments}>
          + New Experiment
        </Button>
      </Box>

      {/* New Experiment Modal */}
      <Modal open={isNewExperimentModalOpen} onClose={handleCloseNewExperimentModal} aria-labelledby="new-experiment-modal-title">
        <Paper sx={modalStyle}>
          <Typography id="new-experiment-modal-title" variant="h6" component="h2" gutterBottom>Create New Experiment</Typography>
          {newExperimentError && <Alert severity="error" sx={{mb:2}}>{newExperimentError}</Alert>}
          <Stack spacing={2}>
            <FormControl fullWidth>
              <InputLabel id="select-config-label">Select Existing Configuration (Optional)</InputLabel>
              <Select
                labelId="select-config-label"
                value={selectedConfiguration ? experimentConfigurations.indexOf(selectedConfiguration) : ""}
                label="Select Existing Configuration (Optional)"
                onChange={handleSelectedConfigurationChange}
              >
                <MenuItem value=""><em>None - Manual Input</em></MenuItem>
                {experimentConfigurations.map((config, index) => (
                  <MenuItem key={index} value={index}>
                    Prompt: {config.prompt.substring(0,30)}... | Size: {config.resize_width}x{config.resize_height} | Type: {config.output_type}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <TextField label="Prompt" value={newExperimentPrompt} onChange={(e) => setNewExperimentPrompt(e.target.value)} fullWidth required />
            <Grid container spacing={2}>
                <Grid item xs={6}>
                    <TextField label="Resize Height" type="number" value={newExperimentResizeHeight} onChange={(e) => setNewExperimentResizeHeight(Number(e.target.value))} fullWidth required />
                </Grid>
                <Grid item xs={6}>
                    <TextField label="Resize Width" type="number" value={newExperimentResizeWidth} onChange={(e) => setNewExperimentResizeWidth(Number(e.target.value))} fullWidth required />
                </Grid>
            </Grid>
            <FormControl fullWidth required>
                <InputLabel id="output-type-label">Output Type</InputLabel>
                <Select labelId="output-type-label" value={newExperimentOutputType} label="Output Type" onChange={(e) => setNewExperimentOutputType(e.target.value)}>
                    {OUTPUT_TYPES.map(type => (<MenuItem key={type} value={type}>{type}</MenuItem>))}
                </Select>
            </FormControl>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1, mt: 2 }}>
              <Button onClick={handleCloseNewExperimentModal} disabled={isCreatingExperiment}>Cancel</Button>
              <Button variant="contained" onClick={handleCreateNewExperiment} disabled={isCreatingExperiment || !newExperimentPrompt.trim() || newExperimentResizeHeight <=0 || newExperimentResizeWidth <=0}>
                {isCreatingExperiment ? <CircularProgress size={24} /> : "Add"}
              </Button>
            </Box>
          </Stack>
        </Paper>
      </Modal>

      {/* Experiment List */}
      {isLoadingExperiments && <Box sx={{display: 'flex', justifyContent: 'center', my: 3}}><CircularProgress /></Box>}
      {!isLoadingExperiments && selectedPictureId && experiments.length === 0 && !error && (<Typography variant="body1" sx={{ textAlign: 'center', mt: 2 }}>No experiments found. Create one!</Typography>)}

      {experiments.map((exp, index) => (
        <Card key={exp.id} sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Experiment {totalExperiments - ((currentPage - 1) * 2) - index} (ID: {exp.id.substring(0,8)}...)
              <Typography variant="caption" sx={{ml: 1}}>Created: {formatDate(exp.created_at)}</Typography>
            </Typography>
            <Box sx={{ display: 'flex', gap: 3, mb: 2, flexWrap: 'wrap' }}>
              <Typography variant="body2"><strong>Prompt:</strong> {exp.prompt}</Typography>
              <Typography variant="body2"><strong>Dimensions:</strong> {exp.resize_width}w x {exp.resize_height}h</Typography>
              <Typography variant="body2"><strong>Output Type:</strong> {exp.output_type}</Typography>
            </Box>
            <Typography variant="subtitle1" sx={{mt: 2, mb: 1}}>Model Outputs:</Typography>
            <Grid container spacing={2}>
              {(exp.results || []).map((result) => (
                <Grid item xs={12} sm={6} md={4} key={result.id}>
                  <Paper elevation={2} sx={{ p: 2, height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
                    <Typography variant="subtitle2" gutterBottom>{result.model_name}</Typography>
                    <Box sx={{ flexGrow: 1, display: 'flex', flexDirection:'column', alignItems: 'center', justifyContent: 'center', minHeight: 180, mb:1 }}>
                      {result.status === 'success' && result.output_image_path && (<img src={result.output_image_path} alt={`Output for ${result.model_name}`} style={{ maxWidth: '100%', maxHeight: '150px', objectFit: 'contain' }} />)}
                      {result.status === 'success' && !result.output_image_path && (<Typography variant="caption" color="textSecondary">Image path missing</Typography>)}
                      {result.status === 'failed' && (<Box sx={{textAlign: 'center'}}><ErrorOutlineIcon color="error" sx={{ fontSize: 40 }} /><Typography variant="body2" color="error">Failed</Typography>{result.error_message && <Tooltip title={result.error_message}><Typography variant="caption" sx={{cursor: 'help'}}><i>Details</i></Typography></Tooltip>}</Box>)}
                      {result.status === 'processing' && (<Box sx={{textAlign: 'center'}}><CircularProgress size={30} /><Typography variant="body2" sx={{mt:1}}>Processing...</Typography></Box>)}
                    </Box>
                    {result.status === 'success' && (
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 'auto' }}>
                        <TextField label="Score (0-10)" type="number" variant="outlined" size="small" value={experimentScores[result.id] === undefined ? '' : experimentScores[result.id]} onChange={(e) => handleScoreChange(result.id, e.target.value)} error={!!scoreErrors[result.id]} helperText={scoreErrors[result.id]} inputProps={{ min: 0, max: 10, step: 1 }} sx={{ flexGrow: 1 }}/>
                        <Tooltip title="Save Score"><span><IconButton onClick={() => handleSaveScore(result.id)} disabled={!!scoreErrors[result.id] || experimentScores[result.id] === undefined || String(experimentScores[result.id]).trim() === ''} color="primary"><SaveIcon /></IconButton></span></Tooltip>
                      </Box>
                    )}
                  </Paper>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>
      ))}

      {totalPages > 1 && (<Box sx={{ display: 'flex', justifyContent: 'center', mt: 3, mb: 2 }}><Pagination count={totalPages} page={currentPage} onChange={handlePageChange} color="primary" disabled={isLoadingExperiments}/></Box>)}
    </Container>
  );
};

export default ResultsPage;
