{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents\\\\Programing\\\\TRO\\\\ModelTestsWorkbench\\\\frontend\\\\src\\\\pages\\\\patent-viz\\\\PatentExplorerPage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { Container, Typography, CircularProgress, Alert } from '@mui/material';\nimport { getPatentsForExploration, getPatentDetails, getPatentImagesInfo } from '../../services/api_patent_viz';\nimport { DayPicker } from \"react-day-picker\"; // Added\nimport \"react-day-picker/style.css\"; // Added\n\n// Import sub-components\nimport FilterControls from '../../components/patent-viz/FilterControls';\nimport QuickStatsDisplay from '../../components/patent-viz/QuickStatsDisplay';\nimport PatentTable from '../../components/patent-viz/PatentTable';\nimport PaginationControls from '../../components/patent-viz/PaginationControls';\nimport PatentDetailModal from '../../components/patent-viz/PatentDetailModal';\nimport PatentImageModal from '../../components/patent-viz/PatentImageModal';\n\n// TDD: TEST: PatentExplorerPage fetches patent list on mount and when filters change\n// TDD: TEST: PatentExplorerPage displays loading/error states for patent list\n// TDD: TEST: PatentExplorerPage renders FilterControls and PatentTable\n// TDD: TEST: PatentExplorerPage handles pagination and sorting correctly\n// TDD: TEST: PatentExplorerPage opens/closes PatentDetailModal and fetches data\n// TDD: TEST: PatentExplorerPage opens/closes PatentImageModal and fetches image paths\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst INITIAL_FILTERS = {\n  document_id_search: \"\",\n  patent_title_search: \"\",\n  abstract_search: \"\",\n  date_published_range: {\n    from: null,\n    to: null\n  },\n  // Changed for react-day-picker\n  patent_types: [],\n  tro_status: \"All\",\n  inventors_search: \"\",\n  assignee_search: \"\",\n  applicant_search: \"\",\n  uspc_class_search: \"\",\n  loc_code_search: \"\",\n  cpc_class_search: \"\",\n  page: 1,\n  per_page: 25,\n  sort_by: \"date_published\",\n  sort_dir: \"desc\",\n  selected_columns: [\"document_id\", \"patent_title\", \"date_published\", \"patent_type\", \"tro\"] // Default visible\n};\n\n// Define based on patents_records table from schema\nconst ALL_AVAILABLE_COLUMNS = [{\n  field: \"id\",\n  headerName: \"ID\",\n  alwaysVisible: false,\n  defaultHidden: true\n},\n// Internal ID, usually not shown\n{\n  field: \"document_id\",\n  headerName: \"Document ID\",\n  alwaysVisible: true\n}, {\n  field: \"patent_title\",\n  headerName: \"Patent Title\",\n  alwaysVisible: true\n}, {\n  field: \"date_published\",\n  headerName: \"Date Published\"\n}, {\n  field: \"patent_type\",\n  headerName: \"Patent Type\"\n}, {\n  field: \"tro\",\n  headerName: \"TRO\"\n}, {\n  field: \"abstract\",\n  headerName: \"Abstract\"\n}, {\n  field: \"inventors\",\n  headerName: \"Inventors\"\n},\n// Array, might need formatting\n{\n  field: \"assignee\",\n  headerName: \"Assignee\"\n}, {\n  field: \"applicant\",\n  headerName: \"Applicant\"\n}, {\n  field: \"uspc_class\",\n  headerName: \"USPC Class\"\n},\n// Array, might need formatting\n{\n  field: \"loc_code\",\n  headerName: \"LOC Code\"\n},\n// Array, might need formatting\n{\n  field: \"cpc_class\",\n  headerName: \"CPC Class\"\n},\n// Array, might need formatting\n{\n  field: \"reg_no\",\n  headerName: \"Registration No.\",\n  defaultHidden: true\n}, {\n  field: \"application_number\",\n  headerName: \"Application No.\",\n  defaultHidden: true\n}, {\n  field: \"date_filed\",\n  headerName: \"Date Filed\",\n  defaultHidden: true\n}, {\n  field: \"claims_count\",\n  headerName: \"Claims Count\",\n  defaultHidden: true\n}, {\n  field: \"independent_claims_count\",\n  headerName: \"Independent Claims\",\n  defaultHidden: true\n}\n// Add other fields as needed, e.g., foreign_priority, related_applications\n];\nfunction PatentExplorerPage() {\n  _s();\n  const [filters, setFilters] = useState(INITIAL_FILTERS);\n  const [patentsData, setPatentsData] = useState({\n    patents: [],\n    pagination: {},\n    quick_stats: {}\n  });\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);\n  const [selectedPatentForDetail, setSelectedPatentForDetail] = useState(null);\n  const [isDetailLoading, setIsDetailLoading] = useState(false);\n  const [detailError, setDetailError] = useState(null);\n  const [isImageModalOpen, setIsImageModalOpen] = useState(false);\n  const [selectedPatentForImages, setSelectedPatentForImages] = useState(null);\n  const [imagePathsData, setImagePathsData] = useState({\n    image_paths: [],\n    patent_reg_no: \"\",\n    base_image_folder_path: \"\"\n  });\n  const [isImagesLoading, setIsImagesLoading] = useState(false);\n  const [imagesError, setImagesError] = useState(null);\n  const [availableColumns] = useState(ALL_AVAILABLE_COLUMNS);\n  const fetchPatents = useCallback(async () => {\n    setIsLoading(true);\n    setError(null);\n    try {\n      var _filters$date_publish, _filters$date_publish2;\n      // TDD: TEST: API_CLIENT.get for patent list is called with correct filter params\n      const apiFilters = {\n        ...filters,\n        date_published_start: (_filters$date_publish = filters.date_published_range) === null || _filters$date_publish === void 0 ? void 0 : _filters$date_publish.from,\n        date_published_end: (_filters$date_publish2 = filters.date_published_range) === null || _filters$date_publish2 === void 0 ? void 0 : _filters$date_publish2.to\n      };\n      delete apiFilters.date_published_range; // Clean up the temporary range object\n\n      const response = await getPatentsForExploration(apiFilters);\n      setPatentsData(response.data);\n    } catch (apiError) {\n      setError(apiError.message || 'Failed to fetch patents');\n      setPatentsData({\n        patents: [],\n        pagination: {},\n        quick_stats: {}\n      }); // Reset data on error\n    } finally {\n      setIsLoading(false);\n    }\n  }, [filters]);\n  useEffect(() => {\n    fetchPatents();\n  }, [fetchPatents]);\n  const handleFilterChange = useCallback(newFilterValues => {\n    setFilters(prevFilters => ({\n      ...prevFilters,\n      ...newFilterValues,\n      page: 1 // Reset to page 1 on filter change\n    }));\n  }, []);\n  const handlePageChange = useCallback(newPage => {\n    setFilters(prevFilters => ({\n      ...prevFilters,\n      page: newPage\n    }));\n  }, []);\n  const handlePerPageChange = useCallback(newPerPage => {\n    setFilters(prevFilters => ({\n      ...prevFilters,\n      per_page: newPerPage,\n      page: 1 // Reset to page 1\n    }));\n  }, []);\n  const handleSortChange = useCallback((sortByField, sortDirection) => {\n    setFilters(prevFilters => ({\n      ...prevFilters,\n      sort_by: sortByField,\n      sort_dir: sortDirection\n    }));\n  }, []);\n  const handleColumnSelectionChange = useCallback(newlySelectedColumnsFieldsArray => {\n    setFilters(prevFilters => ({\n      ...prevFilters,\n      selected_columns: newlySelectedColumnsFieldsArray\n    }));\n  }, []);\n  const openDetailModal = useCallback(async patentIdForDetail => {\n    setIsDetailModalOpen(true);\n    setIsDetailLoading(true);\n    setDetailError(null);\n    setSelectedPatentForDetail(null); // Clear previous data\n    try {\n      // TDD: TEST: API_CLIENT.get for patent detail is called with correct patent_id\n      const response = await getPatentDetails(patentIdForDetail);\n      setSelectedPatentForDetail(response.data);\n    } catch (apiError) {\n      setDetailError(apiError.message || 'Failed to fetch patent details');\n    } finally {\n      setIsDetailLoading(false);\n    }\n  }, []);\n  const closeDetailModal = useCallback(() => {\n    setIsDetailModalOpen(false);\n    setSelectedPatentForDetail(null);\n  }, []);\n  const openImageModal = useCallback(async patentForImages => {\n    // patentForImages is {id, reg_no, patent_title}\n    setSelectedPatentForImages(patentForImages);\n    setIsImageModalOpen(true);\n    setIsImagesLoading(true);\n    setImagesError(null);\n    setImagePathsData({\n      image_paths: [],\n      patent_reg_no: \"\",\n      base_image_folder_path: \"\"\n    }); // Clear previous\n    try {\n      // TDD: TEST: API_CLIENT.get for patent images is called with correct patent_id\n      const response = await getPatentImagesInfo(patentForImages.id);\n      setImagePathsData(response.data);\n    } catch (apiError) {\n      setImagesError(apiError.message || 'Failed to fetch patent images');\n    } finally {\n      setIsImagesLoading(false);\n    }\n  }, []);\n  const closeImageModal = useCallback(() => {\n    setIsImageModalOpen(false);\n    setSelectedPatentForImages(null);\n  }, []);\n  const displayedColumnsConfig = React.useMemo(() => {\n    return filters.selected_columns.map(field => availableColumns.find(col => col.field === field)).filter(Boolean); // Ensure only valid columns are passed\n  }, [filters.selected_columns, availableColumns]);\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"xl\",\n    sx: {\n      mt: 4,\n      mb: 4\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      component: \"h1\",\n      gutterBottom: true,\n      children: \"Patent Explorer\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FilterControls, {\n      current_filters: filters,\n      on_filter_change: handleFilterChange,\n      available_columns: availableColumns,\n      on_column_selection_change: handleColumnSelectionChange\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 211,\n      columnNumber: 7\n    }, this), patentsData.quick_stats && patentsData.quick_stats.total_filtered_results != null && /*#__PURE__*/_jsxDEV(QuickStatsDisplay, {\n      stats: patentsData.quick_stats\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 9\n    }, this), isLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n      sx: {\n        display: 'block',\n        margin: '20px auto'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 9\n    }, this) : error ? /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mt: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 9\n    }, this) : patentsData.patents && patentsData.patents.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(PatentTable, {\n        patents: patentsData.patents,\n        displayed_columns: displayedColumnsConfig,\n        on_row_id_click: openDetailModal,\n        on_row_title_click: openImageModal,\n        current_sort_by: filters.sort_by,\n        current_sort_dir: filters.sort_dir,\n        on_sort_change: handleSortChange\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(PaginationControls, {\n        current_page: filters.page,\n        items_per_page: filters.per_page,\n        total_items: patentsData.pagination.total_items || 0,\n        on_page_change: handlePageChange,\n        on_per_page_change: handlePerPageChange\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true) : /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      sx: {\n        mt: 2\n      },\n      children: \"No patents found matching your criteria.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(PatentDetailModal, {\n      is_open: isDetailModalOpen,\n      on_close: closeDetailModal,\n      patent_data: selectedPatentForDetail,\n      is_loading: isDetailLoading,\n      error: detailError\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 253,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PatentImageModal, {\n      is_open: isImageModalOpen,\n      on_close: closeImageModal,\n      patent_title: selectedPatentForImages ? selectedPatentForImages.patent_title : \"\",\n      image_data: imagePathsData,\n      is_loading: isImagesLoading,\n      error: imagesError\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 205,\n    columnNumber: 5\n  }, this);\n}\n_s(PatentExplorerPage, \"ej1DrTyqugk4RQ3zIsOeEwru9b0=\");\n_c = PatentExplorerPage;\nexport default PatentExplorerPage;\nvar _c;\n$RefreshReg$(_c, \"PatentExplorerPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Container", "Typography", "CircularProgress", "<PERSON><PERSON>", "getPatentsForExploration", "getPatentDetails", "getPatentImagesInfo", "DayPicker", "FilterControls", "QuickStatsDisplay", "PatentTable", "PaginationControls", "PatentDetailModal", "PatentImageModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "INITIAL_FILTERS", "document_id_search", "patent_title_search", "abstract_search", "date_published_range", "from", "to", "patent_types", "tro_status", "inventors_search", "assignee_search", "applicant_search", "uspc_class_search", "loc_code_search", "cpc_class_search", "page", "per_page", "sort_by", "sort_dir", "selected_columns", "ALL_AVAILABLE_COLUMNS", "field", "headerName", "alwaysVisible", "defaultHidden", "PatentExplorerPage", "_s", "filters", "setFilters", "patentsData", "setPatentsData", "patents", "pagination", "quick_stats", "isLoading", "setIsLoading", "error", "setError", "isDetailModalOpen", "setIsDetailModalOpen", "selectedPatentForDetail", "setSelectedPatentForDetail", "isDetailLoading", "setIsDetailLoading", "detailError", "setDetailError", "isImageModalOpen", "setIsImageModalOpen", "selectedPatentForImages", "setSelectedPatentForImages", "imagePathsData", "setImagePathsData", "image_paths", "patent_reg_no", "base_image_folder_path", "isImagesLoading", "setIsImagesLoading", "imagesError", "setImagesError", "availableColumns", "fetchPatents", "_filters$date_publish", "_filters$date_publish2", "apiFilters", "date_published_start", "date_published_end", "response", "data", "apiError", "message", "handleFilterChange", "newFilter<PERSON><PERSON><PERSON>", "prevFilters", "handlePageChange", "newPage", "handlePerPageChange", "newPerPage", "handleSortChange", "sortByField", "sortDirection", "handleColumnSelectionChange", "newlySelectedColumnsFieldsArray", "openDetailModal", "patentIdForDetail", "closeDetailModal", "openImageModal", "patentForImages", "id", "closeImageModal", "displayedColumnsConfig", "useMemo", "map", "find", "col", "filter", "Boolean", "max<PERSON><PERSON><PERSON>", "sx", "mt", "mb", "children", "variant", "component", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "current_filters", "on_filter_change", "available_columns", "on_column_selection_change", "total_filtered_results", "stats", "display", "margin", "severity", "length", "displayed_columns", "on_row_id_click", "on_row_title_click", "current_sort_by", "current_sort_dir", "on_sort_change", "current_page", "items_per_page", "total_items", "on_page_change", "on_per_page_change", "is_open", "on_close", "patent_data", "is_loading", "patent_title", "image_data", "_c", "$RefreshReg$"], "sources": ["D:/Documents/Programing/TRO/ModelTestsWorkbench/frontend/src/pages/patent-viz/PatentExplorerPage.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\r\nimport {\r\n  Container,\r\n  Typography,\r\n  CircularProgress,\r\n  Alert,\r\n} from '@mui/material';\r\nimport {\r\n  getPatentsForExploration,\r\n  getPatentDetails,\r\n  getPatentImagesInfo,\r\n} from '../../services/api_patent_viz';\r\nimport { DayPicker } from \"react-day-picker\"; // Added\r\nimport \"react-day-picker/style.css\"; // Added\r\n\r\n// Import sub-components\r\nimport FilterControls from '../../components/patent-viz/FilterControls';\r\nimport QuickStatsDisplay from '../../components/patent-viz/QuickStatsDisplay';\r\nimport PatentTable from '../../components/patent-viz/PatentTable';\r\nimport PaginationControls from '../../components/patent-viz/PaginationControls';\r\nimport PatentDetailModal from '../../components/patent-viz/PatentDetailModal';\r\nimport PatentImageModal from '../../components/patent-viz/PatentImageModal';\r\n\r\n// TDD: TEST: PatentExplorerPage fetches patent list on mount and when filters change\r\n// TDD: TEST: PatentExplorerPage displays loading/error states for patent list\r\n// TDD: TEST: PatentExplorerPage renders FilterControls and PatentTable\r\n// TDD: TEST: PatentExplorerPage handles pagination and sorting correctly\r\n// TDD: TEST: PatentExplorerPage opens/closes PatentDetailModal and fetches data\r\n// TDD: TEST: PatentExplorerPage opens/closes PatentImageModal and fetches image paths\r\n\r\nconst INITIAL_FILTERS = {\r\n  document_id_search: \"\",\r\n  patent_title_search: \"\",\r\n  abstract_search: \"\",\r\n  date_published_range: { from: null, to: null }, // Changed for react-day-picker\r\n  patent_types: [],\r\n  tro_status: \"All\",\r\n  inventors_search: \"\",\r\n  assignee_search: \"\",\r\n  applicant_search: \"\",\r\n  uspc_class_search: \"\",\r\n  loc_code_search: \"\",\r\n  cpc_class_search: \"\",\r\n  page: 1,\r\n  per_page: 25,\r\n  sort_by: \"date_published\",\r\n  sort_dir: \"desc\",\r\n  selected_columns: [\"document_id\", \"patent_title\", \"date_published\", \"patent_type\", \"tro\"], // Default visible\r\n};\r\n\r\n// Define based on patents_records table from schema\r\nconst ALL_AVAILABLE_COLUMNS = [\r\n  { field: \"id\", headerName: \"ID\", alwaysVisible: false, defaultHidden: true }, // Internal ID, usually not shown\r\n  { field: \"document_id\", headerName: \"Document ID\", alwaysVisible: true },\r\n  { field: \"patent_title\", headerName: \"Patent Title\", alwaysVisible: true },\r\n  { field: \"date_published\", headerName: \"Date Published\" },\r\n  { field: \"patent_type\", headerName: \"Patent Type\" },\r\n  { field: \"tro\", headerName: \"TRO\" },\r\n  { field: \"abstract\", headerName: \"Abstract\" },\r\n  { field: \"inventors\", headerName: \"Inventors\" }, // Array, might need formatting\r\n  { field: \"assignee\", headerName: \"Assignee\" },\r\n  { field: \"applicant\", headerName: \"Applicant\" },\r\n  { field: \"uspc_class\", headerName: \"USPC Class\" }, // Array, might need formatting\r\n  { field: \"loc_code\", headerName: \"LOC Code\" }, // Array, might need formatting\r\n  { field: \"cpc_class\", headerName: \"CPC Class\" }, // Array, might need formatting\r\n  { field: \"reg_no\", headerName: \"Registration No.\", defaultHidden: true },\r\n  { field: \"application_number\", headerName: \"Application No.\", defaultHidden: true },\r\n  { field: \"date_filed\", headerName: \"Date Filed\", defaultHidden: true },\r\n  { field: \"claims_count\", headerName: \"Claims Count\", defaultHidden: true },\r\n  { field: \"independent_claims_count\", headerName: \"Independent Claims\", defaultHidden: true },\r\n  // Add other fields as needed, e.g., foreign_priority, related_applications\r\n];\r\n\r\n\r\nfunction PatentExplorerPage() {\r\n  const [filters, setFilters] = useState(INITIAL_FILTERS);\r\n  const [patentsData, setPatentsData] = useState({ patents: [], pagination: {}, quick_stats: {} });\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n\r\n  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);\r\n  const [selectedPatentForDetail, setSelectedPatentForDetail] = useState(null);\r\n  const [isDetailLoading, setIsDetailLoading] = useState(false);\r\n  const [detailError, setDetailError] = useState(null);\r\n\r\n  const [isImageModalOpen, setIsImageModalOpen] = useState(false);\r\n  const [selectedPatentForImages, setSelectedPatentForImages] = useState(null);\r\n  const [imagePathsData, setImagePathsData] = useState({ image_paths: [], patent_reg_no: \"\", base_image_folder_path: \"\" });\r\n  const [isImagesLoading, setIsImagesLoading] = useState(false);\r\n  const [imagesError, setImagesError] = useState(null);\r\n\r\n  const [availableColumns] = useState(ALL_AVAILABLE_COLUMNS);\r\n\r\n  const fetchPatents = useCallback(async () => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      // TDD: TEST: API_CLIENT.get for patent list is called with correct filter params\r\n      const apiFilters = {\r\n        ...filters,\r\n        date_published_start: filters.date_published_range?.from,\r\n        date_published_end: filters.date_published_range?.to,\r\n      };\r\n      delete apiFilters.date_published_range; // Clean up the temporary range object\r\n\r\n      const response = await getPatentsForExploration(apiFilters);\r\n      setPatentsData(response.data);\r\n    } catch (apiError) {\r\n      setError(apiError.message || 'Failed to fetch patents');\r\n      setPatentsData({ patents: [], pagination: {}, quick_stats: {} }); // Reset data on error\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [filters]);\r\n\r\n  useEffect(() => {\r\n    fetchPatents();\r\n  }, [fetchPatents]);\r\n\r\n  const handleFilterChange = useCallback((newFilterValues) => {\r\n    setFilters((prevFilters) => ({\r\n      ...prevFilters,\r\n      ...newFilterValues,\r\n      page: 1, // Reset to page 1 on filter change\r\n    }));\r\n  }, []);\r\n\r\n  const handlePageChange = useCallback((newPage) => {\r\n    setFilters((prevFilters) => ({ ...prevFilters, page: newPage }));\r\n  }, []);\r\n\r\n  const handlePerPageChange = useCallback((newPerPage) => {\r\n    setFilters((prevFilters) => ({\r\n      ...prevFilters,\r\n      per_page: newPerPage,\r\n      page: 1, // Reset to page 1\r\n    }));\r\n  }, []);\r\n\r\n  const handleSortChange = useCallback((sortByField, sortDirection) => {\r\n    setFilters((prevFilters) => ({\r\n      ...prevFilters,\r\n      sort_by: sortByField,\r\n      sort_dir: sortDirection,\r\n    }));\r\n  }, []);\r\n\r\n  const handleColumnSelectionChange = useCallback((newlySelectedColumnsFieldsArray) => {\r\n    setFilters((prevFilters) => ({\r\n      ...prevFilters,\r\n      selected_columns: newlySelectedColumnsFieldsArray,\r\n    }));\r\n  }, []);\r\n\r\n  const openDetailModal = useCallback(async (patentIdForDetail) => {\r\n    setIsDetailModalOpen(true);\r\n    setIsDetailLoading(true);\r\n    setDetailError(null);\r\n    setSelectedPatentForDetail(null); // Clear previous data\r\n    try {\r\n      // TDD: TEST: API_CLIENT.get for patent detail is called with correct patent_id\r\n      const response = await getPatentDetails(patentIdForDetail);\r\n      setSelectedPatentForDetail(response.data);\r\n    } catch (apiError) {\r\n      setDetailError(apiError.message || 'Failed to fetch patent details');\r\n    } finally {\r\n      setIsDetailLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  const closeDetailModal = useCallback(() => {\r\n    setIsDetailModalOpen(false);\r\n    setSelectedPatentForDetail(null);\r\n  }, []);\r\n\r\n  const openImageModal = useCallback(async (patentForImages) => { // patentForImages is {id, reg_no, patent_title}\r\n    setSelectedPatentForImages(patentForImages);\r\n    setIsImageModalOpen(true);\r\n    setIsImagesLoading(true);\r\n    setImagesError(null);\r\n    setImagePathsData({ image_paths: [], patent_reg_no: \"\", base_image_folder_path: \"\" }); // Clear previous\r\n    try {\r\n      // TDD: TEST: API_CLIENT.get for patent images is called with correct patent_id\r\n      const response = await getPatentImagesInfo(patentForImages.id);\r\n      setImagePathsData(response.data);\r\n    } catch (apiError) {\r\n      setImagesError(apiError.message || 'Failed to fetch patent images');\r\n    } finally {\r\n      setIsImagesLoading(false);\r\n    }\r\n  }, []);\r\n\r\n  const closeImageModal = useCallback(() => {\r\n    setIsImageModalOpen(false);\r\n    setSelectedPatentForImages(null);\r\n  }, []);\r\n  \r\n  const displayedColumnsConfig = React.useMemo(() => {\r\n    return filters.selected_columns\r\n      .map(field => availableColumns.find(col => col.field === field))\r\n      .filter(Boolean); // Ensure only valid columns are passed\r\n  }, [filters.selected_columns, availableColumns]);\r\n\r\n  return (\r\n    <Container maxWidth=\"xl\" sx={{ mt: 4, mb: 4 }}>\r\n      <Typography variant=\"h4\" component=\"h1\" gutterBottom>\r\n        Patent Explorer\r\n      </Typography>\r\n\r\n      {/* TDD: TEST: FilterControls receives current_filters and callbacks */}\r\n      <FilterControls\r\n        current_filters={filters}\r\n        on_filter_change={handleFilterChange}\r\n        available_columns={availableColumns}\r\n        on_column_selection_change={handleColumnSelectionChange}\r\n      />\r\n\r\n      {/* TDD: TEST: QuickStatsDisplay receives correct stats */}\r\n      {patentsData.quick_stats && patentsData.quick_stats.total_filtered_results != null && (\r\n        <QuickStatsDisplay stats={patentsData.quick_stats} />\r\n      )}\r\n\r\n      {isLoading ? (\r\n        <CircularProgress sx={{ display: 'block', margin: '20px auto' }} />\r\n      ) : error ? (\r\n        <Alert severity=\"error\" sx={{ mt: 2 }}>{error}</Alert>\r\n      ) : patentsData.patents && patentsData.patents.length > 0 ? (\r\n        <>\r\n          {/* TDD: TEST: PatentTable receives patents and callbacks */}\r\n          <PatentTable\r\n            patents={patentsData.patents}\r\n            displayed_columns={displayedColumnsConfig}\r\n            on_row_id_click={openDetailModal}\r\n            on_row_title_click={openImageModal}\r\n            current_sort_by={filters.sort_by}\r\n            current_sort_dir={filters.sort_dir}\r\n            on_sort_change={handleSortChange}\r\n          />\r\n          {/* TDD: TEST: PaginationControls receives correct props and callbacks */}\r\n          <PaginationControls\r\n            current_page={filters.page}\r\n            items_per_page={filters.per_page}\r\n            total_items={patentsData.pagination.total_items || 0}\r\n            on_page_change={handlePageChange}\r\n            on_per_page_change={handlePerPageChange}\r\n          />\r\n        </>\r\n      ) : (\r\n        <Alert severity=\"info\" sx={{ mt: 2 }}>No patents found matching your criteria.</Alert>\r\n      )}\r\n\r\n      {/* TDD: TEST: PatentDetailModal receives correct props */}\r\n      <PatentDetailModal\r\n        is_open={isDetailModalOpen}\r\n        on_close={closeDetailModal}\r\n        patent_data={selectedPatentForDetail}\r\n        is_loading={isDetailLoading}\r\n        error={detailError}\r\n      />\r\n\r\n      {/* TDD: TEST: PatentImageModal receives correct props */}\r\n      <PatentImageModal\r\n        is_open={isImageModalOpen}\r\n        on_close={closeImageModal}\r\n        patent_title={selectedPatentForImages ? selectedPatentForImages.patent_title : \"\"}\r\n        image_data={imagePathsData}\r\n        is_loading={isImagesLoading}\r\n        error={imagesError}\r\n      />\r\n    </Container>\r\n  );\r\n}\r\n\r\nexport default PatentExplorerPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SACEC,SAAS,EACTC,UAAU,EACVC,gBAAgB,EAChBC,KAAK,QACA,eAAe;AACtB,SACEC,wBAAwB,EACxBC,gBAAgB,EAChBC,mBAAmB,QACd,+BAA+B;AACtC,SAASC,SAAS,QAAQ,kBAAkB,CAAC,CAAC;AAC9C,OAAO,4BAA4B,CAAC,CAAC;;AAErC;AACA,OAAOC,cAAc,MAAM,4CAA4C;AACvE,OAAOC,iBAAiB,MAAM,+CAA+C;AAC7E,OAAOC,WAAW,MAAM,yCAAyC;AACjE,OAAOC,kBAAkB,MAAM,gDAAgD;AAC/E,OAAOC,iBAAiB,MAAM,+CAA+C;AAC7E,OAAOC,gBAAgB,MAAM,8CAA8C;;AAE3E;AACA;AACA;AACA;AACA;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEA,MAAMC,eAAe,GAAG;EACtBC,kBAAkB,EAAE,EAAE;EACtBC,mBAAmB,EAAE,EAAE;EACvBC,eAAe,EAAE,EAAE;EACnBC,oBAAoB,EAAE;IAAEC,IAAI,EAAE,IAAI;IAAEC,EAAE,EAAE;EAAK,CAAC;EAAE;EAChDC,YAAY,EAAE,EAAE;EAChBC,UAAU,EAAE,KAAK;EACjBC,gBAAgB,EAAE,EAAE;EACpBC,eAAe,EAAE,EAAE;EACnBC,gBAAgB,EAAE,EAAE;EACpBC,iBAAiB,EAAE,EAAE;EACrBC,eAAe,EAAE,EAAE;EACnBC,gBAAgB,EAAE,EAAE;EACpBC,IAAI,EAAE,CAAC;EACPC,QAAQ,EAAE,EAAE;EACZC,OAAO,EAAE,gBAAgB;EACzBC,QAAQ,EAAE,MAAM;EAChBC,gBAAgB,EAAE,CAAC,aAAa,EAAE,cAAc,EAAE,gBAAgB,EAAE,aAAa,EAAE,KAAK,CAAC,CAAE;AAC7F,CAAC;;AAED;AACA,MAAMC,qBAAqB,GAAG,CAC5B;EAAEC,KAAK,EAAE,IAAI;EAAEC,UAAU,EAAE,IAAI;EAAEC,aAAa,EAAE,KAAK;EAAEC,aAAa,EAAE;AAAK,CAAC;AAAE;AAC9E;EAAEH,KAAK,EAAE,aAAa;EAAEC,UAAU,EAAE,aAAa;EAAEC,aAAa,EAAE;AAAK,CAAC,EACxE;EAAEF,KAAK,EAAE,cAAc;EAAEC,UAAU,EAAE,cAAc;EAAEC,aAAa,EAAE;AAAK,CAAC,EAC1E;EAAEF,KAAK,EAAE,gBAAgB;EAAEC,UAAU,EAAE;AAAiB,CAAC,EACzD;EAAED,KAAK,EAAE,aAAa;EAAEC,UAAU,EAAE;AAAc,CAAC,EACnD;EAAED,KAAK,EAAE,KAAK;EAAEC,UAAU,EAAE;AAAM,CAAC,EACnC;EAAED,KAAK,EAAE,UAAU;EAAEC,UAAU,EAAE;AAAW,CAAC,EAC7C;EAAED,KAAK,EAAE,WAAW;EAAEC,UAAU,EAAE;AAAY,CAAC;AAAE;AACjD;EAAED,KAAK,EAAE,UAAU;EAAEC,UAAU,EAAE;AAAW,CAAC,EAC7C;EAAED,KAAK,EAAE,WAAW;EAAEC,UAAU,EAAE;AAAY,CAAC,EAC/C;EAAED,KAAK,EAAE,YAAY;EAAEC,UAAU,EAAE;AAAa,CAAC;AAAE;AACnD;EAAED,KAAK,EAAE,UAAU;EAAEC,UAAU,EAAE;AAAW,CAAC;AAAE;AAC/C;EAAED,KAAK,EAAE,WAAW;EAAEC,UAAU,EAAE;AAAY,CAAC;AAAE;AACjD;EAAED,KAAK,EAAE,QAAQ;EAAEC,UAAU,EAAE,kBAAkB;EAAEE,aAAa,EAAE;AAAK,CAAC,EACxE;EAAEH,KAAK,EAAE,oBAAoB;EAAEC,UAAU,EAAE,iBAAiB;EAAEE,aAAa,EAAE;AAAK,CAAC,EACnF;EAAEH,KAAK,EAAE,YAAY;EAAEC,UAAU,EAAE,YAAY;EAAEE,aAAa,EAAE;AAAK,CAAC,EACtE;EAAEH,KAAK,EAAE,cAAc;EAAEC,UAAU,EAAE,cAAc;EAAEE,aAAa,EAAE;AAAK,CAAC,EAC1E;EAAEH,KAAK,EAAE,0BAA0B;EAAEC,UAAU,EAAE,oBAAoB;EAAEE,aAAa,EAAE;AAAK;AAC3F;AAAA,CACD;AAGD,SAASC,kBAAkBA,CAAA,EAAG;EAAAC,EAAA;EAC5B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGjD,QAAQ,CAACqB,eAAe,CAAC;EACvD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAC;IAAEoD,OAAO,EAAE,EAAE;IAAEC,UAAU,EAAE,CAAC,CAAC;IAAEC,WAAW,EAAE,CAAC;EAAE,CAAC,CAAC;EAChG,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACyD,KAAK,EAAEC,QAAQ,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EAExC,MAAM,CAAC2D,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC6D,uBAAuB,EAAEC,0BAA0B,CAAC,GAAG9D,QAAQ,CAAC,IAAI,CAAC;EAC5E,MAAM,CAAC+D,eAAe,EAAEC,kBAAkB,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACiE,WAAW,EAAEC,cAAc,CAAC,GAAGlE,QAAQ,CAAC,IAAI,CAAC;EAEpD,MAAM,CAACmE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACqE,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGtE,QAAQ,CAAC,IAAI,CAAC;EAC5E,MAAM,CAACuE,cAAc,EAAEC,iBAAiB,CAAC,GAAGxE,QAAQ,CAAC;IAAEyE,WAAW,EAAE,EAAE;IAAEC,aAAa,EAAE,EAAE;IAAEC,sBAAsB,EAAE;EAAG,CAAC,CAAC;EACxH,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC8E,WAAW,EAAEC,cAAc,CAAC,GAAG/E,QAAQ,CAAC,IAAI,CAAC;EAEpD,MAAM,CAACgF,gBAAgB,CAAC,GAAGhF,QAAQ,CAACyC,qBAAqB,CAAC;EAE1D,MAAMwC,YAAY,GAAG/E,WAAW,CAAC,YAAY;IAC3CsD,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,IAAI,CAAC;IACd,IAAI;MAAA,IAAAwB,qBAAA,EAAAC,sBAAA;MACF;MACA,MAAMC,UAAU,GAAG;QACjB,GAAGpC,OAAO;QACVqC,oBAAoB,GAAAH,qBAAA,GAAElC,OAAO,CAACvB,oBAAoB,cAAAyD,qBAAA,uBAA5BA,qBAAA,CAA8BxD,IAAI;QACxD4D,kBAAkB,GAAAH,sBAAA,GAAEnC,OAAO,CAACvB,oBAAoB,cAAA0D,sBAAA,uBAA5BA,sBAAA,CAA8BxD;MACpD,CAAC;MACD,OAAOyD,UAAU,CAAC3D,oBAAoB,CAAC,CAAC;;MAExC,MAAM8D,QAAQ,GAAG,MAAMhF,wBAAwB,CAAC6E,UAAU,CAAC;MAC3DjC,cAAc,CAACoC,QAAQ,CAACC,IAAI,CAAC;IAC/B,CAAC,CAAC,OAAOC,QAAQ,EAAE;MACjB/B,QAAQ,CAAC+B,QAAQ,CAACC,OAAO,IAAI,yBAAyB,CAAC;MACvDvC,cAAc,CAAC;QAAEC,OAAO,EAAE,EAAE;QAAEC,UAAU,EAAE,CAAC,CAAC;QAAEC,WAAW,EAAE,CAAC;MAAE,CAAC,CAAC,CAAC,CAAC;IACpE,CAAC,SAAS;MACRE,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACR,OAAO,CAAC,CAAC;EAEb/C,SAAS,CAAC,MAAM;IACdgF,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;EAElB,MAAMU,kBAAkB,GAAGzF,WAAW,CAAE0F,eAAe,IAAK;IAC1D3C,UAAU,CAAE4C,WAAW,KAAM;MAC3B,GAAGA,WAAW;MACd,GAAGD,eAAe;MAClBxD,IAAI,EAAE,CAAC,CAAE;IACX,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM0D,gBAAgB,GAAG5F,WAAW,CAAE6F,OAAO,IAAK;IAChD9C,UAAU,CAAE4C,WAAW,KAAM;MAAE,GAAGA,WAAW;MAAEzD,IAAI,EAAE2D;IAAQ,CAAC,CAAC,CAAC;EAClE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,mBAAmB,GAAG9F,WAAW,CAAE+F,UAAU,IAAK;IACtDhD,UAAU,CAAE4C,WAAW,KAAM;MAC3B,GAAGA,WAAW;MACdxD,QAAQ,EAAE4D,UAAU;MACpB7D,IAAI,EAAE,CAAC,CAAE;IACX,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM8D,gBAAgB,GAAGhG,WAAW,CAAC,CAACiG,WAAW,EAAEC,aAAa,KAAK;IACnEnD,UAAU,CAAE4C,WAAW,KAAM;MAC3B,GAAGA,WAAW;MACdvD,OAAO,EAAE6D,WAAW;MACpB5D,QAAQ,EAAE6D;IACZ,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,2BAA2B,GAAGnG,WAAW,CAAEoG,+BAA+B,IAAK;IACnFrD,UAAU,CAAE4C,WAAW,KAAM;MAC3B,GAAGA,WAAW;MACdrD,gBAAgB,EAAE8D;IACpB,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,eAAe,GAAGrG,WAAW,CAAC,MAAOsG,iBAAiB,IAAK;IAC/D5C,oBAAoB,CAAC,IAAI,CAAC;IAC1BI,kBAAkB,CAAC,IAAI,CAAC;IACxBE,cAAc,CAAC,IAAI,CAAC;IACpBJ,0BAA0B,CAAC,IAAI,CAAC,CAAC,CAAC;IAClC,IAAI;MACF;MACA,MAAMyB,QAAQ,GAAG,MAAM/E,gBAAgB,CAACgG,iBAAiB,CAAC;MAC1D1C,0BAA0B,CAACyB,QAAQ,CAACC,IAAI,CAAC;IAC3C,CAAC,CAAC,OAAOC,QAAQ,EAAE;MACjBvB,cAAc,CAACuB,QAAQ,CAACC,OAAO,IAAI,gCAAgC,CAAC;IACtE,CAAC,SAAS;MACR1B,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMyC,gBAAgB,GAAGvG,WAAW,CAAC,MAAM;IACzC0D,oBAAoB,CAAC,KAAK,CAAC;IAC3BE,0BAA0B,CAAC,IAAI,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM4C,cAAc,GAAGxG,WAAW,CAAC,MAAOyG,eAAe,IAAK;IAAE;IAC9DrC,0BAA0B,CAACqC,eAAe,CAAC;IAC3CvC,mBAAmB,CAAC,IAAI,CAAC;IACzBS,kBAAkB,CAAC,IAAI,CAAC;IACxBE,cAAc,CAAC,IAAI,CAAC;IACpBP,iBAAiB,CAAC;MAAEC,WAAW,EAAE,EAAE;MAAEC,aAAa,EAAE,EAAE;MAAEC,sBAAsB,EAAE;IAAG,CAAC,CAAC,CAAC,CAAC;IACvF,IAAI;MACF;MACA,MAAMY,QAAQ,GAAG,MAAM9E,mBAAmB,CAACkG,eAAe,CAACC,EAAE,CAAC;MAC9DpC,iBAAiB,CAACe,QAAQ,CAACC,IAAI,CAAC;IAClC,CAAC,CAAC,OAAOC,QAAQ,EAAE;MACjBV,cAAc,CAACU,QAAQ,CAACC,OAAO,IAAI,+BAA+B,CAAC;IACrE,CAAC,SAAS;MACRb,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMgC,eAAe,GAAG3G,WAAW,CAAC,MAAM;IACxCkE,mBAAmB,CAAC,KAAK,CAAC;IAC1BE,0BAA0B,CAAC,IAAI,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMwC,sBAAsB,GAAG/G,KAAK,CAACgH,OAAO,CAAC,MAAM;IACjD,OAAO/D,OAAO,CAACR,gBAAgB,CAC5BwE,GAAG,CAACtE,KAAK,IAAIsC,gBAAgB,CAACiC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACxE,KAAK,KAAKA,KAAK,CAAC,CAAC,CAC/DyE,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC;EACtB,CAAC,EAAE,CAACpE,OAAO,CAACR,gBAAgB,EAAEwC,gBAAgB,CAAC,CAAC;EAEhD,oBACE9D,OAAA,CAACf,SAAS;IAACkH,QAAQ,EAAC,IAAI;IAACC,EAAE,EAAE;MAAEC,EAAE,EAAE,CAAC;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,gBAC5CvG,OAAA,CAACd,UAAU;MAACsH,OAAO,EAAC,IAAI;MAACC,SAAS,EAAC,IAAI;MAACC,YAAY;MAAAH,QAAA,EAAC;IAErD;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGb9G,OAAA,CAACP,cAAc;MACbsH,eAAe,EAAEjF,OAAQ;MACzBkF,gBAAgB,EAAEvC,kBAAmB;MACrCwC,iBAAiB,EAAEnD,gBAAiB;MACpCoD,0BAA0B,EAAE/B;IAA4B;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzD,CAAC,EAGD9E,WAAW,CAACI,WAAW,IAAIJ,WAAW,CAACI,WAAW,CAAC+E,sBAAsB,IAAI,IAAI,iBAChFnH,OAAA,CAACN,iBAAiB;MAAC0H,KAAK,EAAEpF,WAAW,CAACI;IAAY;MAAAuE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CACrD,EAEAzE,SAAS,gBACRrC,OAAA,CAACb,gBAAgB;MAACiH,EAAE,EAAE;QAAEiB,OAAO,EAAE,OAAO;QAAEC,MAAM,EAAE;MAAY;IAAE;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,GACjEvE,KAAK,gBACPvC,OAAA,CAACZ,KAAK;MAACmI,QAAQ,EAAC,OAAO;MAACnB,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAE,QAAA,EAAEhE;IAAK;MAAAoE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,GACpD9E,WAAW,CAACE,OAAO,IAAIF,WAAW,CAACE,OAAO,CAACsF,MAAM,GAAG,CAAC,gBACvDxH,OAAA,CAAAE,SAAA;MAAAqG,QAAA,gBAEEvG,OAAA,CAACL,WAAW;QACVuC,OAAO,EAAEF,WAAW,CAACE,OAAQ;QAC7BuF,iBAAiB,EAAE7B,sBAAuB;QAC1C8B,eAAe,EAAErC,eAAgB;QACjCsC,kBAAkB,EAAEnC,cAAe;QACnCoC,eAAe,EAAE9F,OAAO,CAACV,OAAQ;QACjCyG,gBAAgB,EAAE/F,OAAO,CAACT,QAAS;QACnCyG,cAAc,EAAE9C;MAAiB;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eAEF9G,OAAA,CAACJ,kBAAkB;QACjBmI,YAAY,EAAEjG,OAAO,CAACZ,IAAK;QAC3B8G,cAAc,EAAElG,OAAO,CAACX,QAAS;QACjC8G,WAAW,EAAEjG,WAAW,CAACG,UAAU,CAAC8F,WAAW,IAAI,CAAE;QACrDC,cAAc,EAAEtD,gBAAiB;QACjCuD,kBAAkB,EAAErD;MAAoB;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC;IAAA,eACF,CAAC,gBAEH9G,OAAA,CAACZ,KAAK;MAACmI,QAAQ,EAAC,MAAM;MAACnB,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAE,QAAA,EAAC;IAAwC;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACtF,eAGD9G,OAAA,CAACH,iBAAiB;MAChBuI,OAAO,EAAE3F,iBAAkB;MAC3B4F,QAAQ,EAAE9C,gBAAiB;MAC3B+C,WAAW,EAAE3F,uBAAwB;MACrC4F,UAAU,EAAE1F,eAAgB;MAC5BN,KAAK,EAAEQ;IAAY;MAAA4D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC,eAGF9G,OAAA,CAACF,gBAAgB;MACfsI,OAAO,EAAEnF,gBAAiB;MAC1BoF,QAAQ,EAAE1C,eAAgB;MAC1B6C,YAAY,EAAErF,uBAAuB,GAAGA,uBAAuB,CAACqF,YAAY,GAAG,EAAG;MAClFC,UAAU,EAAEpF,cAAe;MAC3BkF,UAAU,EAAE7E,eAAgB;MAC5BnB,KAAK,EAAEqB;IAAY;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEhB;AAACjF,EAAA,CArMQD,kBAAkB;AAAA8G,EAAA,GAAlB9G,kBAAkB;AAuM3B,eAAeA,kBAAkB;AAAC,IAAA8G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}