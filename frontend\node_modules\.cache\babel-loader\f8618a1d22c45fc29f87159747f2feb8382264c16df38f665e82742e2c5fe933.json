{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents\\\\Programing\\\\TRO\\\\ModelTestsWorkbench\\\\frontend\\\\src\\\\components\\\\model-test-workbench\\\\MetricsDisplay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport Plot from 'react-plotly.js';\nimport { Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Typography, CircularProgress, Alert, Box, Slider, Grid, Link } from '@mui/material';\nimport { getPerformanceSummary, getScoreDistribution, getConfusionMatrix } from '../../services/api_model_workbench';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MetricsDisplay = ({\n  ipCategory\n}) => {\n  _s();\n  const [performanceSummary, setPerformanceSummary] = useState([]);\n  const [selectedModel, setSelectedModel] = useState(null);\n  const [scoreDistribution, setScoreDistribution] = useState({\n    gt_scores: [],\n    non_gt_scores: []\n  });\n  const [confusionMatrix, setConfusionMatrix] = useState({\n    tp: 0,\n    fn: 0,\n    fp: 0,\n    tn: 0\n  });\n  const [threshold, setThreshold] = useState(0.5); // Default threshold\n  const [loadingSummary, setLoadingSummary] = useState(false);\n  const [loadingDetails, setLoadingDetails] = useState(false);\n  const [error, setError] = useState(null);\n  const ipCategoryDisplay = ipCategory.charAt(0).toUpperCase() + ipCategory.slice(1);\n\n  // Fetch Performance Summary\n  useEffect(() => {\n    const fetchSummary = async () => {\n      setLoadingSummary(true);\n      setError(null);\n      setSelectedModel(null); // Reset selected model when category changes\n      setPerformanceSummary([]);\n      try {\n        const response = await getPerformanceSummary(ipCategory);\n        // Sort by precision_avg_rank ascending before setting state\n        const sortedData = response.data.sort((a, b) => a.precision_avg_rank - b.precision_avg_rank);\n        setPerformanceSummary(sortedData);\n      } catch (err) {\n        console.error(`Error fetching performance summary for ${ipCategory}:`, err);\n        setError(`Failed to load performance summary for ${ipCategoryDisplay}.`);\n      } finally {\n        setLoadingSummary(false);\n      }\n    };\n    fetchSummary();\n  }, [ipCategory, ipCategoryDisplay]); // Rerun when ipCategory changes\n\n  // Fetch Score Distribution when selectedModel changes\n  useEffect(() => {\n    if (!selectedModel) {\n      setScoreDistribution({\n        gt_scores: [],\n        non_gt_scores: []\n      });\n      return;\n    }\n    const fetchScores = async () => {\n      setLoadingDetails(true);\n      setError(null);\n      try {\n        // Corrected parameter order: model_id, ip_category\n        const response = await getScoreDistribution(selectedModel.model_id, ipCategory);\n        console.log(\"Score distribution data received:\", response.data); // Added logging\n        setScoreDistribution(response.data);\n      } catch (err) {\n        console.error(`Error fetching score distribution for model ${selectedModel.model_id}:`, err);\n        setError(`Failed to load score distribution for ${selectedModel.model_name}.`);\n        setScoreDistribution({\n          gt_scores: [],\n          non_gt_scores: []\n        }); // Clear data on error\n      } finally {\n        setLoadingDetails(false); // Stop loading details only after scores are fetched\n      }\n    };\n    fetchScores();\n  }, [selectedModel, ipCategory]); // Rerun when selectedModel or ipCategory changes\n\n  // Fetch Confusion Matrix when selectedModel or threshold changes\n  const fetchMatrix = useCallback(async currentThreshold => {\n    if (!selectedModel) {\n      setConfusionMatrix({\n        tp: 0,\n        fn: 0,\n        fp: 0,\n        tn: 0\n      });\n      return;\n    }\n    // Don't set loadingDetails here if score distribution is already loading\n    if (!loadingDetails) setLoadingDetails(true);\n    // Don't clear error if score distribution fetch failed\n    // setError(null);\n    try {\n      // Corrected parameter order: model_id, ip_category, threshold\n      const response = await getConfusionMatrix(selectedModel.model_id, ipCategory, currentThreshold);\n      setConfusionMatrix(response.data);\n    } catch (err) {\n      console.error(`Error fetching confusion matrix for model ${selectedModel.model_id} at threshold ${currentThreshold}:`, err);\n      setError(`Failed to load confusion matrix for ${selectedModel.model_name}.`);\n      setConfusionMatrix({\n        tp: 0,\n        fn: 0,\n        fp: 0,\n        tn: 0\n      }); // Clear data on error\n    } finally {\n      setLoadingDetails(false); // Ensure loading is stopped\n    }\n  }, [selectedModel, ipCategory]); // Removed loadingDetails from dependency array to prevent potential loops\n\n  useEffect(() => {\n    fetchMatrix(threshold);\n  }, [selectedModel, threshold, fetchMatrix]); // Rerun when selectedModel or threshold changes\n\n  const handleModelSelect = model => {\n    setSelectedModel(model);\n    setThreshold(0.5); // Reset threshold when selecting a new model\n    setError(null); // Clear previous errors\n  };\n  const handleThresholdChange = (event, newValue) => {\n    setThreshold(newValue);\n    // Fetching matrix is handled by the useEffect hook watching threshold\n  };\n  const handleSliderChangeCommitted = (event, newValue) => {\n    // Optional: Could trigger fetch only on commit if performance is an issue\n    // fetchMatrix(newValue);\n  };\n\n  // Plotly data and layout\n  const plotData = [{\n    x: scoreDistribution.non_gt_scores,\n    type: 'histogram',\n    name: 'Non-Matches',\n    opacity: 0.7,\n    marker: {\n      color: 'red'\n    },\n    nbinsx: 50\n  }, {\n    x: scoreDistribution.gt_scores,\n    type: 'histogram',\n    name: 'Ground Truth Matches',\n    opacity: 0.7,\n    marker: {\n      color: 'green'\n    },\n    nbinsx: 50\n  }];\n  const plotLayout = {\n    title: `Score Distribution for ${(selectedModel === null || selectedModel === void 0 ? void 0 : selectedModel.model_name) || 'Selected Model'}`,\n    xaxis: {\n      title: 'Similarity Score',\n      tickangle: 45\n    },\n    // Added tickangle to rotate labels\n    yaxis: {\n      title: 'Frequency',\n      type: 'log'\n    },\n    // Added type: 'log' for logarithmic scale\n    barmode: 'overlay',\n    legend: {\n      x: 0.7,\n      y: 1\n    },\n    margin: {\n      l: 50,\n      r: 50,\n      t: 50,\n      b: 50\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h5\",\n      gutterBottom: true,\n      children: [ipCategoryDisplay, \" Performance Metrics\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 13\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"error\",\n      sx: {\n        mb: 2\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 145,\n      columnNumber: 23\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      gutterBottom: true,\n      children: \"Model Performance Summary\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 13\n    }, this), loadingSummary ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 17\n    }, this) : performanceSummary.length === 0 && !error ? /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"info\",\n      children: [\"No performance data available for \", ipCategoryDisplay, \". Run comparisons first.\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 18\n    }, this) : /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      sx: {\n        mb: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        \"aria-label\": `${ipCategory} performance summary table`,\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: \"Model Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"right\",\n              children: \"Precision Avg. Rank\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 33\n            }, this), performanceSummary.some(m => m.hasOwnProperty('avg_gt_score')) && /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"right\",\n              children: \"Avg. GT Score\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 100\n            }, this), performanceSummary.some(m => m.hasOwnProperty('avg_non_gt_score')) && /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"right\",\n              children: \"Avg. Non-GT Score\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 104\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: performanceSummary.map(model => {\n            var _model$precision_avg_, _model$precision_avg_2, _model$avg_gt_score$t, _model$avg_gt_score, _model$avg_non_gt_sco, _model$avg_non_gt_sco2;\n            return /*#__PURE__*/_jsxDEV(TableRow, {\n              hover: true,\n              onClick: () => handleModelSelect(model),\n              style: {\n                cursor: 'pointer'\n              },\n              selected: (selectedModel === null || selectedModel === void 0 ? void 0 : selectedModel.model_id) === model.model_id,\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                component: \"th\",\n                scope: \"row\",\n                children: [/*#__PURE__*/_jsxDEV(Link, {\n                  component: \"button\",\n                  variant: \"body2\",\n                  onClick: e => {\n                    e.stopPropagation();\n                    handleModelSelect(model);\n                  },\n                  children: model.model_name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  display: \"block\",\n                  color: \"textSecondary\",\n                  children: [\"ID: \", model.model_id]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: (_model$precision_avg_ = (_model$precision_avg_2 = model.precision_avg_rank) === null || _model$precision_avg_2 === void 0 ? void 0 : _model$precision_avg_2.toFixed(2)) !== null && _model$precision_avg_ !== void 0 ? _model$precision_avg_ : 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 37\n              }, this), performanceSummary.some(m => m.hasOwnProperty('avg_gt_score')) && /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: (_model$avg_gt_score$t = (_model$avg_gt_score = model.avg_gt_score) === null || _model$avg_gt_score === void 0 ? void 0 : _model$avg_gt_score.toFixed(4)) !== null && _model$avg_gt_score$t !== void 0 ? _model$avg_gt_score$t : 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 41\n              }, this), performanceSummary.some(m => m.hasOwnProperty('avg_non_gt_score')) && /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"right\",\n                children: (_model$avg_non_gt_sco = (_model$avg_non_gt_sco2 = model.avg_non_gt_score) === null || _model$avg_non_gt_sco2 === void 0 ? void 0 : _model$avg_non_gt_sco2.toFixed(4)) !== null && _model$avg_non_gt_sco !== void 0 ? _model$avg_non_gt_sco : 'N/A'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 41\n              }, this)]\n            }, model.model_id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 33\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 153,\n      columnNumber: 17\n    }, this), selectedModel && /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: [\"Detailed Analysis for: \", selectedModel.model_name]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 21\n      }, this), loadingDetails ? /*#__PURE__*/_jsxDEV(CircularProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 25\n      }, this) : /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 12,\n          children: [\" \", /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            gutterBottom: true,\n            children: \"Score Distribution\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 33\n          }, this), scoreDistribution.gt_scores.length > 0 || scoreDistribution.non_gt_scores.length > 0 ? /*#__PURE__*/_jsxDEV(Plot, {\n            data: plotData,\n            layout: plotLayout,\n            useResizeHandler: true,\n            style: {\n              width: '100%',\n              height: '400px',\n              minWidth: '800px'\n            } // Added minWidth\n            ,\n            config: {\n              responsive: true\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 37\n          }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n            severity: \"info\",\n            children: \"Score distribution data not available for this model.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 37\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 29\n        }, this), \" \", /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 12,\n          children: [\" \", /*#__PURE__*/_jsxDEV(Paper, {\n            elevation: 3,\n            sx: {\n              p: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              children: \"Threshold Analysis\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                width: '90%',\n                margin: 'auto',\n                mt: 2,\n                mb: 4\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                id: \"threshold-slider-label\",\n                gutterBottom: true,\n                children: [\"Similarity Threshold: \", threshold.toFixed(2)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(Slider, {\n                \"aria-labelledby\": \"threshold-slider-label\",\n                value: threshold,\n                onChange: handleThresholdChange,\n                onChangeCommitted: handleSliderChangeCommitted,\n                step: 0.01,\n                min: 0,\n                max: 1,\n                valueLabelDisplay: \"auto\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              gutterBottom: true,\n              sx: {\n                mt: 2\n              },\n              children: [\"Confusion Matrix (Threshold: \", threshold.toFixed(2), \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n              component: Paper,\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(Table, {\n                size: \"small\",\n                \"aria-label\": \"confusion matrix\",\n                children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                  children: /*#__PURE__*/_jsxDEV(TableRow, {\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 245,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"center\",\n                      children: /*#__PURE__*/_jsxDEV(\"b\", {\n                        children: \"Predicted: Match\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 246,\n                        columnNumber: 79\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 246,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"center\",\n                      children: /*#__PURE__*/_jsxDEV(\"b\", {\n                        children: \"Predicted: No Match\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 247,\n                        columnNumber: 79\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 247,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 49\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                  children: [/*#__PURE__*/_jsxDEV(TableRow, {\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      component: \"th\",\n                      scope: \"row\",\n                      children: /*#__PURE__*/_jsxDEV(\"b\", {\n                        children: \"Actual: Match\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 252,\n                        columnNumber: 91\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 252,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"center\",\n                      sx: {\n                        backgroundColor: 'rgba(0, 128, 0, 0.1)'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h6\",\n                        children: confusionMatrix.tp\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 254,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        children: \"(True Positives)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 255,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 253,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"center\",\n                      sx: {\n                        backgroundColor: 'rgba(255, 165, 0, 0.1)'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h6\",\n                        children: confusionMatrix.fn\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 258,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        children: \"(False Negatives)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 259,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 257,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 251,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(TableRow, {\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      component: \"th\",\n                      scope: \"row\",\n                      children: /*#__PURE__*/_jsxDEV(\"b\", {\n                        children: \"Actual: No Match\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 263,\n                        columnNumber: 91\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 263,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"center\",\n                      sx: {\n                        backgroundColor: 'rgba(255, 0, 0, 0.1)'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h6\",\n                        children: confusionMatrix.fp\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 265,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        children: \"(False Positives)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 266,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 264,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"center\",\n                      sx: {\n                        backgroundColor: 'rgba(0, 0, 255, 0.1)'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"h6\",\n                        children: confusionMatrix.tn\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 269,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"caption\",\n                        children: \"(True Negatives)\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 270,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 268,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 262,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              display: \"block\",\n              sx: {\n                mt: 1\n              },\n              children: \"TP: Correctly identified matches. FN: Actual matches missed. FP: Incorrectly identified as matches. TN: Correctly identified non-matches.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 25\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 142,\n    columnNumber: 9\n  }, this);\n};\n_s(MetricsDisplay, \"UimgRfsNoFMLFp9uOkJQFPBjzQ8=\");\n_c = MetricsDisplay;\nexport default MetricsDisplay;\nvar _c;\n$RefreshReg$(_c, \"MetricsDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Plot", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "Typography", "CircularProgress", "<PERSON><PERSON>", "Box", "Slide<PERSON>", "Grid", "Link", "getPerformanceSummary", "getScoreDistribution", "getConfusionMatrix", "jsxDEV", "_jsxDEV", "MetricsDisplay", "ipCategory", "_s", "performanceSummary", "setPerformanceSummary", "selected<PERSON><PERSON>l", "setSelectedModel", "scoreDistribution", "setScoreDistribution", "gt_scores", "non_gt_scores", "confusionMatrix", "setConfusionMatrix", "tp", "fn", "fp", "tn", "threshold", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loadingSummary", "setLoadingSummary", "loadingDetails", "setLoadingDetails", "error", "setError", "ipCategoryDisplay", "char<PERSON>t", "toUpperCase", "slice", "fetchSummary", "response", "sortedData", "data", "sort", "a", "b", "precision_avg_rank", "err", "console", "fetchScores", "model_id", "log", "model_name", "fetchMatrix", "currentThreshold", "handleModelSelect", "model", "handleThresholdChange", "event", "newValue", "handleSliderChangeCommitted", "plotData", "x", "type", "name", "opacity", "marker", "color", "nbinsx", "plotLayout", "title", "xaxis", "tickangle", "yaxis", "barmode", "legend", "y", "margin", "l", "r", "t", "sx", "p", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "severity", "mb", "length", "component", "align", "some", "m", "hasOwnProperty", "map", "_model$precision_avg_", "_model$precision_avg_2", "_model$avg_gt_score$t", "_model$avg_gt_score", "_model$avg_non_gt_sco", "_model$avg_non_gt_sco2", "hover", "onClick", "style", "cursor", "selected", "scope", "e", "stopPropagation", "display", "toFixed", "avg_gt_score", "avg_non_gt_score", "container", "spacing", "item", "xs", "md", "layout", "useResizeHandler", "width", "height", "min<PERSON><PERSON><PERSON>", "config", "responsive", "elevation", "mt", "id", "value", "onChange", "onChangeCommitted", "step", "min", "max", "valueLabelDisplay", "size", "backgroundColor", "_c", "$RefreshReg$"], "sources": ["D:/Documents/Programing/TRO/ModelTestsWorkbench/frontend/src/components/model-test-workbench/MetricsDisplay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\r\nimport Plot from 'react-plotly.js';\r\nimport {\r\n    Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper,\r\n    Typography, CircularProgress, Alert, Box, Slider, Grid, Link\r\n} from '@mui/material';\r\nimport { getPerformanceSummary, getScoreDistribution, getConfusionMatrix } from '../../services/api_model_workbench';\r\n\r\nconst MetricsDisplay = ({ ipCategory }) => {\r\n    const [performanceSummary, setPerformanceSummary] = useState([]);\r\n    const [selectedModel, setSelectedModel] = useState(null);\r\n    const [scoreDistribution, setScoreDistribution] = useState({ gt_scores: [], non_gt_scores: [] });\r\n    const [confusionMatrix, setConfusionMatrix] = useState({ tp: 0, fn: 0, fp: 0, tn: 0 });\r\n    const [threshold, setThreshold] = useState(0.5); // Default threshold\r\n    const [loadingSummary, setLoadingSummary] = useState(false);\r\n    const [loadingDetails, setLoadingDetails] = useState(false);\r\n    const [error, setError] = useState(null);\r\n\r\n    const ipCategoryDisplay = ipCategory.charAt(0).toUpperCase() + ipCategory.slice(1);\r\n\r\n    // Fetch Performance Summary\r\n    useEffect(() => {\r\n        const fetchSummary = async () => {\r\n            setLoadingSummary(true);\r\n            setError(null);\r\n            setSelectedModel(null); // Reset selected model when category changes\r\n            setPerformanceSummary([]);\r\n            try {\r\n                const response = await getPerformanceSummary(ipCategory);\r\n                // Sort by precision_avg_rank ascending before setting state\r\n                const sortedData = response.data.sort((a, b) => a.precision_avg_rank - b.precision_avg_rank);\r\n                setPerformanceSummary(sortedData);\r\n            } catch (err) {\r\n                console.error(`Error fetching performance summary for ${ipCategory}:`, err);\r\n                setError(`Failed to load performance summary for ${ipCategoryDisplay}.`);\r\n            } finally {\r\n                setLoadingSummary(false);\r\n            }\r\n        };\r\n        fetchSummary();\r\n    }, [ipCategory, ipCategoryDisplay]); // Rerun when ipCategory changes\r\n\r\n    // Fetch Score Distribution when selectedModel changes\r\n    useEffect(() => {\r\n        if (!selectedModel) {\r\n            setScoreDistribution({ gt_scores: [], non_gt_scores: [] });\r\n            return;\r\n        }\r\n        const fetchScores = async () => {\r\n            setLoadingDetails(true);\r\n            setError(null);\r\n            try {\r\n                // Corrected parameter order: model_id, ip_category\r\n                const response = await getScoreDistribution(selectedModel.model_id, ipCategory);\r\n                console.log(\"Score distribution data received:\", response.data); // Added logging\r\n                setScoreDistribution(response.data);\r\n            } catch (err) {\r\n                console.error(`Error fetching score distribution for model ${selectedModel.model_id}:`, err);\r\n                setError(`Failed to load score distribution for ${selectedModel.model_name}.`);\r\n                setScoreDistribution({ gt_scores: [], non_gt_scores: [] }); // Clear data on error\r\n            } finally {\r\n                setLoadingDetails(false); // Stop loading details only after scores are fetched\r\n            }\r\n        };\r\n        fetchScores();\r\n    }, [selectedModel, ipCategory]); // Rerun when selectedModel or ipCategory changes\r\n\r\n    // Fetch Confusion Matrix when selectedModel or threshold changes\r\n    const fetchMatrix = useCallback(async (currentThreshold) => {\r\n        if (!selectedModel) {\r\n            setConfusionMatrix({ tp: 0, fn: 0, fp: 0, tn: 0 });\r\n            return;\r\n        }\r\n        // Don't set loadingDetails here if score distribution is already loading\r\n        if (!loadingDetails) setLoadingDetails(true);\r\n        // Don't clear error if score distribution fetch failed\r\n        // setError(null);\r\n        try {\r\n            // Corrected parameter order: model_id, ip_category, threshold\r\n            const response = await getConfusionMatrix(selectedModel.model_id, ipCategory, currentThreshold);\r\n            setConfusionMatrix(response.data);\r\n        } catch (err) {\r\n            console.error(`Error fetching confusion matrix for model ${selectedModel.model_id} at threshold ${currentThreshold}:`, err);\r\n            setError(`Failed to load confusion matrix for ${selectedModel.model_name}.`);\r\n            setConfusionMatrix({ tp: 0, fn: 0, fp: 0, tn: 0 }); // Clear data on error\r\n        } finally {\r\n            setLoadingDetails(false); // Ensure loading is stopped\r\n        }\r\n    }, [selectedModel, ipCategory]); // Removed loadingDetails from dependency array to prevent potential loops\r\n\r\n    useEffect(() => {\r\n        fetchMatrix(threshold);\r\n    }, [selectedModel, threshold, fetchMatrix]); // Rerun when selectedModel or threshold changes\r\n\r\n\r\n    const handleModelSelect = (model) => {\r\n        setSelectedModel(model);\r\n        setThreshold(0.5); // Reset threshold when selecting a new model\r\n        setError(null); // Clear previous errors\r\n    };\r\n\r\n    const handleThresholdChange = (event, newValue) => {\r\n        setThreshold(newValue);\r\n        // Fetching matrix is handled by the useEffect hook watching threshold\r\n    };\r\n\r\n    const handleSliderChangeCommitted = (event, newValue) => {\r\n        // Optional: Could trigger fetch only on commit if performance is an issue\r\n        // fetchMatrix(newValue);\r\n    };\r\n\r\n    // Plotly data and layout\r\n    const plotData = [\r\n        {\r\n            x: scoreDistribution.non_gt_scores,\r\n            type: 'histogram',\r\n            name: 'Non-Matches',\r\n            opacity: 0.7,\r\n            marker: { color: 'red' },\r\n            nbinsx: 50,\r\n        },\r\n        {\r\n            x: scoreDistribution.gt_scores,\r\n            type: 'histogram',\r\n            name: 'Ground Truth Matches',\r\n            opacity: 0.7,\r\n            marker: { color: 'green' },\r\n            nbinsx: 50,\r\n        },\r\n    ];\r\n\r\n    const plotLayout = {\r\n        title: `Score Distribution for ${selectedModel?.model_name || 'Selected Model'}`,\r\n        xaxis: { title: 'Similarity Score', tickangle: 45 }, // Added tickangle to rotate labels\r\n        yaxis: { title: 'Frequency', type: 'log' }, // Added type: 'log' for logarithmic scale\r\n        barmode: 'overlay',\r\n        legend: { x: 0.7, y: 1 },\r\n        margin: { l: 50, r: 50, t: 50, b: 50 },\r\n    };\r\n\r\n    return (\r\n        <Box sx={{ p: 3 }}>\r\n            <Typography variant=\"h5\" gutterBottom>{ipCategoryDisplay} Performance Metrics</Typography>\r\n\r\n            {error && <Alert severity=\"error\" sx={{ mb: 2 }}>{error}</Alert>}\r\n\r\n            <Typography variant=\"h6\" gutterBottom>Model Performance Summary</Typography>\r\n            {loadingSummary ? (\r\n                <CircularProgress />\r\n            ) : performanceSummary.length === 0 && !error ? (\r\n                 <Alert severity=\"info\">No performance data available for {ipCategoryDisplay}. Run comparisons first.</Alert>\r\n            ) : (\r\n                <TableContainer component={Paper} sx={{ mb: 4 }}>\r\n                    <Table aria-label={`${ipCategory} performance summary table`}>\r\n                        <TableHead>\r\n                            <TableRow>\r\n                                <TableCell>Model Name</TableCell>\r\n                                <TableCell align=\"right\">Precision Avg. Rank</TableCell>\r\n                                {/* Conditionally render these headers if any model has them, or remove if never present */}\r\n                                {performanceSummary.some(m => m.hasOwnProperty('avg_gt_score')) && <TableCell align=\"right\">Avg. GT Score</TableCell>}\r\n                                {performanceSummary.some(m => m.hasOwnProperty('avg_non_gt_score')) && <TableCell align=\"right\">Avg. Non-GT Score</TableCell>}\r\n                            </TableRow>\r\n                        </TableHead>\r\n                        <TableBody>\r\n                            {performanceSummary.map((model) => (\r\n                                <TableRow\r\n                                    key={model.model_id}\r\n                                    hover\r\n                                    onClick={() => handleModelSelect(model)}\r\n                                    style={{ cursor: 'pointer' }}\r\n                                    selected={selectedModel?.model_id === model.model_id}\r\n                                >\r\n                                    <TableCell component=\"th\" scope=\"row\">\r\n                                        <Link component=\"button\" variant=\"body2\" onClick={(e) => { e.stopPropagation(); handleModelSelect(model); }}>\r\n                                            {model.model_name}\r\n                                        </Link>\r\n                                        <Typography variant=\"caption\" display=\"block\" color=\"textSecondary\">\r\n                                            ID: {model.model_id}\r\n                                        </Typography>\r\n                                    </TableCell>\r\n                                    <TableCell align=\"right\">{model.precision_avg_rank?.toFixed(2) ?? 'N/A'}</TableCell>\r\n                                    {/* Conditionally render these cells */}\r\n                                    {performanceSummary.some(m => m.hasOwnProperty('avg_gt_score')) &&\r\n                                        <TableCell align=\"right\">{model.avg_gt_score?.toFixed(4) ?? 'N/A'}</TableCell>\r\n                                    }\r\n                                    {performanceSummary.some(m => m.hasOwnProperty('avg_non_gt_score')) &&\r\n                                        <TableCell align=\"right\">{model.avg_non_gt_score?.toFixed(4) ?? 'N/A'}</TableCell>\r\n                                    }\r\n                                </TableRow>\r\n                            ))}\r\n                        </TableBody>\r\n                    </Table>\r\n                </TableContainer>\r\n            )}\r\n\r\n            {selectedModel && (\r\n                <Box>\r\n                    <Typography variant=\"h6\" gutterBottom>Detailed Analysis for: {selectedModel.model_name}</Typography>\r\n                    {loadingDetails ? (\r\n                        <CircularProgress />\r\n                    ) : (\r\n                        <Grid container spacing={3}>\r\n                            {/* Score Distribution Plot */}\r\n                            <Grid item xs={12} md={12}> {/* Changed md={6} to md={12} */}\r\n                                {/* Removed Paper component */}\r\n                                <Typography variant=\"subtitle1\" gutterBottom>Score Distribution</Typography>\r\n                                {(scoreDistribution.gt_scores.length > 0 || scoreDistribution.non_gt_scores.length > 0) ? (\r\n                                    <Plot\r\n                                        data={plotData}\r\n                                        layout={plotLayout}\r\n                                        useResizeHandler={true}\r\n                                        style={{ width: '100%', height: '400px', minWidth: '800px' }} // Added minWidth\r\n                                        config={{ responsive: true }}\r\n                                    />\r\n                                ) : (\r\n                                    <Alert severity=\"info\">Score distribution data not available for this model.</Alert>\r\n                                )}\r\n                            </Grid> {/* Added padding sx={{ p: 2 }} to the Grid item */}\r\n\r\n                            {/* Threshold Analysis & Confusion Matrix */}\r\n                            <Grid item xs={12} md={12}> {/* Changed md={6} to md={12} */}\r\n                                <Paper elevation={3} sx={{ p: 2 }}>\r\n                                    <Typography variant=\"subtitle1\" gutterBottom>Threshold Analysis</Typography>\r\n                                    <Box sx={{ width: '90%', margin: 'auto', mt: 2, mb: 4 }}>\r\n                                        <Typography id=\"threshold-slider-label\" gutterBottom>\r\n                                            Similarity Threshold: {threshold.toFixed(2)}\r\n                                        </Typography>\r\n                                        <Slider\r\n                                            aria-labelledby=\"threshold-slider-label\"\r\n                                            value={threshold}\r\n                                            onChange={handleThresholdChange}\r\n                                            onChangeCommitted={handleSliderChangeCommitted}\r\n                                            step={0.01}\r\n                                            min={0}\r\n                                            max={1}\r\n                                            valueLabelDisplay=\"auto\"\r\n                                        />\r\n                                    </Box>\r\n\r\n                                    <Typography variant=\"subtitle1\" gutterBottom sx={{ mt: 2 }}>Confusion Matrix (Threshold: {threshold.toFixed(2)})</Typography>\r\n                                    <TableContainer component={Paper} variant=\"outlined\">\r\n                                        <Table size=\"small\" aria-label=\"confusion matrix\">\r\n                                            <TableHead>\r\n                                                <TableRow>\r\n                                                    <TableCell></TableCell>\r\n                                                    <TableCell align=\"center\"><b>Predicted: Match</b></TableCell>\r\n                                                    <TableCell align=\"center\"><b>Predicted: No Match</b></TableCell>\r\n                                                </TableRow>\r\n                                            </TableHead>\r\n                                            <TableBody>\r\n                                                <TableRow>\r\n                                                    <TableCell component=\"th\" scope=\"row\"><b>Actual: Match</b></TableCell>\r\n                                                    <TableCell align=\"center\" sx={{ backgroundColor: 'rgba(0, 128, 0, 0.1)' }}>\r\n                                                        <Typography variant=\"h6\">{confusionMatrix.tp}</Typography>\r\n                                                        <Typography variant=\"caption\">(True Positives)</Typography>\r\n                                                    </TableCell>\r\n                                                    <TableCell align=\"center\" sx={{ backgroundColor: 'rgba(255, 165, 0, 0.1)' }}>\r\n                                                        <Typography variant=\"h6\">{confusionMatrix.fn}</Typography>\r\n                                                        <Typography variant=\"caption\">(False Negatives)</Typography>\r\n                                                    </TableCell>\r\n                                                </TableRow>\r\n                                                <TableRow>\r\n                                                    <TableCell component=\"th\" scope=\"row\"><b>Actual: No Match</b></TableCell>\r\n                                                    <TableCell align=\"center\" sx={{ backgroundColor: 'rgba(255, 0, 0, 0.1)' }}>\r\n                                                        <Typography variant=\"h6\">{confusionMatrix.fp}</Typography>\r\n                                                        <Typography variant=\"caption\">(False Positives)</Typography>\r\n                                                    </TableCell>\r\n                                                    <TableCell align=\"center\" sx={{ backgroundColor: 'rgba(0, 0, 255, 0.1)' }}>\r\n                                                        <Typography variant=\"h6\">{confusionMatrix.tn}</Typography>\r\n                                                        <Typography variant=\"caption\">(True Negatives)</Typography>\r\n                                                    </TableCell>\r\n                                                </TableRow>\r\n                                            </TableBody>\r\n                                        </Table>\r\n                                    </TableContainer>\r\n                                    <Typography variant=\"caption\" display=\"block\" sx={{ mt: 1 }}>\r\n                                        TP: Correctly identified matches. FN: Actual matches missed. FP: Incorrectly identified as matches. TN: Correctly identified non-matches.\r\n                                    </Typography>\r\n                                </Paper>\r\n                            </Grid>\r\n                        </Grid>\r\n                    )}\r\n                </Box>\r\n            )}\r\n        </Box>\r\n    );\r\n};\r\n\r\nexport default MetricsDisplay;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,OAAOC,IAAI,MAAM,iBAAiB;AAClC,SACIC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAEC,cAAc,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,KAAK,EACvEC,UAAU,EAAEC,gBAAgB,EAAEC,KAAK,EAAEC,GAAG,EAAEC,MAAM,EAAEC,IAAI,EAAEC,IAAI,QACzD,eAAe;AACtB,SAASC,qBAAqB,EAAEC,oBAAoB,EAAEC,kBAAkB,QAAQ,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErH,MAAMC,cAAc,GAAGA,CAAC;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EACvC,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAAC4B,aAAa,EAAEC,gBAAgB,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC8B,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/B,QAAQ,CAAC;IAAEgC,SAAS,EAAE,EAAE;IAAEC,aAAa,EAAE;EAAG,CAAC,CAAC;EAChG,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGnC,QAAQ,CAAC;IAAEoC,EAAE,EAAE,CAAC;IAAEC,EAAE,EAAE,CAAC;IAAEC,EAAE,EAAE,CAAC;IAAEC,EAAE,EAAE;EAAE,CAAC,CAAC;EACtF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGzC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC0C,cAAc,EAAEC,iBAAiB,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC4C,cAAc,EAAEC,iBAAiB,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC8C,KAAK,EAAEC,QAAQ,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EAExC,MAAMgD,iBAAiB,GAAGxB,UAAU,CAACyB,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG1B,UAAU,CAAC2B,KAAK,CAAC,CAAC,CAAC;;EAElF;EACAlD,SAAS,CAAC,MAAM;IACZ,MAAMmD,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC7BT,iBAAiB,CAAC,IAAI,CAAC;MACvBI,QAAQ,CAAC,IAAI,CAAC;MACdlB,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;MACxBF,qBAAqB,CAAC,EAAE,CAAC;MACzB,IAAI;QACA,MAAM0B,QAAQ,GAAG,MAAMnC,qBAAqB,CAACM,UAAU,CAAC;QACxD;QACA,MAAM8B,UAAU,GAAGD,QAAQ,CAACE,IAAI,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,kBAAkB,GAAGD,CAAC,CAACC,kBAAkB,CAAC;QAC5FhC,qBAAqB,CAAC2B,UAAU,CAAC;MACrC,CAAC,CAAC,OAAOM,GAAG,EAAE;QACVC,OAAO,CAACf,KAAK,CAAC,0CAA0CtB,UAAU,GAAG,EAAEoC,GAAG,CAAC;QAC3Eb,QAAQ,CAAC,0CAA0CC,iBAAiB,GAAG,CAAC;MAC5E,CAAC,SAAS;QACNL,iBAAiB,CAAC,KAAK,CAAC;MAC5B;IACJ,CAAC;IACDS,YAAY,CAAC,CAAC;EAClB,CAAC,EAAE,CAAC5B,UAAU,EAAEwB,iBAAiB,CAAC,CAAC,CAAC,CAAC;;EAErC;EACA/C,SAAS,CAAC,MAAM;IACZ,IAAI,CAAC2B,aAAa,EAAE;MAChBG,oBAAoB,CAAC;QAAEC,SAAS,EAAE,EAAE;QAAEC,aAAa,EAAE;MAAG,CAAC,CAAC;MAC1D;IACJ;IACA,MAAM6B,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC5BjB,iBAAiB,CAAC,IAAI,CAAC;MACvBE,QAAQ,CAAC,IAAI,CAAC;MACd,IAAI;QACA;QACA,MAAMM,QAAQ,GAAG,MAAMlC,oBAAoB,CAACS,aAAa,CAACmC,QAAQ,EAAEvC,UAAU,CAAC;QAC/EqC,OAAO,CAACG,GAAG,CAAC,mCAAmC,EAAEX,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC;QACjExB,oBAAoB,CAACsB,QAAQ,CAACE,IAAI,CAAC;MACvC,CAAC,CAAC,OAAOK,GAAG,EAAE;QACVC,OAAO,CAACf,KAAK,CAAC,+CAA+ClB,aAAa,CAACmC,QAAQ,GAAG,EAAEH,GAAG,CAAC;QAC5Fb,QAAQ,CAAC,yCAAyCnB,aAAa,CAACqC,UAAU,GAAG,CAAC;QAC9ElC,oBAAoB,CAAC;UAAEC,SAAS,EAAE,EAAE;UAAEC,aAAa,EAAE;QAAG,CAAC,CAAC,CAAC,CAAC;MAChE,CAAC,SAAS;QACNY,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;MAC9B;IACJ,CAAC;IACDiB,WAAW,CAAC,CAAC;EACjB,CAAC,EAAE,CAAClC,aAAa,EAAEJ,UAAU,CAAC,CAAC,CAAC,CAAC;;EAEjC;EACA,MAAM0C,WAAW,GAAGhE,WAAW,CAAC,MAAOiE,gBAAgB,IAAK;IACxD,IAAI,CAACvC,aAAa,EAAE;MAChBO,kBAAkB,CAAC;QAAEC,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAC,CAAC;MAClD;IACJ;IACA;IACA,IAAI,CAACK,cAAc,EAAEC,iBAAiB,CAAC,IAAI,CAAC;IAC5C;IACA;IACA,IAAI;MACA;MACA,MAAMQ,QAAQ,GAAG,MAAMjC,kBAAkB,CAACQ,aAAa,CAACmC,QAAQ,EAAEvC,UAAU,EAAE2C,gBAAgB,CAAC;MAC/FhC,kBAAkB,CAACkB,QAAQ,CAACE,IAAI,CAAC;IACrC,CAAC,CAAC,OAAOK,GAAG,EAAE;MACVC,OAAO,CAACf,KAAK,CAAC,6CAA6ClB,aAAa,CAACmC,QAAQ,iBAAiBI,gBAAgB,GAAG,EAAEP,GAAG,CAAC;MAC3Hb,QAAQ,CAAC,uCAAuCnB,aAAa,CAACqC,UAAU,GAAG,CAAC;MAC5E9B,kBAAkB,CAAC;QAAEC,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAC,CAAC,CAAC,CAAC;IACxD,CAAC,SAAS;MACNM,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;IAC9B;EACJ,CAAC,EAAE,CAACjB,aAAa,EAAEJ,UAAU,CAAC,CAAC,CAAC,CAAC;;EAEjCvB,SAAS,CAAC,MAAM;IACZiE,WAAW,CAAC1B,SAAS,CAAC;EAC1B,CAAC,EAAE,CAACZ,aAAa,EAAEY,SAAS,EAAE0B,WAAW,CAAC,CAAC,CAAC,CAAC;;EAG7C,MAAME,iBAAiB,GAAIC,KAAK,IAAK;IACjCxC,gBAAgB,CAACwC,KAAK,CAAC;IACvB5B,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;IACnBM,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EACpB,CAAC;EAED,MAAMuB,qBAAqB,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC/C/B,YAAY,CAAC+B,QAAQ,CAAC;IACtB;EACJ,CAAC;EAED,MAAMC,2BAA2B,GAAGA,CAACF,KAAK,EAAEC,QAAQ,KAAK;IACrD;IACA;EAAA,CACH;;EAED;EACA,MAAME,QAAQ,GAAG,CACb;IACIC,CAAC,EAAE7C,iBAAiB,CAACG,aAAa;IAClC2C,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,aAAa;IACnBC,OAAO,EAAE,GAAG;IACZC,MAAM,EAAE;MAAEC,KAAK,EAAE;IAAM,CAAC;IACxBC,MAAM,EAAE;EACZ,CAAC,EACD;IACIN,CAAC,EAAE7C,iBAAiB,CAACE,SAAS;IAC9B4C,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAE,sBAAsB;IAC5BC,OAAO,EAAE,GAAG;IACZC,MAAM,EAAE;MAAEC,KAAK,EAAE;IAAQ,CAAC;IAC1BC,MAAM,EAAE;EACZ,CAAC,CACJ;EAED,MAAMC,UAAU,GAAG;IACfC,KAAK,EAAE,0BAA0B,CAAAvD,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEqC,UAAU,KAAI,gBAAgB,EAAE;IAChFmB,KAAK,EAAE;MAAED,KAAK,EAAE,kBAAkB;MAAEE,SAAS,EAAE;IAAG,CAAC;IAAE;IACrDC,KAAK,EAAE;MAAEH,KAAK,EAAE,WAAW;MAAEP,IAAI,EAAE;IAAM,CAAC;IAAE;IAC5CW,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE;MAAEb,CAAC,EAAE,GAAG;MAAEc,CAAC,EAAE;IAAE,CAAC;IACxBC,MAAM,EAAE;MAAEC,CAAC,EAAE,EAAE;MAAEC,CAAC,EAAE,EAAE;MAAEC,CAAC,EAAE,EAAE;MAAEnC,CAAC,EAAE;IAAG;EACzC,CAAC;EAED,oBACIpC,OAAA,CAACR,GAAG;IAACgF,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAC,QAAA,gBACd1E,OAAA,CAACX,UAAU;MAACsF,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,GAAEhD,iBAAiB,EAAC,sBAAoB;IAAA;MAAAmD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,EAEzFxD,KAAK,iBAAIxB,OAAA,CAACT,KAAK;MAAC0F,QAAQ,EAAC,OAAO;MAACT,EAAE,EAAE;QAAEU,EAAE,EAAE;MAAE,CAAE;MAAAR,QAAA,EAAElD;IAAK;MAAAqD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAEhEhF,OAAA,CAACX,UAAU;MAACsF,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAF,QAAA,EAAC;IAAyB;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,EAC3E5D,cAAc,gBACXpB,OAAA,CAACV,gBAAgB;MAAAuF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,GACpB5E,kBAAkB,CAAC+E,MAAM,KAAK,CAAC,IAAI,CAAC3D,KAAK,gBACxCxB,OAAA,CAACT,KAAK;MAAC0F,QAAQ,EAAC,MAAM;MAAAP,QAAA,GAAC,oCAAkC,EAAChD,iBAAiB,EAAC,0BAAwB;IAAA;MAAAmD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,gBAE7GhF,OAAA,CAACf,cAAc;MAACmG,SAAS,EAAEhG,KAAM;MAACoF,EAAE,EAAE;QAAEU,EAAE,EAAE;MAAE,CAAE;MAAAR,QAAA,eAC5C1E,OAAA,CAAClB,KAAK;QAAC,cAAY,GAAGoB,UAAU,4BAA6B;QAAAwE,QAAA,gBACzD1E,OAAA,CAACd,SAAS;UAAAwF,QAAA,eACN1E,OAAA,CAACb,QAAQ;YAAAuF,QAAA,gBACL1E,OAAA,CAAChB,SAAS;cAAA0F,QAAA,EAAC;YAAU;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACjChF,OAAA,CAAChB,SAAS;cAACqG,KAAK,EAAC,OAAO;cAAAX,QAAA,EAAC;YAAmB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,EAEvD5E,kBAAkB,CAACkF,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,cAAc,CAAC,cAAc,CAAC,CAAC,iBAAIxF,OAAA,CAAChB,SAAS;cAACqG,KAAK,EAAC,OAAO;cAAAX,QAAA,EAAC;YAAa;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,EACpH5E,kBAAkB,CAACkF,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,cAAc,CAAC,kBAAkB,CAAC,CAAC,iBAAIxF,OAAA,CAAChB,SAAS;cAACqG,KAAK,EAAC,OAAO;cAAAX,QAAA,EAAC;YAAiB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACZhF,OAAA,CAACjB,SAAS;UAAA2F,QAAA,EACLtE,kBAAkB,CAACqF,GAAG,CAAE1C,KAAK;YAAA,IAAA2C,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA;YAAA,oBAC1B/F,OAAA,CAACb,QAAQ;cAEL6G,KAAK;cACLC,OAAO,EAAEA,CAAA,KAAMnD,iBAAiB,CAACC,KAAK,CAAE;cACxCmD,KAAK,EAAE;gBAAEC,MAAM,EAAE;cAAU,CAAE;cAC7BC,QAAQ,EAAE,CAAA9F,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEmC,QAAQ,MAAKM,KAAK,CAACN,QAAS;cAAAiC,QAAA,gBAErD1E,OAAA,CAAChB,SAAS;gBAACoG,SAAS,EAAC,IAAI;gBAACiB,KAAK,EAAC,KAAK;gBAAA3B,QAAA,gBACjC1E,OAAA,CAACL,IAAI;kBAACyF,SAAS,EAAC,QAAQ;kBAACT,OAAO,EAAC,OAAO;kBAACsB,OAAO,EAAGK,CAAC,IAAK;oBAAEA,CAAC,CAACC,eAAe,CAAC,CAAC;oBAAEzD,iBAAiB,CAACC,KAAK,CAAC;kBAAE,CAAE;kBAAA2B,QAAA,EACvG3B,KAAK,CAACJ;gBAAU;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eACPhF,OAAA,CAACX,UAAU;kBAACsF,OAAO,EAAC,SAAS;kBAAC6B,OAAO,EAAC,OAAO;kBAAC9C,KAAK,EAAC,eAAe;kBAAAgB,QAAA,GAAC,MAC5D,EAAC3B,KAAK,CAACN,QAAQ;gBAAA;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACZhF,OAAA,CAAChB,SAAS;gBAACqG,KAAK,EAAC,OAAO;gBAAAX,QAAA,GAAAgB,qBAAA,IAAAC,sBAAA,GAAE5C,KAAK,CAACV,kBAAkB,cAAAsD,sBAAA,uBAAxBA,sBAAA,CAA0Bc,OAAO,CAAC,CAAC,CAAC,cAAAf,qBAAA,cAAAA,qBAAA,GAAI;cAAK;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EAEnF5E,kBAAkB,CAACkF,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,cAAc,CAAC,cAAc,CAAC,CAAC,iBAC3DxF,OAAA,CAAChB,SAAS;gBAACqG,KAAK,EAAC,OAAO;gBAAAX,QAAA,GAAAkB,qBAAA,IAAAC,mBAAA,GAAE9C,KAAK,CAAC2D,YAAY,cAAAb,mBAAA,uBAAlBA,mBAAA,CAAoBY,OAAO,CAAC,CAAC,CAAC,cAAAb,qBAAA,cAAAA,qBAAA,GAAI;cAAK;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,EAEjF5E,kBAAkB,CAACkF,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,cAAc,CAAC,kBAAkB,CAAC,CAAC,iBAC/DxF,OAAA,CAAChB,SAAS;gBAACqG,KAAK,EAAC,OAAO;gBAAAX,QAAA,GAAAoB,qBAAA,IAAAC,sBAAA,GAAEhD,KAAK,CAAC4D,gBAAgB,cAAAZ,sBAAA,uBAAtBA,sBAAA,CAAwBU,OAAO,CAAC,CAAC,CAAC,cAAAX,qBAAA,cAAAA,qBAAA,GAAI;cAAK;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA,GApBjFjC,KAAK,CAACN,QAAQ;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsBb,CAAC;UAAA,CACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACnB,EAEA1E,aAAa,iBACVN,OAAA,CAACR,GAAG;MAAAkF,QAAA,gBACA1E,OAAA,CAACX,UAAU;QAACsF,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,GAAC,yBAAuB,EAACpE,aAAa,CAACqC,UAAU;MAAA;QAAAkC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,EACnG1D,cAAc,gBACXtB,OAAA,CAACV,gBAAgB;QAAAuF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAEpBhF,OAAA,CAACN,IAAI;QAACkH,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAnC,QAAA,gBAEvB1E,OAAA,CAACN,IAAI;UAACoH,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAAAtC,QAAA,GAAC,GAAC,eAExB1E,OAAA,CAACX,UAAU;YAACsF,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAF,QAAA,EAAC;UAAkB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EAC1ExE,iBAAiB,CAACE,SAAS,CAACyE,MAAM,GAAG,CAAC,IAAI3E,iBAAiB,CAACG,aAAa,CAACwE,MAAM,GAAG,CAAC,gBAClFnF,OAAA,CAACnB,IAAI;YACDoD,IAAI,EAAEmB,QAAS;YACf6D,MAAM,EAAErD,UAAW;YACnBsD,gBAAgB,EAAE,IAAK;YACvBhB,KAAK,EAAE;cAAEiB,KAAK,EAAE,MAAM;cAAEC,MAAM,EAAE,OAAO;cAAEC,QAAQ,EAAE;YAAQ,CAAE,CAAC;YAAA;YAC9DC,MAAM,EAAE;cAAEC,UAAU,EAAE;YAAK;UAAE;YAAA1C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,gBAEFhF,OAAA,CAACT,KAAK;YAAC0F,QAAQ,EAAC,MAAM;YAAAP,QAAA,EAAC;UAAqD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CACtF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,KAAC,eAGRhF,OAAA,CAACN,IAAI;UAACoH,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAAAtC,QAAA,GAAC,GAAC,eACxB1E,OAAA,CAACZ,KAAK;YAACoI,SAAS,EAAE,CAAE;YAAChD,EAAE,EAAE;cAAEC,CAAC,EAAE;YAAE,CAAE;YAAAC,QAAA,gBAC9B1E,OAAA,CAACX,UAAU;cAACsF,OAAO,EAAC,WAAW;cAACC,YAAY;cAAAF,QAAA,EAAC;YAAkB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC5EhF,OAAA,CAACR,GAAG;cAACgF,EAAE,EAAE;gBAAE2C,KAAK,EAAE,KAAK;gBAAE/C,MAAM,EAAE,MAAM;gBAAEqD,EAAE,EAAE,CAAC;gBAAEvC,EAAE,EAAE;cAAE,CAAE;cAAAR,QAAA,gBACpD1E,OAAA,CAACX,UAAU;gBAACqI,EAAE,EAAC,wBAAwB;gBAAC9C,YAAY;gBAAAF,QAAA,GAAC,wBAC3B,EAACxD,SAAS,CAACuF,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACbhF,OAAA,CAACP,MAAM;gBACH,mBAAgB,wBAAwB;gBACxCkI,KAAK,EAAEzG,SAAU;gBACjB0G,QAAQ,EAAE5E,qBAAsB;gBAChC6E,iBAAiB,EAAE1E,2BAA4B;gBAC/C2E,IAAI,EAAE,IAAK;gBACXC,GAAG,EAAE,CAAE;gBACPC,GAAG,EAAE,CAAE;gBACPC,iBAAiB,EAAC;cAAM;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENhF,OAAA,CAACX,UAAU;cAACsF,OAAO,EAAC,WAAW;cAACC,YAAY;cAACJ,EAAE,EAAE;gBAAEiD,EAAE,EAAE;cAAE,CAAE;cAAA/C,QAAA,GAAC,+BAA6B,EAACxD,SAAS,CAACuF,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;YAAA;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAC7HhF,OAAA,CAACf,cAAc;cAACmG,SAAS,EAAEhG,KAAM;cAACuF,OAAO,EAAC,UAAU;cAAAD,QAAA,eAChD1E,OAAA,CAAClB,KAAK;gBAACoJ,IAAI,EAAC,OAAO;gBAAC,cAAW,kBAAkB;gBAAAxD,QAAA,gBAC7C1E,OAAA,CAACd,SAAS;kBAAAwF,QAAA,eACN1E,OAAA,CAACb,QAAQ;oBAAAuF,QAAA,gBACL1E,OAAA,CAAChB,SAAS;sBAAA6F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACvBhF,OAAA,CAAChB,SAAS;sBAACqG,KAAK,EAAC,QAAQ;sBAAAX,QAAA,eAAC1E,OAAA;wBAAA0E,QAAA,EAAG;sBAAgB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eAC7DhF,OAAA,CAAChB,SAAS;sBAACqG,KAAK,EAAC,QAAQ;sBAAAX,QAAA,eAAC1E,OAAA;wBAAA0E,QAAA,EAAG;sBAAmB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1D;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACZhF,OAAA,CAACjB,SAAS;kBAAA2F,QAAA,gBACN1E,OAAA,CAACb,QAAQ;oBAAAuF,QAAA,gBACL1E,OAAA,CAAChB,SAAS;sBAACoG,SAAS,EAAC,IAAI;sBAACiB,KAAK,EAAC,KAAK;sBAAA3B,QAAA,eAAC1E,OAAA;wBAAA0E,QAAA,EAAG;sBAAa;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eACtEhF,OAAA,CAAChB,SAAS;sBAACqG,KAAK,EAAC,QAAQ;sBAACb,EAAE,EAAE;wBAAE2D,eAAe,EAAE;sBAAuB,CAAE;sBAAAzD,QAAA,gBACtE1E,OAAA,CAACX,UAAU;wBAACsF,OAAO,EAAC,IAAI;wBAAAD,QAAA,EAAE9D,eAAe,CAACE;sBAAE;wBAAA+D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAa,CAAC,eAC1DhF,OAAA,CAACX,UAAU;wBAACsF,OAAO,EAAC,SAAS;wBAAAD,QAAA,EAAC;sBAAgB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpD,CAAC,eACZhF,OAAA,CAAChB,SAAS;sBAACqG,KAAK,EAAC,QAAQ;sBAACb,EAAE,EAAE;wBAAE2D,eAAe,EAAE;sBAAyB,CAAE;sBAAAzD,QAAA,gBACxE1E,OAAA,CAACX,UAAU;wBAACsF,OAAO,EAAC,IAAI;wBAAAD,QAAA,EAAE9D,eAAe,CAACG;sBAAE;wBAAA8D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAa,CAAC,eAC1DhF,OAAA,CAACX,UAAU;wBAACsF,OAAO,EAAC,SAAS;wBAAAD,QAAA,EAAC;sBAAiB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACXhF,OAAA,CAACb,QAAQ;oBAAAuF,QAAA,gBACL1E,OAAA,CAAChB,SAAS;sBAACoG,SAAS,EAAC,IAAI;sBAACiB,KAAK,EAAC,KAAK;sBAAA3B,QAAA,eAAC1E,OAAA;wBAAA0E,QAAA,EAAG;sBAAgB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC,eACzEhF,OAAA,CAAChB,SAAS;sBAACqG,KAAK,EAAC,QAAQ;sBAACb,EAAE,EAAE;wBAAE2D,eAAe,EAAE;sBAAuB,CAAE;sBAAAzD,QAAA,gBACtE1E,OAAA,CAACX,UAAU;wBAACsF,OAAO,EAAC,IAAI;wBAAAD,QAAA,EAAE9D,eAAe,CAACI;sBAAE;wBAAA6D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAa,CAAC,eAC1DhF,OAAA,CAACX,UAAU;wBAACsF,OAAO,EAAC,SAAS;wBAAAD,QAAA,EAAC;sBAAiB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrD,CAAC,eACZhF,OAAA,CAAChB,SAAS;sBAACqG,KAAK,EAAC,QAAQ;sBAACb,EAAE,EAAE;wBAAE2D,eAAe,EAAE;sBAAuB,CAAE;sBAAAzD,QAAA,gBACtE1E,OAAA,CAACX,UAAU;wBAACsF,OAAO,EAAC,IAAI;wBAAAD,QAAA,EAAE9D,eAAe,CAACK;sBAAE;wBAAA4D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAa,CAAC,eAC1DhF,OAAA,CAACX,UAAU;wBAACsF,OAAO,EAAC,SAAS;wBAAAD,QAAA,EAAC;sBAAgB;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAY,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACjBhF,OAAA,CAACX,UAAU;cAACsF,OAAO,EAAC,SAAS;cAAC6B,OAAO,EAAC,OAAO;cAAChC,EAAE,EAAE;gBAAEiD,EAAE,EAAE;cAAE,CAAE;cAAA/C,QAAA,EAAC;YAE7D;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAC7E,EAAA,CAtRIF,cAAc;AAAAmI,EAAA,GAAdnI,cAAc;AAwRpB,eAAeA,cAAc;AAAC,IAAAmI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}