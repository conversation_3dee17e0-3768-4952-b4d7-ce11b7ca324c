from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, Text
from sqlalchemy.orm import relationship
import datetime
from backend.extensions import db # Import db instance

class BoundingBoxModels(db.Model):
    __tablename__ = 'bounding_box_models'
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, index=True, nullable=False)
    description = Column(Text, nullable=True)
    gcp_model_name = Column(String(255), nullable=True) # Name of the model on GCP/Vertex AI
    is_active = Column(db.Boolean, default=True, nullable=False)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    experiments = relationship("BoundingBoxExperiments", back_populates="model")

class BoundingBoxPictures(db.Model):
    __tablename__ = 'bounding_box_pictures'
    id = Column(Integer, primary_key=True, index=True)
    filename = Column(String, unique=True, index=True, nullable=False)
    # image_data is large, consider if it's always needed or if a path/URL is better
    image_data = Column(Text, nullable=True) # Base64 encoded image - make nullable if path is primary
    file_path = Column(String, nullable=False, unique=True) # Path to the actual image file
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    experiments = relationship("BoundingBoxExperiments", back_populates="picture")

class BoundingBoxExperiments(db.Model):
    __tablename__ = 'bounding_box_experiments'
    id = Column(Integer, primary_key=True, index=True)
    model_id = Column(Integer, ForeignKey('bounding_box_models.id'), nullable=False)
    picture_id = Column(Integer, ForeignKey('bounding_box_pictures.id'), nullable=False)
    prompt = Column(Text, nullable=False)
    resize_height = Column(Integer, nullable=False)
    resize_width = Column(Integer, nullable=False)
    output_type = Column(String, nullable=False) # e.g. 'grayscale', 'color_corrected'
    # Removed 'configuration' as individual fields are now present
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    model = relationship("BoundingBoxModels", back_populates="experiments")
    picture = relationship("BoundingBoxPictures", back_populates="experiments")
    results = relationship("BoundingBoxResults", back_populates="experiment") # Potentially multiple results per experiment if multiple models run

class BoundingBoxResults(db.Model):
    __tablename__ = 'bounding_box_results'
    id = Column(Integer, primary_key=True, index=True)
    experiment_id = Column(Integer, ForeignKey('bounding_box_experiments.id'), nullable=False)
    model_id = Column(Integer, ForeignKey('bounding_box_models.id'), nullable=False)
    status = Column(String(50), default='pending', nullable=False) # pending, processing, success, failed
    output_image_path = Column(String(1024), nullable=True) # Path to the generated image
    score = Column(Integer, nullable=True) # Score from 0-10
    bounding_boxes = Column(Text, nullable=True) # JSON string for bounding boxes
    inference_time = Column(Float, nullable=True)
    error_message = Column(Text, nullable=True) # To store error messages from task failures
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    experiment = relationship("BoundingBoxExperiments", back_populates="results")
    model = relationship("BoundingBoxModels")
